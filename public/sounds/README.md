# Notification Sounds

This directory contains audio files for the restaurant management system notifications.

## Required Sound Files

1. **new-order.mp3** - Played when a new order is received
   - Should be a pleasant, attention-grabbing sound (like a gentle chime or bell)
   - Duration: 1-3 seconds
   - Volume: Moderate (not too loud or jarring)

2. **status-update.mp3** - Played when an order status is updated
   - Should be a subtle notification sound (like a soft ding)
   - Duration: 0.5-2 seconds
   - Volume: Softer than new-order sound

3. **error.mp3** - Played when an error occurs
   - Should be a distinctive but not alarming sound
   - Duration: 1-2 seconds
   - Volume: Moderate

## Sound Requirements

- Format: MP3 (for best browser compatibility)
- Sample Rate: 44.1 kHz or 48 kHz
- Bit Rate: 128-320 kbps
- Mono or Stereo (mono preferred for smaller file size)

## Recommended Sources

- Free sounds: freesound.org, zapsplat.com
- Royalty-free: audiojungle.net, pond5.com
- Generate your own: audacity.org, online tone generators

## Browser Compatibility

The audio notification system includes fallbacks:
1. Primary: MP3 file playback
2. Fallback: Programmatically generated beep sound
3. Graceful degradation: Silent operation if audio fails

## Volume Control

Users can toggle sound notifications on/off through the notification dropdown in the manager interface.
