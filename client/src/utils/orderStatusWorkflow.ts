// Order Status Workflow System
// Defines valid status transitions and workflow logic for different order types

export const ORDER_STATUSES = {
  // Initial status
  CONFIRMED: 'confirmed',
  
  // Kitchen workflow
  PREPARING: 'preparing',
  
  // Ready states (different for delivery vs takeaway)
  READY_FOR_PICKUP: 'ready_for_pickup',
  READY_FOR_DELIVERY: 'ready_for_delivery',
  
  // Driver workflow (delivery only)
  WITH_DRIVER: 'with_driver',
  ON_THE_WAY: 'on_the_way',
  
  // Final states
  DELIVERED: 'delivered',
  COMPLETED: 'completed',
  CANCELLED: 'cancelled'
} as const;

export const ORDER_TYPES = {
  DELIVERY: 'delivery',
  TAKEAWAY: 'takeaway'
} as const;

export type OrderStatus = typeof ORDER_STATUSES[keyof typeof ORDER_STATUSES];
export type OrderType = typeof ORDER_TYPES[keyof typeof ORDER_TYPES];

// Define valid status transitions for each order type
export const STATUS_WORKFLOWS = {
  [ORDER_TYPES.DELIVERY]: {
    [ORDER_STATUSES.CONFIRMED]: [ORDER_STATUSES.PREPARING],
    [ORDER_STATUSES.PREPARING]: [ORDER_STATUSES.READY_FOR_DELIVERY],
    [ORDER_STATUSES.READY_FOR_DELIVERY]: [ORDER_STATUSES.WITH_DRIVER],
    [ORDER_STATUSES.WITH_DRIVER]: [ORDER_STATUSES.ON_THE_WAY],
    [ORDER_STATUSES.ON_THE_WAY]: [ORDER_STATUSES.DELIVERED],
    [ORDER_STATUSES.DELIVERED]: [ORDER_STATUSES.COMPLETED],
  },
  [ORDER_TYPES.TAKEAWAY]: {
    [ORDER_STATUSES.CONFIRMED]: [ORDER_STATUSES.PREPARING],
    [ORDER_STATUSES.PREPARING]: [ORDER_STATUSES.READY_FOR_PICKUP],
    [ORDER_STATUSES.READY_FOR_PICKUP]: [ORDER_STATUSES.COMPLETED],
  }
} as const;

// Status display information
export const STATUS_INFO = {
  [ORDER_STATUSES.CONFIRMED]: {
    label: 'Order Confirmed',
    description: 'Your order has been received and confirmed',
    color: 'blue',
    icon: 'bell'
  },
  [ORDER_STATUSES.PREPARING]: {
    label: 'Preparing',
    description: 'Your order is being prepared in the kitchen',
    color: 'orange',
    icon: 'flame'
  },
  [ORDER_STATUSES.READY_FOR_PICKUP]: {
    label: 'Ready for Pickup',
    description: 'Your order is ready for collection',
    color: 'green',
    icon: 'package'
  },
  [ORDER_STATUSES.READY_FOR_DELIVERY]: {
    label: 'Ready for Delivery',
    description: 'Your order is ready and waiting for a driver',
    color: 'green',
    icon: 'truck'
  },
  [ORDER_STATUSES.WITH_DRIVER]: {
    label: 'With Driver',
    description: 'A driver has collected your order',
    color: 'cyan',
    icon: 'user'
  },
  [ORDER_STATUSES.ON_THE_WAY]: {
    label: 'On the Way',
    description: 'Your order is on its way to you',
    color: 'purple',
    icon: 'truck'
  },
  [ORDER_STATUSES.DELIVERED]: {
    label: 'Delivered',
    description: 'Your order has been delivered',
    color: 'green',
    icon: 'check'
  },
  [ORDER_STATUSES.COMPLETED]: {
    label: 'Completed',
    description: 'Order completed successfully',
    color: 'teal',
    icon: 'check-circle'
  },
  [ORDER_STATUSES.CANCELLED]: {
    label: 'Cancelled',
    description: 'Order has been cancelled',
    color: 'red',
    icon: 'x'
  }
} as const;

// Workflow validation functions
export function isValidStatusTransition(
  currentStatus: OrderStatus,
  newStatus: OrderStatus,
  orderType: OrderType
): boolean {
  const workflow = STATUS_WORKFLOWS[orderType];
  const validNextStatuses = workflow[currentStatus as keyof typeof workflow];
  return validNextStatuses?.includes(newStatus as any) || false;
}

export function getNextValidStatuses(
  currentStatus: OrderStatus,
  orderType: OrderType
): OrderStatus[] {
  const workflow = STATUS_WORKFLOWS[orderType];
  return (workflow[currentStatus as keyof typeof workflow] as OrderStatus[]) || [];
}

export function getNextStatus(
  currentStatus: OrderStatus,
  orderType: OrderType
): OrderStatus | null {
  const validNextStatuses = getNextValidStatuses(currentStatus, orderType);
  return validNextStatuses.length > 0 ? validNextStatuses[0] : null;
}

// Check if status requires driver assignment
export function requiresDriverAssignment(status: OrderStatus): boolean {
  return status === ORDER_STATUSES.READY_FOR_DELIVERY;
}

// Check if order should be visible to drivers
export function isDriverOrder(status: OrderStatus, orderType: OrderType): boolean {
  return orderType === ORDER_TYPES.DELIVERY && [
    ORDER_STATUSES.READY_FOR_DELIVERY,
    ORDER_STATUSES.WITH_DRIVER,
    ORDER_STATUSES.ON_THE_WAY
  ].includes(status);
}

// Check if order should be visible to managers
export function isManagerOrder(status: OrderStatus): boolean {
  return [
    ORDER_STATUSES.CONFIRMED,
    ORDER_STATUSES.PREPARING,
    ORDER_STATUSES.READY_FOR_PICKUP,
    ORDER_STATUSES.READY_FOR_DELIVERY
  ].includes(status);
}

// Get status timeline for order tracker
export function getStatusTimeline(orderType: OrderType): OrderStatus[] {
  if (orderType === ORDER_TYPES.DELIVERY) {
    return [
      ORDER_STATUSES.CONFIRMED,
      ORDER_STATUSES.PREPARING,
      ORDER_STATUSES.READY_FOR_DELIVERY,
      ORDER_STATUSES.WITH_DRIVER,
      ORDER_STATUSES.ON_THE_WAY,
      ORDER_STATUSES.DELIVERED
    ];
  } else {
    return [
      ORDER_STATUSES.CONFIRMED,
      ORDER_STATUSES.PREPARING,
      ORDER_STATUSES.READY_FOR_PICKUP
    ];
  }
}

// Get user-friendly status label
export function getStatusLabel(status: OrderStatus): string {
  return STATUS_INFO[status]?.label || status.replace(/_/g, ' ');
}

// Get status description
export function getStatusDescription(status: OrderStatus): string {
  return STATUS_INFO[status]?.description || '';
}

// Get status color theme
export function getStatusColor(status: OrderStatus): string {
  return STATUS_INFO[status]?.color || 'gray';
}
