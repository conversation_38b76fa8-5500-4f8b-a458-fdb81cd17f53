import { useState } from "react";
import { useLocation } from "wouter";
import { motion } from "framer-motion";
import Button from "@/components/Button";

const OrderLookup = () => {
  const [, setLocation] = useLocation();
  const [orderId, setOrderId] = useState("");
  const [isSearching, setIsSearching] = useState(false);

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!orderId.trim()) {
      return;
    }

    setIsSearching(true);
    
    // Validate order ID format (should be a number)
    const numericOrderId = parseInt(orderId.trim());
    if (isNaN(numericOrderId)) {
      alert("Please enter a valid order ID (numbers only)");
      setIsSearching(false);
      return;
    }

    // Check if order exists
    try {
      const response = await fetch(`/api/orders/${numericOrderId}`);
      if (response.ok) {
        // Order exists, redirect to tracking page
        setLocation(`/track-order/${numericOrderId}`);
      } else {
        alert("Order not found. Please check your order ID and try again.");
        setIsSearching(false);
      }
    } catch (error) {
      console.error("Error checking order:", error);
      alert("Error checking order. Please try again.");
      setIsSearching(false);
    }
  };

  return (
    <main className="min-h-screen bg-black relative overflow-hidden flex items-center justify-center py-12">
      {/* Animated Gradient Background */}
      <div 
        className="absolute inset-0 z-0"
        style={{
          background: "radial-gradient(circle at 20% 20%, rgba(0, 255, 255, 0.03), transparent 33%), " +
                    "radial-gradient(circle at 80% 80%, rgba(57, 255, 20, 0.03), transparent 33%), " +
                    "radial-gradient(circle at 50% 50%, rgba(255, 0, 255, 0.03), rgba(0, 0, 0, 1) 100%)",
        }}
      ></div>
      
      {/* Neon Grid Overlay */}
      <div className="absolute inset-0 z-0 opacity-[0.03]" 
           style={{ 
             backgroundImage: "linear-gradient(to right, #39FF14 1px, transparent 1px), linear-gradient(to bottom, #39FF14 1px, transparent 1px)", 
             backgroundSize: "40px 40px" 
           }}>
      </div>

      <div className="container mx-auto px-4 z-10 relative max-w-md">
        <motion.div 
          className="bg-black/30 backdrop-blur-sm rounded-2xl p-8 border border-lime-800/30
                     shadow-[0_0_30px_rgba(57,255,20,0.2)]"
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.6 }}
        >
          <div className="text-center mb-8">
            <motion.div 
              className="w-16 h-16 mx-auto mb-6 rounded-full bg-gradient-to-br from-lime-500/20 to-green-700/20 
                         flex items-center justify-center"
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ 
                type: "spring",
                stiffness: 200,
                damping: 15,
                delay: 0.2
              }}
            >
              <svg className="w-8 h-8 text-lime-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </motion.div>
            
            <h1 className="font-playfair text-2xl md:text-3xl font-bold mb-4 text-white">
              Track Your Order
            </h1>
            
            <p className="text-gray-300 mb-6">
              Enter your order ID to track your delicious BBQ order
            </p>
          </div>

          <form onSubmit={handleSearch} className="space-y-6">
            <div>
              <label htmlFor="orderId" className="block text-sm font-medium text-gray-300 mb-2">
                Order ID
              </label>
              <input
                type="text"
                id="orderId"
                value={orderId}
                onChange={(e) => setOrderId(e.target.value)}
                placeholder="Enter your order ID (e.g., 42)"
                className="w-full px-4 py-3 bg-black/50 border border-gray-700 rounded-lg 
                         text-white placeholder-gray-500 focus:outline-none focus:border-lime-500
                         focus:ring-1 focus:ring-lime-500 transition-colors"
                disabled={isSearching}
                required
              />
            </div>

            <Button
              type="submit"
              disabled={isSearching || !orderId.trim()}
              className="w-full"
            >
              {isSearching ? (
                <div className="flex items-center justify-center space-x-2">
                  <motion.div
                    className="w-4 h-4 border-2 border-white border-t-transparent rounded-full"
                    animate={{ rotate: 360 }}
                    transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                  />
                  <span>Searching...</span>
                </div>
              ) : (
                <div className="flex items-center justify-center space-x-2">
                  <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                  </svg>
                  <span>Track Order</span>
                </div>
              )}
            </Button>
          </form>

          <div className="mt-6 text-center">
            <a 
              href="/" 
              className="text-gray-400 hover:text-lime-400 transition-colors duration-300 text-sm"
            >
              ← Return to Home
            </a>
          </div>
        </motion.div>
      </div>
    </main>
  );
};

export default OrderLookup;
