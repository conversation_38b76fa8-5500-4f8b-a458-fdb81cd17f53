import { useEffect } from 'react';
import { useLocation } from 'wouter';

const LogoutPage = () => {
  const [, setLocation] = useLocation();

  useEffect(() => {
    // In a real app, this would make a call to a logout API endpoint
    // that would invalidate the session/token

    // Secure silent logout - redirect without exposing information
    setTimeout(() => {
      setLocation('/auth');
    }, 500);
  }, [setLocation]);

  return (
    <div className="min-h-screen bg-black text-white flex items-center justify-center">
      <div className="text-center">
        <h1 className="text-2xl font-bold mb-4">Logging out...</h1>
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-pink-500 mx-auto"></div>
      </div>
    </div>
  );
};

export default LogoutPage;