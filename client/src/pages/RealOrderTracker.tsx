import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { CheckCircle, Clock, Package, ChefHat, Navigation, User, Sparkles, Star } from 'lucide-react';
import { format } from 'date-fns';
import confetti from 'canvas-confetti';
import {
  ORDER_STATUSES,
  ORDER_TYPES,
  getStatusTimeline,
  getStatusLabel,
  getStatusDescription
} from '@/utils/orderStatusWorkflow';

interface OrderTrackerProps {
  params?: {
    orderId?: string;
  };
}

interface Order {
  id: number;
  status: string;
  orderDetails: {
    type: 'delivery' | 'takeaway';
    time: string;
    scheduledTime?: string;
  };
  items: Array<{
    id: number;
    name: string;
    quantity: number;
    price: number;
    description?: string;
    imageUrl?: string;
  }>;
  customer: {
    firstName: string;
    email: string;
    phone: string;
    address?: string;
    postalCode?: string;
    city?: string;
  };
  subtotal: number;
  deliveryFee: number;
  total: number;
  paymentMethod: string;
  notes?: string;
  createdAt: string;
  estimatedDeliveryTime?: string;
}

const RealOrderTracker = (props: OrderTrackerProps) => {
  const orderId = props.params?.orderId;
  const [order, setOrder] = useState<Order | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [hasTriggeredConfetti, setHasTriggeredConfetti] = useState(false);

  // Fetch order data
  useEffect(() => {
    const fetchOrder = async () => {
      if (!orderId) {
        setError('Order ID is required');
        setLoading(false);
        return;
      }

      try {
        const response = await fetch(`/api/orders/${orderId}`);
        if (!response.ok) {
          throw new Error('Order not found');
        }

        const orderData = await response.json();
        setOrder(orderData);
        setError(null);
      } catch (err) {
        console.error('Error fetching order:', err);
        setError('Failed to load order details');
      } finally {
        setLoading(false);
      }
    };

    fetchOrder();

    // Poll for updates every 30 seconds
    const interval = setInterval(fetchOrder, 30000);
    return () => clearInterval(interval);
  }, [orderId]);

  // Trigger confetti for completed orders
  useEffect(() => {
    if (order && !hasTriggeredConfetti && isOrderCompleted(order.status)) {
      triggerConfetti();
      setHasTriggeredConfetti(true);
    }
  }, [order, hasTriggeredConfetti]);

  // Helper function to check if order is completed
  const isOrderCompleted = (status: string) => {
    return status === ORDER_STATUSES.COMPLETED || status === ORDER_STATUSES.DELIVERED;
  };

  // Confetti animation
  const triggerConfetti = () => {
    const duration = 3000;
    const animationEnd = Date.now() + duration;
    const defaults = { startVelocity: 30, spread: 360, ticks: 60, zIndex: 0 };

    function randomInRange(min: number, max: number) {
      return Math.random() * (max - min) + min;
    }

    const interval = setInterval(function() {
      const timeLeft = animationEnd - Date.now();

      if (timeLeft <= 0) {
        return clearInterval(interval);
      }

      const particleCount = 50 * (timeLeft / duration);

      // since particles fall down, start a bit higher than random
      confetti(Object.assign({}, defaults, {
        particleCount,
        origin: { x: randomInRange(0.1, 0.3), y: Math.random() - 0.2 }
      }));
      confetti(Object.assign({}, defaults, {
        particleCount,
        origin: { x: randomInRange(0.7, 0.9), y: Math.random() - 0.2 }
      }));
    }, 250);
  };

  // Get status icon with special handling for completed orders
  const getStatusIcon = (status: string, isCompleted = false) => {
    if (isCompleted && (status === ORDER_STATUSES.DELIVERED || status === ORDER_STATUSES.COMPLETED)) {
      return (
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ type: "spring", stiffness: 300, damping: 20 }}
        >
          <Star className="w-6 h-6 text-yellow-400 fill-current" />
        </motion.div>
      );
    }

    switch (status) {
      case ORDER_STATUSES.CONFIRMED:
        return <CheckCircle className="w-6 h-6" />;
      case ORDER_STATUSES.PREPARING:
        return <ChefHat className="w-6 h-6" />;
      case ORDER_STATUSES.READY_FOR_PICKUP:
        return <Package className="w-6 h-6" />;
      case ORDER_STATUSES.READY_FOR_DELIVERY:
        return <Package className="w-6 h-6" />;
      case ORDER_STATUSES.WITH_DRIVER:
        return <User className="w-6 h-6" />;
      case ORDER_STATUSES.ON_THE_WAY:
        return <Navigation className="w-6 h-6" />;
      case ORDER_STATUSES.DELIVERED:
      case ORDER_STATUSES.COMPLETED:
        return (
          <motion.div
            animate={{
              scale: [1, 1.2, 1],
              rotate: [0, 360, 0]
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              repeatType: "loop" as const
            }}
          >
            <CheckCircle className="w-6 h-6" />
          </motion.div>
        );
      default:
        return <Clock className="w-6 h-6" />;
    }
  };

  // Get status color with enhanced completion styling
  const getStatusColorClass = (status: string, isActive: boolean, isCompleted: boolean) => {
    // Special styling for completed final step
    if (isCompleted && (status === ORDER_STATUSES.DELIVERED || status === ORDER_STATUSES.COMPLETED)) {
      return 'text-yellow-400 bg-gradient-to-r from-yellow-900/30 to-green-900/30 border-yellow-500 shadow-lg shadow-yellow-500/20';
    }

    if (isCompleted) return 'text-green-400 bg-green-900/20 border-green-700';

    if (isActive) {
      switch (status) {
        case ORDER_STATUSES.CONFIRMED:
          return 'text-blue-400 bg-blue-900/20 border-blue-700';
        case ORDER_STATUSES.PREPARING:
          return 'text-orange-400 bg-orange-900/20 border-orange-700';
        case ORDER_STATUSES.READY_FOR_PICKUP:
        case ORDER_STATUSES.READY_FOR_DELIVERY:
          return 'text-green-400 bg-green-900/20 border-green-700';
        case ORDER_STATUSES.WITH_DRIVER:
          return 'text-cyan-400 bg-cyan-900/20 border-cyan-700';
        case ORDER_STATUSES.ON_THE_WAY:
          return 'text-purple-400 bg-purple-900/20 border-purple-700';
        case ORDER_STATUSES.DELIVERED:
        case ORDER_STATUSES.COMPLETED:
          return 'text-yellow-400 bg-gradient-to-r from-yellow-900/30 to-green-900/30 border-yellow-500 shadow-lg shadow-yellow-500/20';
        default:
          return 'text-gray-400 bg-gray-900/20 border-gray-700';
      }
    }
    return 'text-gray-600 bg-gray-900/10 border-gray-800';
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-cyan-400 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading order details...</p>
        </div>
      </div>
    );
  }

  if (error || !order) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-400 text-6xl mb-4">⚠️</div>
          <h1 className="text-2xl font-bold text-white mb-2">Order Not Found</h1>
          <p className="text-gray-400 mb-6">{error || 'The order you are looking for does not exist.'}</p>
          <a href="/" className="text-cyan-400 hover:text-cyan-300 underline">
            Return to Home
          </a>
        </div>
      </div>
    );
  }

  const orderType = order.orderDetails.type;
  const statusTimeline = getStatusTimeline(orderType);
  const currentStatusIndex = statusTimeline.indexOf(order.status as any);
  const isCompleted = isOrderCompleted(order.status);

  // Fix for completed orders - if order is completed but not found in timeline,
  // it means it's at the final step
  const actualCurrentIndex = isCompleted && currentStatusIndex === -1
    ? statusTimeline.length - 1
    : currentStatusIndex;

  return (
    <div className="min-h-screen bg-black text-white py-12">
      {/* Background */}
      <div className="absolute inset-0 z-0 opacity-[0.02]"
           style={{
             backgroundImage: "linear-gradient(to right, #00FFFF 1px, transparent 1px), linear-gradient(to bottom, #00FFFF 1px, transparent 1px)",
             backgroundSize: "40px 40px"
           }}>
      </div>

      <div className="container mx-auto px-4 z-10 relative max-w-4xl">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-3xl md:text-4xl font-bold mb-4">
            Track Your Order
          </h1>
          <div className="space-y-2">
            <p className="text-xl">
              Order <span className="text-cyan-400 font-medium">#{order.id}</span>
            </p>
            <p className="text-gray-400">
              {orderType === ORDER_TYPES.DELIVERY ? 'Delivery' : 'Takeaway'} Order
            </p>
            <p className="text-gray-500 text-sm">
              Placed on {format(new Date(order.createdAt), 'PPp')}
            </p>
          </div>
        </div>

        {/* Current Status */}
        <div className="bg-gray-900/50 rounded-xl p-6 mb-8 border border-gray-800">
          <div className="flex items-center justify-center space-x-4">
            <motion.div
              className={`p-3 rounded-full ${getStatusColorClass(order.status, true, isCompleted)}`}
              animate={isCompleted ? {
                boxShadow: [
                  "0 0 0 rgba(255, 255, 0, 0)",
                  "0 0 20px rgba(255, 255, 0, 0.6)",
                  "0 0 0 rgba(255, 255, 0, 0)"
                ]
              } : {}}
              transition={isCompleted ? {
                duration: 2,
                repeat: Infinity,
                repeatType: "loop" as const
              } : {}}
            >
              {getStatusIcon(order.status, isCompleted)}
            </motion.div>
            <div className="text-center">
              <h2 className="text-2xl font-bold flex items-center justify-center gap-2">
                {getStatusLabel(order.status as any)}
                {isCompleted && (
                  <motion.div
                    initial={{ scale: 0, rotate: -180 }}
                    animate={{ scale: 1, rotate: 0 }}
                    transition={{ type: "spring", stiffness: 300, damping: 20, delay: 0.5 }}
                  >
                    <Sparkles className="w-6 h-6 text-yellow-400" />
                  </motion.div>
                )}
              </h2>
              <p className="text-gray-400">{getStatusDescription(order.status as any)}</p>
              {isCompleted && (
                <motion.p
                  className="text-yellow-400 font-medium mt-2"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 1 }}
                >
                  🎉 Thank you for choosing us!
                </motion.p>
              )}
            </div>
          </div>
        </div>

        {/* Horizontal Progress Timeline */}
        <div className="bg-gray-900/50 rounded-xl p-6 mb-8 border border-gray-800">
          <h3 className="text-xl font-bold mb-8 text-center">Order Progress</h3>

          {/* Horizontal Timeline Container */}
          <div className="relative">
            {/* Progress Line */}
            <div className="absolute top-1/2 left-0 right-0 h-1 bg-gray-800 rounded-full transform -translate-y-1/2 z-0">
              <motion.div
                className="h-full bg-gradient-to-r from-cyan-500 to-green-500 rounded-full"
                initial={{ width: "0%" }}
                animate={{
                  width: `${actualCurrentIndex >= 0 ?
                    (actualCurrentIndex / (statusTimeline.length - 1)) * 100 : 0}%`
                }}
                transition={{ duration: 1.5, ease: "easeInOut" }}
              />
            </div>

            {/* Timeline Steps */}
            <div className="relative z-10 flex justify-between items-center">
              {statusTimeline.map((status, index) => {
                const isActive = index === actualCurrentIndex;
                const isStepCompleted = index < actualCurrentIndex ||
                  (isCompleted && index === statusTimeline.length - 1);
                const isFinalStep = index === statusTimeline.length - 1;

                return (
                  <motion.div
                    key={status}
                    className="flex flex-col items-center space-y-3 relative"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.2 }}
                  >
                    {/* Step Circle */}
                    <motion.div
                      className={`relative p-4 rounded-full border-2 z-10 bg-black
                        ${getStatusColorClass(status, isActive, isStepCompleted)}
                        ${isFinalStep && isCompleted ? 'ring-4 ring-yellow-400/30' : ''}
                      `}
                      animate={isFinalStep && isCompleted ? {
                        scale: [1, 1.1, 1],
                        boxShadow: [
                          "0 0 0 rgba(255, 255, 0, 0)",
                          "0 0 30px rgba(255, 255, 0, 0.8)",
                          "0 0 0 rgba(255, 255, 0, 0)"
                        ]
                      } : isActive ? {
                        scale: [1, 1.05, 1]
                      } : {}}
                      transition={isFinalStep && isCompleted ? {
                        duration: 2,
                        repeat: Infinity,
                        repeatType: "loop" as const
                      } : isActive ? {
                        duration: 1.5,
                        repeat: Infinity,
                        repeatType: "reverse" as const
                      } : {}}
                    >
                      {getStatusIcon(status, isStepCompleted)}

                      {/* Completion Sparkles */}
                      {isFinalStep && isCompleted && (
                        <AnimatePresence>
                          <motion.div
                            className="absolute -top-2 -right-2"
                            initial={{ scale: 0, rotate: -180 }}
                            animate={{ scale: 1, rotate: 0 }}
                            exit={{ scale: 0, rotate: 180 }}
                            transition={{ type: "spring", stiffness: 300, damping: 20 }}
                          >
                            <Sparkles className="w-4 h-4 text-yellow-400" />
                          </motion.div>
                        </AnimatePresence>
                      )}
                    </motion.div>

                    {/* Step Label */}
                    <div className="text-center max-w-24">
                      <h4 className={`text-sm font-medium mb-1
                        ${isActive || isStepCompleted ? 'text-white' : 'text-gray-500'}
                        ${isFinalStep && isCompleted ? 'text-yellow-400' : ''}
                      `}>
                        {getStatusLabel(status)}
                      </h4>
                      <p className={`text-xs leading-tight
                        ${isActive || isStepCompleted ? 'text-gray-300' : 'text-gray-600'}
                      `}>
                        {getStatusDescription(status)}
                      </p>
                    </div>

                    {/* Step Number */}
                    <div className={`text-xs px-2 py-1 rounded-full
                      ${isActive || isStepCompleted ? 'bg-cyan-900/30 text-cyan-400' : 'bg-gray-800 text-gray-500'}
                      ${isFinalStep && isCompleted ? 'bg-yellow-900/30 text-yellow-400' : ''}
                    `}>
                      {index + 1}
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </div>

          {/* Completion Message */}
          {isCompleted && (
            <motion.div
              className="mt-8 text-center"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1.5 }}
            >
              <div className="bg-gradient-to-r from-yellow-900/20 to-green-900/20 rounded-lg p-4 border border-yellow-500/30">
                <h4 className="text-lg font-bold text-yellow-400 mb-2">
                  🎉 Order Complete!
                </h4>
                <p className="text-gray-300">
                  Your order has been successfully {orderType === ORDER_TYPES.DELIVERY ? 'delivered' : 'completed'}.
                  We hope you enjoyed your meal!
                </p>
              </div>
            </motion.div>
          )}
        </div>

        {/* Order Details */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Items */}
          <div className="bg-gray-900/50 rounded-xl p-6 border border-gray-800">
            <h3 className="text-xl font-bold mb-4">Order Items</h3>
            <div className="space-y-3">
              {order.items.map((item, index) => (
                <div key={index} className="flex justify-between items-center">
                  <div>
                    <span className="text-white">{item.name}</span>
                    <span className="text-gray-400 ml-2">x{item.quantity}</span>
                  </div>
                  <span className="text-cyan-400">${item.price.toFixed(2)}</span>
                </div>
              ))}
              <div className="border-t border-gray-700 pt-3 mt-3">
                <div className="flex justify-between items-center font-bold">
                  <span>Total</span>
                  <span className="text-green-400">${order.total.toFixed(2)}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Customer Details */}
          <div className="bg-gray-900/50 rounded-xl p-6 border border-gray-800">
            <h3 className="text-xl font-bold mb-4">
              {orderType === ORDER_TYPES.DELIVERY ? 'Delivery Details' : 'Customer Details'}
            </h3>
            <div className="space-y-3">
              <div>
                <span className="text-gray-400">Name:</span>
                <span className="text-white ml-2">{order.customer.firstName}</span>
              </div>
              <div>
                <span className="text-gray-400">Phone:</span>
                <span className="text-white ml-2">{order.customer.phone}</span>
              </div>
              {orderType === ORDER_TYPES.DELIVERY && order.customer.address && (
                <div>
                  <span className="text-gray-400">Address:</span>
                  <span className="text-white ml-2">
                    {order.customer.address}
                    {order.customer.postalCode && `, ${order.customer.postalCode}`}
                    {order.customer.city && ` ${order.customer.city}`}
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="text-center mt-8">
          <a
            href="/"
            className="inline-flex items-center px-6 py-3 bg-cyan-600 hover:bg-cyan-700 text-white rounded-lg transition-colors"
          >
            Return to Home
          </a>
        </div>
      </div>
    </div>
  );
};

export default RealOrderTracker;
