import { useCallback } from 'react';
import { useNotifications } from '@/context/NotificationContext';

interface AudioNotificationOptions {
  volume?: number;
}

export const useAudioNotification = (options: AudioNotificationOptions = {}) => {
  const { soundEnabled } = useNotifications();
  const { volume = 0.5 } = options;

  const playSystemBeep = useCallback(async (soundType: 'new_order' | 'status_update' | 'error' = 'new_order') => {
    const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);

    // Different frequencies and patterns for different notification types
    let frequency = 800;
    let duration = 0.3;

    switch (soundType) {
      case 'new_order':
        frequency = 880; // A5 note - pleasant and attention-grabbing
        duration = 0.4;
        break;
      case 'status_update':
        frequency = 660; // E5 note - softer
        duration = 0.2;
        break;
      case 'error':
        frequency = 440; // A4 note - lower, more serious
        duration = 0.5;
        break;
    }

    oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);
    oscillator.type = 'sine';

    gainNode.gain.setValueAtTime(0, audioContext.currentTime);
    gainNode.gain.linearRampToValueAtTime(volume * 0.3, audioContext.currentTime + 0.01);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration);

    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + duration);

    // Clean up
    setTimeout(() => {
      audioContext.close();
    }, (duration + 0.2) * 1000);
  }, [volume]);

  const playNotificationSound = useCallback(async (soundType: 'new_order' | 'status_update' | 'error' = 'new_order') => {
    if (!soundEnabled) return;

    try {
      // For now, use programmatically generated sounds since we don't have audio files yet
      // In production, replace this with actual sound files
      await playSystemBeep(soundType);
    } catch (error) {
      console.warn('Could not play notification sound:', error);
    }
  }, [soundEnabled, playSystemBeep]);

  const preloadSounds = useCallback(() => {
    // Currently using programmatically generated sounds, so no preloading needed
    // When switching to audio files, uncomment and modify this:
    /*
    const soundFiles = [
      '/sounds/new-order.mp3',
      '/sounds/status-update.mp3',
      '/sounds/error.mp3'
    ];

    soundFiles.forEach(soundFile => {
      const audio = new Audio();
      audio.preload = 'auto';
      audio.src = soundFile;
    });
    */
  }, []);

  return {
    playNotificationSound,
    playSystemBeep,
    preloadSounds,
    soundEnabled
  };
};
