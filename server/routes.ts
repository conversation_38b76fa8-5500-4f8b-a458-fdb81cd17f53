import { Express, Request, Response, NextFunction, Router } from 'express';
import http from 'http';
import { log } from './vite';
import { storage } from './storage';
import adminApiRouter from './admin-api';
import { setupAuth, isAdmin, isStaff, isManagerOrAdmin } from './auth';

// Status workflow validation functions
function getValidNextStatuses(currentStatus: string, orderType: string): string[] {
  const workflows = {
    delivery: {
      confirmed: ['preparing'],
      preparing: ['ready_for_delivery'],
      ready_for_delivery: ['with_driver'],
      with_driver: ['on_the_way'],
      on_the_way: ['delivered'],
      delivered: ['completed']
    },
    takeaway: {
      confirmed: ['preparing'],
      preparing: ['ready_for_pickup'],
      ready_for_pickup: ['completed']
    }
  };

  const workflow = workflows[orderType as keyof typeof workflows] || workflows.takeaway;
  return workflow[currentStatus as keyof typeof workflow] || [];
}

export async function registerRoutes(app: Express): Promise<http.Server> {
  const apiRouter = Router();

  // Logging middleware
  app.use((req: Request, _res: Response, next: NextFunction) => {
    if (req.url.startsWith('/api')) {
      log(`${req.method} ${req.url}`, 'api');
    }
    next();
  });

  // Reusable error handler
  const handleError = (error: any, res: Response) => {
    console.error(error);
    res.status(500).json({ error: 'An unexpected error occurred' });
  };

  // Menu Items / Dishes Routes
  apiRouter.get('/dishes', async (req: Request, res: Response) => {
    try {
      const dishes = await storage.getAllDishes();
      res.json(dishes);
    } catch (error) {
      handleError(error, res);
    }
  });

  apiRouter.get('/dishes/:id', async (req: Request, res: Response) => {
    try {
      const id = parseInt(req.params.id);
      const dish = await storage.getDishById(id);

      if (!dish) {
        return res.status(404).json({ error: 'Dish not found' });
      }

      res.json(dish);
    } catch (error) {
      handleError(error, res);
    }
  });

  // Categories Routes
  apiRouter.get('/categories', async (req: Request, res: Response) => {
    try {
      const categories = await storage.getAllMenuCategories();
      res.json(categories);
    } catch (error) {
      handleError(error, res);
    }
  });

  apiRouter.post('/categories', async (req: Request, res: Response) => {
    try {
      if (!req.body.name) {
        return res.status(400).json({ error: 'Category name is required' });
      }

      const newCategory = await storage.createMenuCategory(req.body);
      res.status(201).json(newCategory);
    } catch (error) {
      handleError(error, res);
    }
  });

  apiRouter.put('/categories/:id', async (req: Request, res: Response) => {
    try {
      const id = parseInt(req.params.id);
      const category = await storage.getMenuCategoryById(id);

      if (!category) {
        return res.status(404).json({ error: 'Category not found' });
      }

      const updatedCategory = await storage.updateMenuCategory(id, req.body);
      res.json(updatedCategory);
    } catch (error) {
      handleError(error, res);
    }
  });

  apiRouter.delete('/categories/:id', async (req: Request, res: Response) => {
    try {
      const id = parseInt(req.params.id);
      const category = await storage.getMenuCategoryById(id);

      if (!category) {
        return res.status(404).json({ error: 'Category not found' });
      }

      const deletedCategory = await storage.deleteMenuCategory(id);
      res.json({ message: 'Category deleted successfully', category: deletedCategory });
    } catch (error) {
      handleError(error, res);
    }
  });

  // Menu Items Routes
  apiRouter.get('/items', async (req: Request, res: Response) => {
    try {
      const items = await storage.getAllMenuItems();
      res.json(items);
    } catch (error) {
      handleError(error, res);
    }
  });

  apiRouter.post('/items', async (req: Request, res: Response) => {
    try {
      const newItem = await storage.createMenuItem(req.body);
      res.status(201).json(newItem);
    } catch (error) {
      handleError(error, res);
    }
  });

  apiRouter.put('/items/:id', async (req: Request, res: Response) => {
    try {
      const id = parseInt(req.params.id);
      const item = await storage.getMenuItemById(id);

      if (!item) {
        return res.status(404).json({ error: 'Menu item not found' });
      }

      // Update logic would go here
      res.json({ ...item, ...req.body });
    } catch (error) {
      handleError(error, res);
    }
  });

  // Order Routes
  apiRouter.post('/orders', async (req: Request, res: Response) => {
    try {
      console.log('Order creation request received');

      // Get restaurant settings directly from storage to check if it's open
      const settings = await storage.getRestaurantSettings();
      console.log('Restaurant settings:', settings);

      // Check if restaurant is open (default to true if no settings exist)
      const isOpen = settings ? settings.restaurantOpen : true;
      console.log('Restaurant is open:', isOpen);

      if (!isOpen) {
        console.log('Restaurant is closed, rejecting order');
        return res.status(403).json({
          error: 'Restaurant is currently closed. Orders cannot be placed at this time.'
        });
      }

      // If restaurant is open, proceed with order creation
      console.log('Creating order with data:', req.body);
      const newOrder = await storage.createOrder(req.body);
      console.log('Order created successfully:', newOrder);
      res.status(201).json(newOrder);
    } catch (error) {
      console.error('Error in order creation:', error);
      handleError(error, res);
    }
  });

  apiRouter.get('/orders/:id', async (req: Request, res: Response) => {
    try {
      const id = parseInt(req.params.id);
      const order = await storage.getOrderById(id);

      if (!order) {
        return res.status(404).json({ error: 'Order not found' });
      }

      res.json(order);
    } catch (error) {
      handleError(error, res);
    }
  });

  // Contact Form Route
  apiRouter.post('/contact', async (req: Request, res: Response) => {
    try {
      const message = await storage.createContactMessage(req.body);
      res.status(201).json({ success: true, message });
    } catch (error) {
      handleError(error, res);
    }
  });

  // Cart Routes
  apiRouter.post('/cart', async (req: Request, res: Response) => {
    try {
      const result = await storage.createCart(req.body);
      res.status(201).json(result);
    } catch (error) {
      handleError(error, res);
    }
  });

  // Public Settings Route (for checkout page to get delivery fee)
  apiRouter.get('/settings', async (req: Request, res: Response) => {
    try {
      const settings = await storage.getRestaurantSettings();

      if (!settings) {
        // Return default settings if none exist
        return res.json({
          delivery_fee: 49,
          estimated_time: "25-35 min",
          restaurant_open: true
        });
      }

      // Return only public settings (no business hours)
      res.json({
        delivery_fee: settings.deliveryFee,
        estimated_time: settings.estimatedTime,
        restaurant_open: settings.restaurantOpen
      });
    } catch (error) {
      handleError(error, res);
    }
  });

  // Public Customizations Route (for menu page)
  apiRouter.get('/items/:itemId/customizations', async (req: Request, res: Response) => {
    try {
      const itemId = parseInt(req.params.itemId);
      const customizations = await storage.getCustomizationOptionsForMenuItem(itemId);
      res.json(customizations);
    } catch (error) {
      handleError(error, res);
    }
  });

  // Register the API router with app
  app.use('/api', apiRouter);

  // Setup authentication
  setupAuth(app);

  // Create staff-accessible order routes (for managers and drivers)
  // These routes need to be registered before the general admin routes
  app.get('/api/admin/orders', isStaff, async (req: Request, res: Response) => {
    try {
      const statusFilter = req.query.status as string;
      // Get orders from database
      let orders = await storage.getAllOrders();

      // Apply status filtering if applicable
      if (statusFilter && statusFilter !== 'all') {
        if (statusFilter === 'active') {
          // Active orders are those that are not completed or cancelled
          orders = orders.filter(order =>
            !['completed', 'cancelled'].includes(order.status)
          );
        } else {
          orders = orders.filter(order => order.status === statusFilter);
        }
      }

      res.json(orders);
    } catch (error) {
      console.error('Error fetching orders:', error);
      res.status(500).json({ error: 'Failed to fetch orders' });
    }
  });

  app.get('/api/admin/orders/:id', isStaff, async (req: Request, res: Response) => {
    try {
      const orderId = parseInt(req.params.id);
      const order = await storage.getOrderById(orderId);

      if (!order) {
        return res.status(404).json({ error: 'Order not found' });
      }

      res.json(order);
    } catch (error) {
      console.error(`Error fetching order ${req.params.id}:`, error);
      res.status(500).json({ error: 'Failed to fetch order' });
    }
  });

  app.put('/api/admin/orders/:id/status', isStaff, async (req: Request, res: Response) => {
    try {
      const orderId = parseInt(req.params.id);
      const { newStatus } = req.body;

      if (!newStatus) {
        return res.status(400).json({ error: 'New status is required' });
      }

      // Get current order to validate status transition
      const currentOrder = await storage.getOrderById(orderId);
      if (!currentOrder) {
        return res.status(404).json({ error: 'Order not found' });
      }

      // Validate status transition
      const validStatuses = getValidNextStatuses(currentOrder.status, currentOrder.orderDetails?.type || 'takeaway');
      if (!validStatuses.includes(newStatus)) {
        return res.status(400).json({
          error: `Invalid status transition from ${currentOrder.status} to ${newStatus}`,
          validStatuses
        });
      }

      // Update the order status in the database
      const updatedOrder = await storage.updateOrder(orderId, { status: newStatus });

      if (!updatedOrder) {
        return res.status(500).json({ error: 'Failed to update order status' });
      }

      res.json({
        success: true,
        message: `Order ${orderId} status updated to ${newStatus}`,
        order: updatedOrder
      });
    } catch (error) {
      console.error(`Error updating order status for ${req.params.id}:`, error);
      res.status(500).json({ error: 'Failed to update order status' });
    }
  });

  app.post('/api/admin/dispatch/to-driver', isStaff, async (req: Request, res: Response) => {
    try {
      const { orderId } = req.body;

      if (!orderId) {
        return res.status(400).json({ error: 'Order ID is required' });
      }

      // Get the order to validate it's ready for dispatch
      const order = await storage.getOrderById(orderId);
      if (!order) {
        return res.status(404).json({ error: 'Order not found' });
      }

      // Validate that order is ready for delivery
      if (order.status !== 'ready_for_delivery') {
        return res.status(400).json({
          error: 'Order must be ready for delivery before dispatching to driver',
          currentStatus: order.status
        });
      }

      // Update order status to 'with_driver'
      const updatedOrder = await storage.updateOrder(orderId, { status: 'with_driver' });

      if (!updatedOrder) {
        return res.status(500).json({ error: 'Failed to update order status' });
      }

      res.json({
        success: true,
        message: `Order #${orderId} has been dispatched to a driver`,
        order: updatedOrder,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error dispatching order to driver:', error);
      res.status(500).json({ error: 'Failed to dispatch order to driver' });
    }
  });

  // Public order tracking endpoint
  app.get('/api/orders/:id', async (req: Request, res: Response) => {
    try {
      const orderId = parseInt(req.params.id);

      if (isNaN(orderId)) {
        return res.status(400).json({ error: 'Invalid order ID' });
      }

      const order = await storage.getOrderById(orderId);

      if (!order) {
        return res.status(404).json({ error: 'Order not found' });
      }

      // Return public order information (no sensitive data)
      const publicOrder = {
        id: order.id,
        status: order.status,
        orderDetails: order.orderDetails,
        customerDetails: {
          name: order.customerDetails?.name,
          phone: order.customerDetails?.phone?.replace(/(\d{3})\d{4}(\d{3})/, '$1****$2'), // Mask phone
          address: order.customerDetails?.address
        },
        createdAt: order.createdAt,
        estimatedDeliveryTime: order.estimatedDeliveryTime
      };

      res.json(publicOrder);
    } catch (error) {
      console.error('Error fetching order:', error);
      res.status(500).json({ error: 'Failed to fetch order' });
    }
  });

  // Register admin API routes with admin protection (for settings, menu management, etc.)
  app.use('/api/admin', isAdmin, adminApiRouter);

  // Create and return the server
  const server = http.createServer(app);
  return server;
}