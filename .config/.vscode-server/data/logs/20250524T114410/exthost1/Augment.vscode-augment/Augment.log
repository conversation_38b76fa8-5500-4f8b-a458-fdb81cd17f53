2025-05-24 11:44:15.018 [info] 'AugmentConfigListener' settings parsed successfully
2025-05-24 11:44:15.019 [info] 'AugmentConfigListener' Config changed from <unset> to {"apiToken":"","completionURL":"","modelName":"","conflictingCodingAssistantCheck":true,"codeInstruction":{},"chat":{"enableEditableHistory":false,"useRichTextHistory":true,"smartPasteUsePrecomputation":true,"experimentalFullFilePaste":false,"modelDisplayNameToId":{"Augment":null},"userGuidelines":""},"agent":{},"autofix":{"enabled":false},"oauth":{"clientID":"augment-vscode-extension","url":"https://auth.augmentcode.com"},"enableUpload":true,"shortcutsDisplayDelayMS":2000,"enableEmptyFileHint":true,"enableDataCollection":false,"enableDebugFeatures":false,"enableReviewerWorkflows":false,"completions":{"enableAutomaticCompletions":true,"disableCompletionsByLanguage":{},"enableQuickSuggestions":true,"timeoutMs":800,"maxWaitMs":1600,"addIntelliSenseSuggestions":true},"openFileManager":{},"nextEdit":{"backgroundEnabled":true,"useCursorDecorations":false,"allowDuringDebugging":false,"useMockResults":false,"noDiffModeUseCodeLens":false,"enableBackgroundSuggestions":true,"enableGlobalBackgroundSuggestions":false,"highlightSuggestionsInTheEditor":false,"showDiffInHover":false,"enableAutoApply":true},"recencySignalManager":{"collectTabSwitchEvents":false},"preferenceCollection":{"enable":false,"enableRetrievalDataCollection":false,"enableRandomizedMode":true},"vcs":{"watcherEnabled":false},"git":{"enableCommitIndexing":false,"maxCommitsToIndex":100},"smartPaste":{},"instructions":{},"integrations":{},"mcpServers":[],"advanced":{}}
2025-05-24 11:44:15.019 [info] 'FeatureFlagManager' feature flags changed from <unset> to {"gitDiff":false,"gitDiffPollingFrequencyMSec":0,"additionalChatModels":"","smallSyncThreshold":15,"bigSyncThreshold":1000,"enableWorkspaceManagerUi":true,"enableInstructions":false,"enableSmartPaste":false,"enableSmartPasteMinVersion":"","enablePromptEnhancer":false,"enableViewTextDocument":false,"bypassLanguageFilter":false,"enableHindsight":false,"maxUploadSizeBytes":131072,"vscodeNextEditBottomPanelMinVersion":"","vscodeNextEditMinVersion":"","vscodeNextEditUx1MaxVersion":"","vscodeNextEditUx2MaxVersion":"","vscodeFlywheelMinVersion":"","vscodeExternalSourcesInChatMinVersion":"","vscodeShareMinVersion":"","maxTrackableFileCount":250000,"maxTrackableFileCountWithoutPermission":150000,"minUploadedPercentageWithoutPermission":90,"memoryClassificationOnFirstToken":false,"vscodeSourcesMinVersion":"","vscodeChatHintDecorationMinVersion":"","nextEditDebounceMs":400,"enableCompletionFileEditEvents":false,"vscodeEnableCpuProfile":false,"verifyFolderIsSourceRepo":false,"refuseToSyncHomeDirectories":false,"enableFileLimitsForSyncingPermission":false,"enableChatMermaidDiagrams":false,"enableSummaryTitles":false,"smartPastePrecomputeMode":"visible-hover","vscodeNewThreadsMenuMinVersion":"","vscodeEditableHistoryMinVersion":"","vscodeEnableChatMermaidDiagramsMinVersion":"","userGuidelinesLengthLimit":2000,"workspaceGuidelinesLengthLimit":2000,"enableGuidelines":false,"useCheckpointManagerContextMinVersion":"","validateCheckpointManagerContext":false,"vscodeDesignSystemRichTextEditorMinVersion":"","allowClientFeatureFlagOverrides":false,"vscodeChatWithToolsMinVersion":"","vscodeChatMultimodalMinVersion":"","vscodeAgentModeMinVersion":"","vscodeAgentModeMinStableVersion":"","vscodeBackgroundAgentsMinVersion":"","vscodeAgentEditTool":"backend_edit_tool","vscodeRichCheckpointInfoMinVersion":"","vscodeDirectApplyMinVersion":"","memoriesParams":{},"eloModelConfiguration":{"highPriorityModels":[],"regularBattleModels":[],"highPriorityThreshold":0.5},"vscodeVirtualizedMessageListMinVersion":"","vscodeChatStablePrefixTruncationMinVersion":"","agentEditToolMinViewSize":0,"agentEditToolSchemaType":"StrReplaceEditorToolDefinitionNested","agentEditToolEnableFuzzyMatching":false,"agentEditToolFuzzyMatchSuccessMessage":"Replacement successful. old_str and new_str were slightly modified to match the original file content.","agentEditToolFuzzyMatchMaxDiff":50,"agentEditToolFuzzyMatchMaxDiffRatio":0.15,"agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs":5,"agentEditToolInstructionsReminder":false,"agentEditToolShowResultSnippet":true,"agentSaveFileToolInstructionsReminder":false,"vscodePersonalitiesMinVersion":"","useMemorySnapshotManager":false,"vscodeGenerateCommitMessageMinVersion":"","enableRules":false,"memoriesTextEditorEnabled":false,"enableModelRegistry":false,"openFileManagerV2Enabled":false,"modelRegistry":{},"vscodeTaskListMinVersion":"","vscodeRemoteAgentSSHMinVersion":"","clientAnnouncement":""}
2025-05-24 11:44:18.285 [info] 'AugmentExtension' Retrieving model config
2025-05-24 11:44:18.336 [info] 'AugmentConfigListener' settings parsed successfully
2025-05-24 11:44:19.084 [info] 'AugmentExtension' Retrieved model config
2025-05-24 11:44:19.084 [info] 'AugmentExtension' Returning model config
2025-05-24 11:44:19.121 [info] 'FeatureFlagManager' feature flags changed:
  - additionalChatModels: "" to "{}"
  - enableInstructions: false to true
  - enableSmartPasteMinVersion: "" to "0.267.0"
  - enablePromptEnhancer: false to true
  - enableViewTextDocument: false to true
  - bypassLanguageFilter: false to true
  - enableHindsight: false to true
  - maxUploadSizeBytes: 131072 to 524288
  - vscodeNextEditBottomPanelMinVersion: "" to "0.394.0"
  - vscodeNextEditMinVersion: "" to "0.343.0"
  - vscodeFlywheelMinVersion: "" to "0.282.0"
  - vscodeExternalSourcesInChatMinVersion: "" to "0.243.2"
  - vscodeShareMinVersion: "" to "0.314.0"
  - memoryClassificationOnFirstToken: false to true
  - vscodeChatHintDecorationMinVersion: "" to "0.274.0"
  - enableCompletionFileEditEvents: false to true
  - verifyFolderIsSourceRepo: false to true
  - refuseToSyncHomeDirectories: false to true
  - enableFileLimitsForSyncingPermission: false to true
  - enableSummaryTitles: false to true
  - smartPastePrecomputeMode: "visible-hover" to "visible"
  - vscodeNewThreadsMenuMinVersion: "" to "0.305.0"
  - vscodeEditableHistoryMinVersion: "" to "0.330.0"
  - vscodeEnableChatMermaidDiagramsMinVersion: "" to "0.314.0"
  - userGuidelinesLengthLimit: 2000 to 24576
  - workspaceGuidelinesLengthLimit: 2000 to 24576
  - enableGuidelines: false to true
  - useCheckpointManagerContextMinVersion: "" to "0.323.0"
  - vscodeDesignSystemRichTextEditorMinVersion: "" to "0.363.0"
  - vscodeChatMultimodalMinVersion: "" to "0.384.0"
  - vscodeAgentModeMinVersion: "" to "0.395.0"
  - vscodeAgentModeMinStableVersion: "" to "0.399.1"
  - vscodeAgentEditTool: "backend_edit_tool" to "str_replace_editor_tool"
  - eloModelConfiguration > highPriorityModels: [] to undefined
  - eloModelConfiguration > regularBattleModels: [] to undefined
  - eloModelConfiguration > highPriorityThreshold: 0.5 to undefined
  - vscodeChatStablePrefixTruncationMinVersion: "" to "0.402.0"
  - agentEditToolMinViewSize: 0 to 500
  - agentEditToolSchemaType: "StrReplaceEditorToolDefinitionNested" to "StrReplaceEditorToolDefinitionFlat"
  - agentEditToolEnableFuzzyMatching: false to true
  - agentEditToolInstructionsReminder: false to true
  - agentSaveFileToolInstructionsReminder: false to true
  - useMemorySnapshotManager: false to true
  - openFileManagerV2Enabled: false to true
  - vscodeRemoteAgentSSHMinVersion: "" to "0.456.0"
  - clientAnnouncement: "" to "🎉 Your Agents are now using Claude Sonnet 4!"
2025-05-24 11:44:19.121 [info] 'SyncingPermissionTracker' Initial syncing permission: syncing permission granted for workspace. Folders:
    /home/<USER>/workspace (explicit) at 5/21/2025, 11:43:10 PM
2025-05-24 11:44:19.121 [info] 'WorkspaceManager' OpenFileManagerProxy created. V2 enabled: [true]
2025-05-24 11:44:19.121 [info] 'BlobsCheckpointManager' BlobsCheckpointManager created. checkpointThreshold: 1000
2025-05-24 11:44:19.121 [info] 'SyncingPermissionTracker' Permission to sync folder /home/<USER>/workspace granted at 5/21/2025, 11:43:10 PM; type = explicit
2025-05-24 11:44:19.121 [info] 'WorkspaceManager' Adding workspace folder workspace; folderRoot = /home/<USER>/workspace; syncingPermission = granted
2025-05-24 11:44:19.121 [info] 'SyncingPermissionTracker' Updating syncing permission: syncing permission granted for workspace. Folders:
    /home/<USER>/workspace (explicit) at 5/21/2025, 11:43:10 PM
2025-05-24 11:44:19.141 [info] 'RemoteAgentsMessenger' RemoteAgentsMessenger initialized, setting up onDidChangeTextDocument listener
2025-05-24 11:44:19.141 [info] 'RemoteAgentsMessenger' Registering RemoteAgentsMessenger with AsyncMsgHandler
2025-05-24 11:44:19.141 [info] 'HotKeyHints' HotKeyHints initialized
2025-05-24 11:44:19.141 [info] 'ToolsModel' Loaded saved chat mode: AGENT
2025-05-24 11:44:19.157 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-05-24 11:44:19.158 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-05-24 11:44:19.587 [error] 'RemoteAgentsMessenger' Unexpected message type: main-panel-loaded
2025-05-24 11:44:19.944 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:19.945 [info] 'WorkspaceManager[workspace]' Start tracking
2025-05-24 11:44:19.946 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:19.946 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:19.953 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:19.953 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:19.953 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:19.953 [info] 'TaskManager' Setting current root task UUID to e32d22d4-ad72-4243-b521-68cf64daa6ee
2025-05-24 11:44:19.953 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:19.953 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:19.953 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:19.953 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:19.953 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:19.953 [error] 'RemoteAgentsMessenger' Unexpected message type: update-shared-webview-state
2025-05-24 11:44:19.960 [info] 'PathMap' Opened source folder /home/<USER>/workspace with id 100
2025-05-24 11:44:19.960 [info] 'OpenFileManager' Opened source folder 100
2025-05-24 11:44:19.961 [info] 'MtimeCache[workspace]' reading blob name cache from /home/<USER>/.vscode-server/data/User/workspaceStorage/28d495afc6b3165b645c0d6d358123ef/Augment.vscode-augment/ce248d0eb8d4f662cc202e5206f37824b70c818932a8dbfb169463d063d70aa1/mtime-cache.json
2025-05-24 11:44:19.968 [info] 'MtimeCache[workspace]' read 1644 entries from /home/<USER>/.vscode-server/data/User/workspaceStorage/28d495afc6b3165b645c0d6d358123ef/Augment.vscode-augment/ce248d0eb8d4f662cc202e5206f37824b70c818932a8dbfb169463d063d70aa1/mtime-cache.json
2025-05-24 11:44:20.056 [error] 'RemoteAgentsMessenger' Unexpected message type: get-orientation-status
2025-05-24 11:44:20.221 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:20.301 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-05-24 11:44:20.301 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-05-24 11:44:20.301 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-05-24 11:44:20.301 [info] 'ToolsModel' Host: sidecarToolHost (7 tools: 101 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + remember
 + render-mermaid
 + view

2025-05-24 11:44:20.433 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-05-24 11:44:20.433 [info] 'ToolsModel' Host: sidecarToolHost (7 tools: 101 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + remember
 + render-mermaid
 + view

2025-05-24 11:44:20.619 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:20.619 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:20.619 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:20.619 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.406 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/logs/20250524T114410/exthost1/output_logging_20250524T114412
2025-05-24 11:44:21.776 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.776 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.776 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.776 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.776 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.776 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.988 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.996 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.996 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.996 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.996 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.996 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.996 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.996 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.997 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.997 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.997 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.997 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.997 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.997 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.997 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.997 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.997 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.997 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.997 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.997 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.997 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.997 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.997 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.997 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.997 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.997 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.998 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.998 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.998 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.998 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.998 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.998 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.998 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.998 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.998 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:21.998 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:22.673 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:23.069 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:44:27.373 [info] 'StallDetector' Event loop delay: Timer(100 msec) ran 3198 msec late.
2025-05-24 11:44:37.672 [error] 'AugmentExtension' API request 1a011b5a-c0c3-4760-bded-1a61edcc0b57 to https://i1.api.augmentcode.com/record-session-events response 502: Bad Gateway
2025-05-24 11:44:37.976 [error] 'AgentSessionEventReporter' Error uploading metrics: Error: HTTP error: 502 Bad Gateway Error: HTTP error: 502 Bad Gateway
    at Function.fromResponse (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.464.0/out/extension.js:236:11786)
    at xQ.callApi (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.464.0/out/extension.js:1135:13364)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at xQ.callApi (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.464.0/out/extension.js:1135:54293)
    at xQ.logAgentSessionEvent (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.464.0/out/extension.js:1135:33544)
    at /home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.464.0/out/extension.js:236:15614
    at ts (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.464.0/out/extension.js:236:14159)
    at e._doUpload (/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.464.0/out/extension.js:236:15508)
    at /home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.464.0/out/extension.js:236:15179
2025-05-24 11:44:37.977 [info] 'AgentSessionEventReporter' Operation failed with error Error: HTTP error: 502 Bad Gateway, retrying in 100 ms; retries = 0
2025-05-24 11:44:38.374 [info] 'AgentSessionEventReporter' Operation succeeded after 1 transient failures
2025-05-24 11:44:42.902 [info] 'WorkspaceManager[workspace]' Tracking enabled
2025-05-24 11:44:42.902 [info] 'WorkspaceManager[workspace]' Path metrics:
  - directories emitted: 374
  - files emitted: 1842
  - other paths emitted: 4
  - total paths emitted: 2220
  - timing stats:
    - readDir: 11 ms
    - filter: 106 ms
    - yield: 32 ms
    - total: 162 ms
2025-05-24 11:44:42.902 [info] 'WorkspaceManager[workspace]' File metrics:
  - paths accepted: 1708
  - paths not accessible: 0
  - not plain files: 0
  - large files: 28
  - blob name calculation fails: 0
  - encoding errors: 0
  - mtime cache hits: 1634
  - mtime cache misses: 74
  - probe batches: 8
  - blob names probed: 1741
  - files read: 262
  - blobs uploaded: 29
  - timing stats:
    - ingestPath: 11 ms
    - probe: 8801 ms
    - stat: 28 ms
    - read: 1879 ms
    - upload: 3692 ms
2025-05-24 11:44:42.903 [info] 'WorkspaceManager[workspace]' Startup metrics:
  - create SourceFolder: 15 ms
  - read MtimeCache: 8 ms
  - pre-populate PathMap: 78 ms
  - create PathFilter: 805 ms
  - create PathNotifier: 0 ms
  - enumerate paths: 168 ms
  - purge stale PathMap entries: 0 ms
  - enumerate: 0 ms
  - await DiskFileManager quiesced: 21879 ms
  - enable persist: 3 ms
  - total: 22956 ms
2025-05-24 11:44:42.903 [info] 'WorkspaceManager' Workspace startup complete in 23797 ms
2025-05-24 11:44:49.945 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:45:19.956 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:45:49.949 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:46:19.955 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:46:49.957 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:47:19.961 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:47:49.957 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:48:19.953 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:48:49.953 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:48:57.316 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:48:59.192 [info] 'StallDetector' Recent work: [{"name":"open-confirmation-modal","durationMs":1806.058978,"timestamp":"2025-05-24T11:48:59.122Z"}]
2025-05-24 11:48:59.990 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:49:00.196 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:49:00.197 [info] 'ToolsWebviewMessageHandler' Received closeAllToolProcesses message
2025-05-24 11:49:00.197 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:49:00.197 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:49:00.197 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:49:00.377 [info] 'TaskManager' Setting current root task UUID to eeca5ce5-787d-4538-8540-43173a4419d7
2025-05-24 11:49:00.377 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:49:00.377 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:49:00.377 [info] 'TaskManager' Setting current root task UUID to eeca5ce5-787d-4538-8540-43173a4419d7
2025-05-24 11:49:00.377 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:49:00.694 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:49:19.954 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:49:49.957 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:50:19.951 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:50:49.953 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:51:20.257 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:51:49.949 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:52:19.951 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:52:49.948 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:53:19.951 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:53:49.960 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:54:19.947 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:54:49.949 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:55:19.961 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:55:49.959 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:56:19.954 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:56:49.949 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:57:19.948 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:57:49.968 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:58:19.953 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:58:49.957 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:59:19.950 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 11:59:49.955 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:00:19.960 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:00:49.963 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:01:19.961 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:01:49.953 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:02:19.953 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:02:49.953 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:03:19.960 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:03:49.967 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:04:19.951 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:04:49.966 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:05:19.952 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:05:49.951 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:06:19.951 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:06:49.963 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:07:19.952 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:07:49.963 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:08:19.962 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:08:49.965 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:09:19.955 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:09:49.951 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:10:19.959 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:10:49.968 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:11:19.966 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:11:49.961 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:12:19.953 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:12:49.955 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:13:19.960 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:13:49.955 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:14:14.397 [info] 'AugmentExtension' Retrieving model config
2025-05-24 12:14:14.684 [info] 'AugmentExtension' Retrieved model config
2025-05-24 12:14:14.684 [info] 'AugmentExtension' Returning model config
2025-05-24 12:14:19.965 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:14:49.957 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:15:14.655 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:15:14.836 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:15:19.968 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:15:34.399 [error] 'RemoteAgentsMessenger' Unexpected message type: used-chat
2025-05-24 12:15:34.403 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:15:34.403 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:15:34.420 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:15:34.582 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:15:34.907 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:15:40.973 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:15:40.992 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:15:42.549 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:15:43.679 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:15:43.683 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:15:43.928 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:15:46.510 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:15:48.885 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:15:49.073 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:15:49.955 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:15:55.091 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:15:55.306 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:15:55.309 [info] 'ViewTool' Tool called with path: client/src/pages/Checkout.tsx and view_range: [900,1100]
2025-05-24 12:15:56.384 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:15:56.565 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:16:03.383 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:16:03.563 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:16:03.566 [info] 'ViewTool' Tool called with path: client/src/pages/Checkout.tsx and view_range: [675,925]
2025-05-24 12:16:03.922 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:16:04.106 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:16:19.963 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:16:49.959 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:17:15.689 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:17:15.882 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:17:15.885 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-24 12:17:15.885 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (82600 bytes)
2025-05-24 12:17:16.075 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:17:17.140 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:17:17.949 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:17:18.759 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-24 12:17:18.760 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (97931 bytes)
2025-05-24 12:17:18.984 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:17:19.958 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:17:21.244 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/workspaceStorage/28d495afc6b3165b645c0d6d358123ef/Augment.vscode-augment/augment-user-assets/checkpoint-documents/171f58de-c287-4210-9a22-3dd69a12fcea
2025-05-24 12:17:21.983 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:17:22.174 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:17:27.610 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:17:27.804 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:17:27.807 [info] 'ViewTool' Tool called with path: client/src/pages/Checkout.tsx and view_range: [1180,1280]
2025-05-24 12:17:28.171 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:17:28.364 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:17:49.964 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:18:19.963 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:18:21.535 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:18:21.724 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:18:21.727 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-24 12:18:21.728 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (97931 bytes)
2025-05-24 12:18:22.123 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:18:23.121 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:18:23.627 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:18:23.977 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-24 12:18:23.977 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (83073 bytes)
2025-05-24 12:18:24.156 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:18:27.174 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:18:27.369 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:18:32.173 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:18:32.364 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:18:32.551 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:18:32.742 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:18:37.901 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:18:38.095 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:18:38.098 [info] 'ViewTool' Tool called with path: client/src/pages/Checkout.tsx and view_range: [675,950]
2025-05-24 12:18:38.494 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:18:38.686 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:18:44.667 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:18:44.860 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:18:45.108 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:18:45.334 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:18:49.923 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:18:49.959 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:18:50.116 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:18:50.298 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:18:50.487 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:18:54.954 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:18:55.146 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:18:55.327 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:18:55.517 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:18:59.755 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:18:59.955 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:19:02.410 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:19:02.600 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:19:07.327 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:19:07.519 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:19:07.759 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:19:07.949 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:19:12.112 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:19:12.302 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:19:13.122 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:19:13.365 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:19:17.645 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:19:17.836 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:19:18.019 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:19:18.212 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:19:19.957 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:19:23.485 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:19:23.681 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:19:23.915 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:19:24.107 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:19:28.133 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:19:28.347 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:19:28.565 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:19:28.757 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:19:33.352 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:19:33.547 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:19:33.550 [info] 'ViewTool' Tool called with path: package.json and view_range: undefined
2025-05-24 12:19:34.438 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:19:34.631 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:19:40.565 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:19:40.762 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:19:40.982 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:19:41.175 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:19:45.757 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:19:45.951 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:19:46.168 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:19:46.364 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:19:49.959 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:19:50.639 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:19:50.833 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:19:51.012 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:19:51.205 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:19:56.337 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:19:56.536 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:19:58.132 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:19:58.366 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:20:02.797 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:20:02.997 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:20:03.224 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:20:03.456 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:20:07.746 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:20:07.976 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:20:18.359 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:20:18.554 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:20:19.955 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:20:23.911 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:20:24.104 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:20:24.464 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:20:24.658 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:20:39.436 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:20:39.436 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:20:39.436 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:20:39.821 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:20:40.342 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:20:49.960 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:21:19.961 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:21:49.955 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:22:19.969 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:22:49.963 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:23:19.966 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:23:49.963 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:24:19.955 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:24:49.968 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:25:05.026 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:25:05.214 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:25:19.957 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:25:49.956 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:26:19.958 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:26:49.965 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:27:19.871 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:27:19.961 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:27:20.069 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:27:27.651 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:27:27.877 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:27:30.792 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:27:49.963 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:28:19.966 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:28:49.963 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:29:19.969 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:29:20.462 [error] 'RemoteAgentsMessenger' Unexpected message type: used-chat
2025-05-24 12:29:20.462 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:29:20.462 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:29:20.485 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:29:20.834 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:29:20.959 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:29:25.369 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:29:25.376 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:29:26.397 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:29:28.126 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:29:28.126 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:29:28.339 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:29:31.611 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:29:33.792 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:29:33.990 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:29:42.351 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:29:42.548 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:29:42.551 [info] 'ViewTool' Tool called with path: client/src/pages/Checkout.tsx and view_range: [936,1180]
2025-05-24 12:29:43.676 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:29:43.886 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:29:49.962 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:30:02.936 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:30:03.137 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:30:03.141 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-24 12:30:03.142 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (83073 bytes)
2025-05-24 12:30:03.942 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:30:04.972 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:30:05.455 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:30:05.817 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-24 12:30:05.818 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (83160 bytes)
2025-05-24 12:30:06.115 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:30:09.001 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:30:09.198 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:30:18.459 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:30:18.665 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:30:18.669 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-24 12:30:18.669 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (83160 bytes)
2025-05-24 12:30:19.161 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:30:19.966 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:30:20.071 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:30:20.595 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:30:20.954 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-24 12:30:20.954 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (83181 bytes)
2025-05-24 12:30:21.129 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:30:24.160 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:30:24.363 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:30:49.961 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:30:51.847 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:30:52.048 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:30:52.052 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-24 12:30:52.053 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (83181 bytes)
2025-05-24 12:30:52.738 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:30:53.601 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:30:54.110 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:30:54.542 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-24 12:30:54.543 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (80165 bytes)
2025-05-24 12:30:54.823 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:30:57.737 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:30:57.937 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:31:19.969 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:31:21.754 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:31:21.955 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:31:21.959 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-24 12:31:21.960 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (80165 bytes)
2025-05-24 12:31:22.512 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:31:23.508 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:31:24.008 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:31:24.393 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-24 12:31:24.394 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (79778 bytes)
2025-05-24 12:31:24.602 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:31:27.624 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:31:27.827 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:31:49.969 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:31:53.900 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:31:54.101 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:31:54.104 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-24 12:31:54.105 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (79778 bytes)
2025-05-24 12:31:54.884 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:31:55.858 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:31:56.351 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:31:56.839 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-24 12:31:56.839 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (79779 bytes)
2025-05-24 12:31:57.015 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:32:00.076 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:32:00.282 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:32:06.467 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:32:06.687 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:32:06.876 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:32:07.080 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:32:13.202 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:32:13.403 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:32:13.780 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:32:13.983 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:32:19.963 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:32:20.598 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:32:20.806 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:32:21.172 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:32:21.378 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:32:32.701 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:32:32.891 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:32:33.243 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:32:33.475 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:32:33.729 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:32:33.971 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:32:34.205 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:32:34.737 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:32:34.744 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:32:34.752 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:32:35.045 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:32:35.301 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:32:35.423 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:32:35.797 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:32:36.029 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:32:36.356 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:32:36.579 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:32:36.971 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:32:37.119 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:32:37.347 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:32:37.591 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:32:37.815 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:32:37.927 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:32:37.990 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:32:38.147 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:32:38.305 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:32:38.435 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:32:38.631 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:32:38.784 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:32:38.945 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:32:39.178 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:32:39.354 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:32:39.467 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:32:39.639 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:32:39.796 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:32:40.151 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:32:40.241 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:32:40.421 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:32:40.648 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:32:40.768 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:32:40.913 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:32:41.063 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:32:41.293 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:32:41.379 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:32:41.519 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:32:41.925 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:32:42.052 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:32:42.087 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:32:42.550 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:32:42.550 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:32:42.550 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:32:42.769 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:32:43.279 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:32:49.980 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:33:19.962 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:33:31.635 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:33:31.857 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:33:33.965 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:33:34.165 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:33:49.961 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:33:50.945 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:34:16.643 [error] 'RemoteAgentsMessenger' Unexpected message type: used-chat
2025-05-24 12:34:16.643 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:34:16.643 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:34:16.643 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:34:16.933 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:34:17.120 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:34:19.957 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:34:21.549 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:34:21.558 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:34:22.587 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:34:24.149 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:34:24.149 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:34:24.366 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:34:24.379 [info] 'ViewTool' Tool called with path: client/src/pages/Checkout.tsx and view_range: [1190,1280]
2025-05-24 12:34:25.286 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:34:25.496 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:34:27.778 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:34:30.969 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:34:31.198 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:34:31.202 [info] 'ViewTool' Tool called with path: client/src/pages/Checkout.tsx and view_range: [1130,1200]
2025-05-24 12:34:31.560 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:34:31.773 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:34:43.456 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:34:43.667 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:34:43.672 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-24 12:34:43.672 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (79779 bytes)
2025-05-24 12:34:44.551 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:34:45.577 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:34:46.090 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:34:46.568 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-24 12:34:46.571 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (79948 bytes)
2025-05-24 12:34:47.343 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:34:49.833 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:34:49.971 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:34:50.089 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:35:14.116 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:35:14.325 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:35:14.336 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-24 12:35:14.337 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (79948 bytes)
2025-05-24 12:35:14.624 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:35:14.832 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:35:19.863 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:35:19.971 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:35:20.072 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:35:20.076 [info] 'ViewTool' Tool called with path: client/src/pages/Checkout.tsx and view_range: [245,320]
2025-05-24 12:35:20.477 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:35:20.688 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:35:41.725 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:35:41.937 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:35:41.944 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-24 12:35:41.944 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (79948 bytes)
2025-05-24 12:35:42.413 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:35:43.395 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:35:43.894 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:35:44.250 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-24 12:35:44.250 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (83187 bytes)
2025-05-24 12:35:45.036 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:35:47.449 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:35:47.662 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:35:49.961 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:35:52.978 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:35:53.195 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:35:53.201 [info] 'ViewTool' Tool called with path: client/src/pages/Checkout.tsx and view_range: [1230,1280]
2025-05-24 12:35:53.594 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:35:53.811 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:36:19.960 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:36:20.713 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:36:20.929 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:36:20.935 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-24 12:36:20.935 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (83187 bytes)
2025-05-24 12:36:21.463 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:36:22.457 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:36:22.967 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:36:23.358 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-24 12:36:23.358 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (87444 bytes)
2025-05-24 12:36:23.538 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:36:26.540 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:36:26.768 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:36:49.971 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:37:08.015 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:37:08.231 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:37:08.242 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-24 12:37:08.242 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (87444 bytes)
2025-05-24 12:37:08.771 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:37:09.775 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:37:10.288 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:37:10.728 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-24 12:37:10.729 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (88003 bytes)
2025-05-24 12:37:10.954 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:37:13.919 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:37:14.132 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:37:19.960 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:37:24.700 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:37:24.930 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:37:24.958 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-24 12:37:24.962 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (88003 bytes)
2025-05-24 12:37:25.547 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:37:26.563 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:37:27.176 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:37:28.000 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-24 12:37:28.002 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (88393 bytes)
2025-05-24 12:37:28.196 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:37:31.206 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:37:31.424 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:37:36.121 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:37:36.333 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:37:36.601 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:37:36.852 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:37:40.817 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:37:41.064 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:37:41.260 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:37:41.489 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:37:45.581 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:37:45.804 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:37:46.003 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:37:46.222 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:37:49.973 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:37:50.500 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:37:50.723 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:37:50.950 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:37:51.207 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:37:55.183 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:37:55.402 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:37:55.624 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:37:55.840 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:38:00.364 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:38:00.585 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:38:00.590 [info] 'ViewTool' Tool called with path: client/package.json and view_range: undefined
2025-05-24 12:38:00.766 [info] 'ViewTool' Path does not exist: client/package.json
2025-05-24 12:38:01.344 [info] 'ToolFileUtils' File not found: client/package.json. Similar files found: package.json, node_modules/color-name/package.json, node_modules/d3-interpolate/package.json, node_modules/debug/package.json, node_modules/graceful-fs/package.json
2025-05-24 12:38:01.524 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:38:01.797 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:38:06.767 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:38:06.985 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:38:06.992 [info] 'ViewTool' Tool called with path: . and view_range: undefined
2025-05-24 12:38:07.172 [info] 'ViewTool' Listing directory: . (depth: 2, showHidden: false)
2025-05-24 12:38:19.969 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:38:49.965 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:39:07.973 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:39:08.194 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:39:13.883 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:39:14.106 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:39:14.112 [info] 'ViewTool' Tool called with path: package.json and view_range: undefined
2025-05-24 12:39:15.008 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:39:15.230 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:39:19.963 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:39:20.135 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:39:20.357 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:39:20.602 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:39:20.825 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:39:25.243 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:39:25.466 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:39:25.648 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:39:25.866 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:39:29.467 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:39:29.689 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:39:29.876 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:39:30.098 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:39:35.270 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:39:35.492 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:39:35.677 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:39:35.898 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:39:40.492 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:39:40.713 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:39:44.243 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:39:44.466 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:39:48.658 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:39:48.878 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:39:49.062 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:39:49.282 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:39:50.004 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:39:53.689 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:39:53.915 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:39:55.501 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:39:55.724 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:40:00.607 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:40:00.831 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:40:01.192 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:40:01.418 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:40:19.649 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:40:19.650 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:40:19.651 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:40:19.963 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:40:20.011 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:40:20.585 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:40:49.959 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:41:19.973 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:41:49.960 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:41:50.294 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:41:50.497 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:41:51.198 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:41:51.420 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:42:06.008 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:42:07.073 [error] 'RemoteAgentsMessenger' Unexpected message type: used-chat
2025-05-24 12:42:07.073 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:42:07.073 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:42:07.092 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:42:07.333 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:42:07.567 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:42:10.611 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:42:10.611 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:42:10.838 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:42:12.737 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:42:12.743 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:42:12.959 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:42:12.966 [info] 'ViewTool' Tool called with path: client/src/pages/Checkout.tsx and view_range: [1295,1340]
2025-05-24 12:42:13.873 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:42:14.102 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:42:15.822 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:42:19.969 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:42:20.273 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:42:20.502 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:42:20.510 [info] 'ViewTool' Tool called with path: client/src/pages/Checkout.tsx and view_range: [302,392]
2025-05-24 12:42:20.872 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:42:21.101 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:42:49.975 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:42:57.909 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:42:58.142 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:42:58.154 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-24 12:42:58.154 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (88393 bytes)
2025-05-24 12:42:59.007 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:43:00.137 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:43:00.648 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:43:00.990 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-24 12:43:00.990 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (89062 bytes)
2025-05-24 12:43:01.166 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:43:04.172 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:43:04.402 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:43:10.250 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:43:10.475 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:43:10.484 [info] 'ViewTool' Tool called with path: client/src/pages/Checkout.tsx and view_range: [1,50]
2025-05-24 12:43:10.876 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:43:11.107 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:43:19.966 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:43:21.154 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:43:21.383 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:43:21.390 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-24 12:43:21.390 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (89062 bytes)
2025-05-24 12:43:21.931 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:43:23.041 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:43:23.448 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:43:23.766 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-24 12:43:23.766 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (89079 bytes)
2025-05-24 12:43:23.973 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:43:26.962 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:43:27.289 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:43:36.809 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:43:37.048 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:43:37.239 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:43:37.467 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:43:43.057 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:43:43.293 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:43:43.692 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:43:43.925 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:43:49.972 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:43:57.716 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:43:58.048 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:43:58.056 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-24 12:43:58.056 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (89079 bytes)
2025-05-24 12:43:58.703 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:43:59.660 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:44:00.172 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:44:00.707 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-24 12:44:00.707 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (89411 bytes)
2025-05-24 12:44:00.895 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:44:03.907 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:44:04.144 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:44:12.376 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:44:12.642 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:44:12.648 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-24 12:44:12.649 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (89411 bytes)
2025-05-24 12:44:13.074 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:44:14.096 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:44:14.397 [info] 'AugmentExtension' Retrieving model config
2025-05-24 12:44:14.664 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:44:14.774 [info] 'AugmentExtension' Retrieved model config
2025-05-24 12:44:14.774 [info] 'AugmentExtension' Returning model config
2025-05-24 12:44:15.063 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-24 12:44:15.063 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (89476 bytes)
2025-05-24 12:44:15.298 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:44:18.314 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:44:18.555 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:44:19.975 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:44:26.722 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:44:27.522 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:44:27.529 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-24 12:44:27.529 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (89476 bytes)
2025-05-24 12:44:28.077 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:44:29.136 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:44:29.576 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:44:29.948 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-24 12:44:29.948 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (89605 bytes)
2025-05-24 12:44:30.130 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:44:33.128 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:44:33.402 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:44:42.321 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:44:42.557 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:44:42.564 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-24 12:44:42.564 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (89605 bytes)
2025-05-24 12:44:43.176 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:44:44.278 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:44:44.630 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:44:45.090 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-24 12:44:45.090 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (89731 bytes)
2025-05-24 12:44:45.307 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:44:48.338 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:44:48.573 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:44:49.966 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:44:56.353 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:44:56.589 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:44:56.597 [info] 'ViewTool' Tool called with path: client/src/pages/Checkout.tsx and view_range: [1350,1420]
2025-05-24 12:44:56.954 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:44:57.192 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:45:19.960 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:45:21.025 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:45:21.262 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:45:21.268 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-24 12:45:21.269 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (89731 bytes)
2025-05-24 12:45:21.881 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:45:22.823 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:45:23.335 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:45:23.841 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-24 12:45:23.841 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (91133 bytes)
2025-05-24 12:45:24.023 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:45:27.098 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:45:27.333 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:45:32.426 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:45:32.662 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:45:32.846 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:45:33.087 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:45:48.655 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:45:48.898 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:45:48.904 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-24 12:45:48.904 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (91133 bytes)
2025-05-24 12:45:49.483 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:45:49.962 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:45:50.489 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:45:50.988 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:45:51.353 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-24 12:45:51.354 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (91506 bytes)
2025-05-24 12:45:51.533 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:45:54.553 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:45:54.835 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:46:01.534 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:46:01.775 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:46:02.168 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:46:02.411 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:46:19.973 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:46:31.773 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:46:32.016 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:46:32.023 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-24 12:46:32.607 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (91506 bytes)
2025-05-24 12:46:33.420 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:46:34.397 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:46:34.908 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:46:35.313 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-24 12:46:35.314 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (90913 bytes)
2025-05-24 12:46:35.500 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:46:38.525 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:46:38.762 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:46:42.935 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:46:43.170 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:46:43.361 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:46:43.597 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:46:49.962 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:46:55.916 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:47:05.739 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:47:06.111 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:47:06.609 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:47:19.961 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:47:47.800 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:47:47.977 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:47:49.975 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:48:07.016 [error] 'RemoteAgentsMessenger' Unexpected message type: used-chat
2025-05-24 12:48:07.016 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:48:07.018 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:48:07.434 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:48:07.522 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:48:10.988 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:48:11.231 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:48:13.252 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:48:13.255 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:48:13.507 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:48:15.980 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:48:19.010 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:48:19.248 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:48:19.986 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:48:38.037 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:48:38.273 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:48:38.281 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-24 12:48:38.281 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (90913 bytes)
2025-05-24 12:48:38.785 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:48:39.779 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:48:40.299 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:48:40.632 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-24 12:48:40.632 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (91412 bytes)
2025-05-24 12:48:40.810 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:48:43.861 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:48:44.101 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:48:49.993 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:48:57.065 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:48:57.317 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:48:57.323 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-24 12:48:57.323 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (91412 bytes)
2025-05-24 12:48:57.865 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:48:58.893 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:48:59.392 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:48:59.748 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-24 12:48:59.748 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (91635 bytes)
2025-05-24 12:48:59.928 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:49:02.929 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:49:03.218 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:49:17.704 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:49:17.945 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:49:17.952 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-24 12:49:17.952 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (91635 bytes)
2025-05-24 12:49:18.518 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:49:19.518 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:49:20.038 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:49:20.038 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:49:20.547 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-24 12:49:20.548 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (92520 bytes)
2025-05-24 12:49:20.728 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:49:23.780 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:49:24.021 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:49:34.972 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:49:35.252 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:49:35.260 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-24 12:49:35.845 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (92520 bytes)
2025-05-24 12:49:36.822 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:49:37.795 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:49:38.364 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:49:38.655 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-24 12:49:38.656 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (92755 bytes)
2025-05-24 12:49:38.833 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:49:41.999 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:49:42.245 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:49:48.119 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:49:48.373 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:49:48.798 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:49:49.073 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:49:50.076 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:50:10.176 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:50:10.359 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:50:10.872 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:50:19.969 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:50:23.665 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:50:23.845 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:50:40.350 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:50:40.532 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:50:43.551 [error] 'RemoteAgentsMessenger' Unexpected message type: used-chat
2025-05-24 12:50:43.552 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:50:43.552 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:50:43.590 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:50:43.802 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:50:44.064 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:50:47.600 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:50:47.608 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:50:48.193 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:50:50.008 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:50:51.264 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:50:51.265 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:50:51.508 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:50:51.514 [info] 'ViewTool' Tool called with path: client/src/pages/Checkout.tsx and view_range: [180,200]
2025-05-24 12:50:51.904 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:50:52.143 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:50:53.004 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:50:58.808 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:50:59.064 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:51:04.156 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:51:04.404 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:51:12.567 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:51:12.813 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:51:12.821 [info] 'ViewTool' Tool called with path: client/src/context/RestaurantStatusContext.tsx and view_range: [30,60]
2025-05-24 12:51:13.712 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:51:13.955 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:51:19.978 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:51:23.918 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:51:24.170 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:51:24.178 [info] 'ToolFileUtils' Reading file: client/src/context/RestaurantStatusContext.tsx
2025-05-24 12:51:24.178 [info] 'ToolFileUtils' Successfully read file: client/src/context/RestaurantStatusContext.tsx (2834 bytes)
2025-05-24 12:51:24.358 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:51:25.361 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:51:26.151 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:51:26.250 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/-3f810595
2025-05-24 12:51:26.944 [info] 'ToolFileUtils' Reading file: client/src/context/RestaurantStatusContext.tsx
2025-05-24 12:51:26.945 [info] 'ToolFileUtils' Successfully read file: client/src/context/RestaurantStatusContext.tsx (2822 bytes)
2025-05-24 12:51:27.184 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:51:30.144 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:51:30.389 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:51:44.987 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:51:45.237 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:51:45.249 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-24 12:51:45.250 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (92755 bytes)
2025-05-24 12:51:45.824 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:51:46.822 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:51:47.445 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:51:47.743 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-24 12:51:47.743 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (92212 bytes)
2025-05-24 12:51:47.924 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:51:49.977 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:51:50.927 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:51:51.170 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:52:03.902 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:52:04.151 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:52:04.161 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-24 12:52:04.161 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (92212 bytes)
2025-05-24 12:52:04.712 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:52:05.710 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:52:06.210 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:52:06.562 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-24 12:52:06.562 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (91989 bytes)
2025-05-24 12:52:06.741 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:52:09.794 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:52:10.040 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:52:19.392 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:52:19.639 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:52:19.696 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-24 12:52:19.696 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (91989 bytes)
2025-05-24 12:52:19.968 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:52:20.331 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:52:21.255 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:52:21.767 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:52:22.228 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-24 12:52:22.228 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (91844 bytes)
2025-05-24 12:52:22.408 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:52:25.412 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:52:25.665 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:52:29.990 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:52:30.243 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:52:30.512 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:52:30.772 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:52:36.745 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:52:36.993 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:52:37.354 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:52:37.600 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:52:44.816 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:52:49.975 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:52:56.338 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:52:56.522 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:52:57.035 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:53:13.567 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:53:13.747 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:53:19.972 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:53:28.257 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:53:28.440 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:53:28.440 [info] 'ToolsWebviewMessageHandler' Received closeAllToolProcesses message
2025-05-24 12:53:28.440 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:53:28.440 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:53:28.446 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:53:28.664 [info] 'TaskManager' Setting current root task UUID to d2e59e91-1713-4c71-a56d-19125c0d4b4a
2025-05-24 12:53:28.664 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:53:28.664 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:53:28.664 [info] 'TaskManager' Setting current root task UUID to d2e59e91-1713-4c71-a56d-19125c0d4b4a
2025-05-24 12:53:28.664 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:53:28.939 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:53:49.971 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:54:20.001 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:54:49.976 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:55:19.976 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:55:49.963 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:56:19.966 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:56:49.968 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:57:19.975 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:57:37.756 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:57:37.934 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:57:42.411 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:57:42.628 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:57:43.528 [error] 'RemoteAgentsMessenger' Unexpected message type: used-chat
2025-05-24 12:57:43.529 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:57:43.529 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:57:43.548 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:57:43.548 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:57:43.729 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:57:44.038 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:57:46.786 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:57:46.786 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:57:46.787 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:57:47.002 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:57:50.061 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:57:50.649 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:57:50.651 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:57:50.825 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:57:51.402 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:57:55.920 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:57:56.103 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:58:02.461 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:58:02.641 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:58:02.642 [info] 'ViewTool' Tool called with path: server/routes.ts and view_range: undefined
2025-05-24 12:58:03.539 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:58:03.724 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:58:08.935 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:58:09.120 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:58:09.121 [info] 'ViewTool' Tool called with path: server/auth.ts and view_range: undefined
2025-05-24 12:58:10.020 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:58:10.204 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:58:16.752 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:58:16.936 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:58:19.974 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:58:21.549 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:58:21.736 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:58:42.179 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:58:42.363 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:58:42.365 [info] 'ToolFileUtils' Reading file: server/auth.ts
2025-05-24 12:58:42.365 [info] 'ToolFileUtils' Successfully read file: server/auth.ts (7082 bytes)
2025-05-24 12:58:42.542 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:58:43.555 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:58:44.918 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:58:45.201 [info] 'ToolFileUtils' Reading file: server/auth.ts
2025-05-24 12:58:45.202 [info] 'ToolFileUtils' Successfully read file: server/auth.ts (7699 bytes)
2025-05-24 12:58:45.415 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:58:47.624 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/workspaceStorage/28d495afc6b3165b645c0d6d358123ef/Augment.vscode-augment/augment-user-assets/checkpoint-documents/38b76fa8-5500-4f8b-a458-fdb81cd17f53
2025-05-24 12:58:48.390 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:58:48.574 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:58:50.076 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:58:54.100 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:58:54.298 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:58:54.301 [info] 'ViewTool' Tool called with path: server/admin-api.ts and view_range: [130,180]
2025-05-24 12:58:55.360 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:58:55.553 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:59:03.906 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:59:04.100 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:59:04.102 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-05-24 12:59:04.103 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (6922 bytes)
2025-05-24 12:59:04.284 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:59:05.300 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:59:06.038 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:59:06.858 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-05-24 12:59:06.858 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (6949 bytes)
2025-05-24 12:59:07.043 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:59:10.051 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:59:10.241 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:59:19.976 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:59:28.973 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:59:29.158 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:59:29.159 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-05-24 12:59:29.160 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (6949 bytes)
2025-05-24 12:59:29.579 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:59:30.566 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:59:31.070 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:59:31.639 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-05-24 12:59:31.639 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (10219 bytes)
2025-05-24 12:59:31.816 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:59:34.874 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:59:35.086 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:59:49.972 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:59:57.186 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:59:57.376 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:59:57.378 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-05-24 12:59:57.378 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (10219 bytes)
2025-05-24 12:59:57.789 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:59:58.792 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:59:59.287 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 12:59:59.628 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-05-24 12:59:59.629 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (6949 bytes)
2025-05-24 12:59:59.839 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:00:02.870 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:00:03.066 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:00:10.825 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:00:11.010 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:00:11.011 [info] 'ToolFileUtils' Reading file: server/admin-api.ts
2025-05-24 13:00:11.012 [info] 'ToolFileUtils' Successfully read file: server/admin-api.ts (17747 bytes)
2025-05-24 13:00:11.238 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:00:11.428 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:00:16.999 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:00:17.195 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:00:17.197 [info] 'ToolFileUtils' Reading file: server/admin-api.ts
2025-05-24 13:00:17.198 [info] 'ToolFileUtils' Successfully read file: server/admin-api.ts (17747 bytes)
2025-05-24 13:00:17.416 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:00:18.406 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:00:19.168 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:00:19.973 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:00:19.983 [info] 'ToolFileUtils' Reading file: server/admin-api.ts
2025-05-24 13:00:19.983 [info] 'ToolFileUtils' Successfully read file: server/admin-api.ts (17781 bytes)
2025-05-24 13:00:20.202 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:00:23.167 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:00:23.419 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:00:30.451 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:00:30.642 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:00:30.643 [info] 'ToolFileUtils' Reading file: server/admin-api.ts
2025-05-24 13:00:30.644 [info] 'ToolFileUtils' Successfully read file: server/admin-api.ts (17781 bytes)
2025-05-24 13:00:31.021 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:00:32.033 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:00:32.535 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:00:32.916 [info] 'ToolFileUtils' Reading file: server/admin-api.ts
2025-05-24 13:00:32.916 [info] 'ToolFileUtils' Successfully read file: server/admin-api.ts (17747 bytes)
2025-05-24 13:00:33.096 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:00:36.096 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:00:36.287 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:00:49.967 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:00:55.813 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:00:56.006 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:00:56.009 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-05-24 13:00:56.009 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (6949 bytes)
2025-05-24 13:00:56.415 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:00:57.420 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:00:57.916 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:00:58.241 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-05-24 13:00:58.241 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (10027 bytes)
2025-05-24 13:00:58.419 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:01:01.471 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:01:01.717 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:01:18.478 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:01:18.669 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:01:18.670 [info] 'ToolFileUtils' Reading file: server/admin-api.ts
2025-05-24 13:01:18.670 [info] 'ToolFileUtils' Successfully read file: server/admin-api.ts (17747 bytes)
2025-05-24 13:01:18.863 [error] 'ChangeTracker' invalid chunk: 
2025-05-24 13:01:19.045 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:01:19.969 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:01:20.133 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:01:20.561 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:01:20.958 [info] 'ToolFileUtils' Reading file: server/admin-api.ts
2025-05-24 13:01:20.958 [info] 'ToolFileUtils' Successfully read file: server/admin-api.ts (15082 bytes)
2025-05-24 13:01:21.139 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:01:25.136 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:01:25.328 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:01:30.998 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:01:31.191 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:01:32.636 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:01:32.832 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:01:40.582 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:01:40.802 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:01:42.396 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:01:42.588 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:01:47.007 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:01:47.197 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:01:47.460 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:01:47.910 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:01:49.966 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:01:52.310 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:01:52.502 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:01:52.688 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:01:52.884 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:01:56.999 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:01:57.190 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:01:57.461 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:01:57.681 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:02:02.946 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:02:03.137 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:02:18.525 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:02:18.717 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:02:19.966 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:02:25.105 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:02:25.296 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:02:25.696 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:02:25.885 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:02:30.257 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:02:30.447 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:02:30.838 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:02:31.029 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:02:36.121 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:02:36.313 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:02:36.602 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:02:36.794 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:02:43.647 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:02:43.835 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:02:44.234 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:02:44.451 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:02:49.963 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:02:50.297 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:02:50.491 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:02:50.493 [info] 'ViewTool' Tool called with path: server/schema.sql and view_range: [146,155]
2025-05-24 13:02:51.424 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:02:51.801 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:02:57.616 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:02:57.805 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:03:03.104 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:03:03.301 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:03:16.221 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:03:16.419 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:03:18.438 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:03:18.633 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:03:19.967 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:03:24.178 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:03:24.371 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:03:26.293 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:03:26.516 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:03:35.041 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:03:35.232 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:03:37.366 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:03:37.562 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:03:42.479 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:03:42.672 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:03:43.237 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:03:43.478 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:03:45.312 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:03:45.551 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:03:49.973 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:03:52.447 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:03:52.641 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:03:52.927 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:03:53.123 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:04:01.092 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:04:01.288 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:04:01.650 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:04:01.880 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:04:17.641 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:04:18.003 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:04:18.497 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:04:19.973 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:04:43.501 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:04:43.693 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:04:49.966 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:04:53.297 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:05:04.424 [error] 'RemoteAgentsMessenger' Unexpected message type: used-chat
2025-05-24 13:05:04.424 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:05:04.424 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:05:04.459 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:05:04.675 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:05:04.930 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:05:09.532 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:05:09.549 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:05:10.463 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:05:11.537 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:05:11.537 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:05:11.732 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:05:11.736 [info] 'ViewTool' Tool called with path: client/src/pages/auth/AuthPage.tsx and view_range: [50,80]
2025-05-24 13:05:12.620 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:05:12.818 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:05:15.602 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:05:19.966 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:05:24.847 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:05:25.053 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:05:25.057 [info] 'ToolFileUtils' Reading file: client/src/pages/auth/AuthPage.tsx
2025-05-24 13:05:25.057 [info] 'ToolFileUtils' Successfully read file: client/src/pages/auth/AuthPage.tsx (11634 bytes)
2025-05-24 13:05:25.240 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:05:26.302 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:05:26.910 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:05:27.694 [info] 'ToolFileUtils' Reading file: client/src/pages/auth/AuthPage.tsx
2025-05-24 13:05:27.694 [info] 'ToolFileUtils' Successfully read file: client/src/pages/auth/AuthPage.tsx (11469 bytes)
2025-05-24 13:05:27.877 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:05:30.903 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:05:31.097 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:05:41.609 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:05:41.609 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:05:41.609 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:05:41.792 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:05:42.303 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:05:49.978 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:06:19.977 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:06:49.973 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:07:20.004 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:07:49.972 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:08:19.969 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:08:49.982 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:09:19.978 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:09:22.060 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:09:22.236 [error] 'RemoteAgentsMessenger' Unexpected message type: open-file
2025-05-24 13:09:49.978 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:10:02.136 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:10:02.340 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:10:03.136 [error] 'RemoteAgentsMessenger' Unexpected message type: used-chat
2025-05-24 13:10:03.137 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:10:03.137 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:10:03.150 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:10:03.351 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:10:03.635 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:10:05.220 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:10:05.402 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:10:06.206 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:10:06.214 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:10:06.420 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:10:08.303 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:10:08.305 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:10:08.500 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:10:10.754 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:10:14.672 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:10:14.880 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:10:19.971 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:10:20.904 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:10:20.904 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:10:21.638 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:10:21.638 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:10:27.763 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:10:27.968 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:10:27.972 [info] 'ToolFileUtils' Reading file: client/src/pages/auth/LogoutPage.tsx
2025-05-24 13:10:28.509 [info] 'ToolFileUtils' Successfully read file: client/src/pages/auth/LogoutPage.tsx (887 bytes)
2025-05-24 13:10:28.695 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:10:29.746 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:10:30.455 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:10:30.579 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/29081d20
2025-05-24 13:10:31.278 [info] 'ToolFileUtils' Reading file: client/src/pages/auth/LogoutPage.tsx
2025-05-24 13:10:31.278 [info] 'ToolFileUtils' Successfully read file: client/src/pages/auth/LogoutPage.tsx (811 bytes)
2025-05-24 13:10:31.458 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:10:34.521 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:10:34.746 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:10:35.632 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:10:35.945 [error] 'RemoteAgentsMessenger' Unexpected message type: open-file
2025-05-24 13:10:40.002 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:10:40.002 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:10:40.751 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:10:40.751 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:10:46.620 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:10:46.800 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:10:47.302 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:10:48.769 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:10:48.770 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:10:48.770 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:10:48.770 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:10:49.972 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:10:52.546 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:10:52.724 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:11:19.978 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:11:49.972 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:11:56.625 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:11:56.844 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:11:58.664 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:11:58.876 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:12:08.289 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:12:09.804 [error] 'RemoteAgentsMessenger' Unexpected message type: used-chat
2025-05-24 13:12:09.807 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:12:09.807 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:12:09.840 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:12:10.180 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:12:10.306 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:12:15.252 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:12:15.259 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:12:15.508 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:12:17.892 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:12:17.896 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:12:18.094 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:12:19.970 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:12:23.092 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:12:23.327 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:12:25.192 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:12:31.331 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:12:31.535 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:12:37.193 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:12:37.402 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:12:43.398 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:12:43.607 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:12:43.612 [info] 'ViewTool' Tool called with path: client/src/pages/manager/ManagerPage.tsx and view_range: [350,450]
2025-05-24 13:12:44.522 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:12:44.730 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:12:49.982 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:13:11.280 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:13:11.492 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:13:13.346 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:13:13.367 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:13:13.619 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:13:19.978 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:13:31.882 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:13:32.090 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:13:33.952 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:13:33.955 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:13:34.163 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:13:49.970 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:14:13.026 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:14:13.027 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:14:13.027 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:14:13.027 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:14:13.027 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:14:13.027 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:14:13.027 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:14:13.027 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:14:13.027 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:14:13.027 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:14:13.027 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:14:13.027 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:14:13.332 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:14:13.556 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:14:14.398 [info] 'AugmentExtension' Retrieving model config
2025-05-24 13:14:14.737 [info] 'AugmentExtension' Retrieved model config
2025-05-24 13:14:14.737 [info] 'AugmentExtension' Returning model config
2025-05-24 13:14:15.559 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:14:15.559 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:14:15.801 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:14:15.801 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:14:16.016 [error] 'RemoteAgentsMessenger' Unexpected message type: open-file
2025-05-24 13:14:19.973 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:14:25.378 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:14:25.586 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:14:25.591 [info] 'ToolFileUtils' Reading file: client/src/pages/manager/ManagerLayout.tsx
2025-05-24 13:14:26.195 [info] 'ToolFileUtils' Successfully read file: client/src/pages/manager/ManagerLayout.tsx (2653 bytes)
2025-05-24 13:14:26.377 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:14:27.376 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:14:28.002 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:14:28.128 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/7bf52af5
2025-05-24 13:14:28.825 [info] 'ToolFileUtils' Reading file: client/src/pages/manager/ManagerLayout.tsx
2025-05-24 13:14:28.825 [info] 'ToolFileUtils' Successfully read file: client/src/pages/manager/ManagerLayout.tsx (2755 bytes)
2025-05-24 13:14:29.019 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:14:32.269 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:14:33.080 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:14:41.203 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:14:41.410 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:14:41.420 [info] 'ToolFileUtils' Reading file: client/src/pages/manager/ManagerLayout.tsx
2025-05-24 13:14:41.420 [info] 'ToolFileUtils' Successfully read file: client/src/pages/manager/ManagerLayout.tsx (2755 bytes)
2025-05-24 13:14:41.821 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:14:42.815 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:14:43.314 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:14:43.651 [info] 'ToolFileUtils' Reading file: client/src/pages/manager/ManagerLayout.tsx
2025-05-24 13:14:43.651 [info] 'ToolFileUtils' Successfully read file: client/src/pages/manager/ManagerLayout.tsx (2872 bytes)
2025-05-24 13:14:43.832 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:14:46.926 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:14:47.171 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:14:49.989 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:14:58.116 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:14:58.325 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:14:58.329 [info] 'ToolFileUtils' Reading file: client/src/pages/manager/ManagerLayout.tsx
2025-05-24 13:14:58.330 [info] 'ToolFileUtils' Successfully read file: client/src/pages/manager/ManagerLayout.tsx (2872 bytes)
2025-05-24 13:14:58.695 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:14:59.749 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:15:00.273 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:15:00.571 [info] 'ToolFileUtils' Reading file: client/src/pages/manager/ManagerLayout.tsx
2025-05-24 13:15:00.571 [info] 'ToolFileUtils' Successfully read file: client/src/pages/manager/ManagerLayout.tsx (3249 bytes)
2025-05-24 13:15:00.754 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:15:04.270 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:15:04.675 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:15:14.698 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:15:14.909 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:15:14.914 [info] 'ToolFileUtils' Reading file: client/src/pages/manager/ManagerPage.tsx
2025-05-24 13:15:14.914 [info] 'ToolFileUtils' Successfully read file: client/src/pages/manager/ManagerPage.tsx (24944 bytes)
2025-05-24 13:15:15.146 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:15:16.096 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:15:16.800 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:15:17.600 [info] 'ToolFileUtils' Reading file: client/src/pages/manager/ManagerPage.tsx
2025-05-24 13:15:17.601 [info] 'ToolFileUtils' Successfully read file: client/src/pages/manager/ManagerPage.tsx (25087 bytes)
2025-05-24 13:15:17.800 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:15:20.100 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:15:21.282 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:15:21.492 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:15:30.623 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:15:30.838 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:15:30.843 [info] 'ToolFileUtils' Reading file: client/src/pages/manager/ManagerPage.tsx
2025-05-24 13:15:30.843 [info] 'ToolFileUtils' Successfully read file: client/src/pages/manager/ManagerPage.tsx (25087 bytes)
2025-05-24 13:15:31.213 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:15:32.213 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:15:32.817 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:15:33.079 [info] 'ToolFileUtils' Reading file: client/src/pages/manager/ManagerPage.tsx
2025-05-24 13:15:33.079 [info] 'ToolFileUtils' Successfully read file: client/src/pages/manager/ManagerPage.tsx (25262 bytes)
2025-05-24 13:15:33.278 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:15:36.523 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:15:36.737 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:15:49.972 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:15:56.503 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:15:56.716 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:15:56.720 [info] 'ToolFileUtils' Reading file: client/src/pages/manager/ManagerPage.tsx
2025-05-24 13:15:56.721 [info] 'ToolFileUtils' Successfully read file: client/src/pages/manager/ManagerPage.tsx (25262 bytes)
2025-05-24 13:15:57.096 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:15:58.093 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:15:58.913 [info] 'ToolFileUtils' Reading file: client/src/pages/manager/ManagerPage.tsx
2025-05-24 13:15:58.913 [info] 'ToolFileUtils' Successfully read file: client/src/pages/manager/ManagerPage.tsx (27073 bytes)
2025-05-24 13:16:02.097 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:16:02.354 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:16:11.852 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:16:12.061 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:16:12.065 [info] 'ToolFileUtils' Reading file: client/src/pages/manager/ManagerPage.tsx
2025-05-24 13:16:12.065 [info] 'ToolFileUtils' Successfully read file: client/src/pages/manager/ManagerPage.tsx (27073 bytes)
2025-05-24 13:16:12.442 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:16:13.451 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:16:13.945 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:16:14.320 [info] 'ToolFileUtils' Reading file: client/src/pages/manager/ManagerPage.tsx
2025-05-24 13:16:14.320 [info] 'ToolFileUtils' Successfully read file: client/src/pages/manager/ManagerPage.tsx (26891 bytes)
2025-05-24 13:16:14.496 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:16:17.507 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:16:17.719 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:16:19.970 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:16:26.184 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:16:26.398 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:16:26.402 [info] 'ToolFileUtils' Reading file: client/src/App.tsx
2025-05-24 13:16:27.007 [info] 'ToolFileUtils' Successfully read file: client/src/App.tsx (3515 bytes)
2025-05-24 13:16:27.188 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:16:28.192 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:16:28.802 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:16:29.626 [info] 'ToolFileUtils' Reading file: client/src/App.tsx
2025-05-24 13:16:29.626 [info] 'ToolFileUtils' Successfully read file: client/src/App.tsx (3585 bytes)
2025-05-24 13:16:29.806 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:16:32.839 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:16:33.052 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:16:40.830 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:16:41.040 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:16:41.044 [info] 'ToolFileUtils' Reading file: client/src/App.tsx
2025-05-24 13:16:41.044 [info] 'ToolFileUtils' Successfully read file: client/src/App.tsx (3585 bytes)
2025-05-24 13:16:41.447 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:16:42.443 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:16:42.955 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:16:43.483 [info] 'ToolFileUtils' Reading file: client/src/App.tsx
2025-05-24 13:16:43.483 [info] 'ToolFileUtils' Successfully read file: client/src/App.tsx (3662 bytes)
2025-05-24 13:16:43.658 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:16:46.679 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:16:46.885 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:16:49.970 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:16:53.372 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:16:53.584 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:16:55.074 [info] 'WorkspaceManager[workspace]' Directory created: public
2025-05-24 13:16:55.074 [info] 'WorkspaceManager[workspace]' Directory created: public/sounds
2025-05-24 13:16:55.202 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:16:55.413 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:17:09.282 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:17:09.497 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:17:11.620 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:17:11.626 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:17:12.201 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:17:19.973 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:17:26.578 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:17:26.815 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:17:26.821 [info] 'ToolFileUtils' Reading file: client/src/hooks/useAudioNotification.ts
2025-05-24 13:17:26.822 [info] 'ToolFileUtils' Successfully read file: client/src/hooks/useAudioNotification.ts (2911 bytes)
2025-05-24 13:17:27.245 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:17:28.271 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:17:28.633 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/69f10954
2025-05-24 13:17:28.771 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:17:29.279 [info] 'ToolFileUtils' Reading file: client/src/hooks/useAudioNotification.ts
2025-05-24 13:17:29.279 [info] 'ToolFileUtils' Successfully read file: client/src/hooks/useAudioNotification.ts (2242 bytes)
2025-05-24 13:17:29.459 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:17:29.459 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:17:29.676 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:17:49.636 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:17:49.850 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:17:49.854 [info] 'ToolFileUtils' Reading file: client/src/hooks/useAudioNotification.ts
2025-05-24 13:17:49.854 [info] 'ToolFileUtils' Successfully read file: client/src/hooks/useAudioNotification.ts (2242 bytes)
2025-05-24 13:17:49.973 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:17:50.260 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:17:51.296 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:17:51.802 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:17:52.173 [info] 'ToolFileUtils' Reading file: client/src/hooks/useAudioNotification.ts
2025-05-24 13:17:52.174 [info] 'ToolFileUtils' Successfully read file: client/src/hooks/useAudioNotification.ts (2811 bytes)
2025-05-24 13:17:52.379 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:17:52.405 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:17:52.632 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:18:00.867 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:18:01.079 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:18:01.088 [info] 'ToolFileUtils' Reading file: client/src/hooks/useAudioNotification.ts
2025-05-24 13:18:01.089 [info] 'ToolFileUtils' Successfully read file: client/src/hooks/useAudioNotification.ts (2811 bytes)
2025-05-24 13:18:01.496 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:18:02.524 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:18:03.271 [info] 'ToolFileUtils' Reading file: client/src/hooks/useAudioNotification.ts
2025-05-24 13:18:03.271 [info] 'ToolFileUtils' Successfully read file: client/src/hooks/useAudioNotification.ts (2775 bytes)
2025-05-24 13:18:03.460 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:18:03.672 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:18:13.870 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:18:14.086 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:18:14.095 [info] 'ToolFileUtils' Reading file: client/src/hooks/useAudioNotification.ts
2025-05-24 13:18:14.095 [info] 'ToolFileUtils' Successfully read file: client/src/hooks/useAudioNotification.ts (2775 bytes)
2025-05-24 13:18:14.477 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:18:15.483 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:18:15.984 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:18:16.400 [info] 'ToolFileUtils' Reading file: client/src/hooks/useAudioNotification.ts
2025-05-24 13:18:16.401 [info] 'ToolFileUtils' Successfully read file: client/src/hooks/useAudioNotification.ts (2890 bytes)
2025-05-24 13:18:16.579 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:18:19.586 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:18:19.801 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:18:20.107 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:18:20.791 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/logs/20250524T114410/exthost1/vscode.markdown-language-features
2025-05-24 13:18:26.168 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:18:26.494 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:18:27.153 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:18:27.403 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:18:41.177 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:18:41.394 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:18:41.578 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:18:41.792 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:18:49.973 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:18:53.696 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:18:53.909 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:18:54.161 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:18:54.377 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:18:59.418 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:18:59.655 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:18:59.984 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:19:00.224 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:19:06.867 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:19:07.077 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:19:07.474 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:19:07.688 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:19:14.406 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:19:14.407 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:19:19.971 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:19:26.851 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:19:26.851 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:19:26.851 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:19:26.851 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:19:26.851 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:19:26.851 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:19:26.851 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:19:26.851 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:19:27.040 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:19:27.040 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:19:27.194 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:19:27.194 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:19:33.027 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:19:33.027 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:19:33.027 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:19:33.384 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:19:33.890 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:19:47.916 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:19:48.143 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:19:49.984 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:20:19.976 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:20:49.984 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:21:19.977 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:21:49.977 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:22:19.982 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:22:48.455 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:22:48.667 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:22:49.974 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:23:07.639 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:23:19.986 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:23:49.980 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:24:19.976 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:24:49.984 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:25:19.974 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:25:30.532 [error] 'RemoteAgentsMessenger' Unexpected message type: used-chat
2025-05-24 13:25:30.532 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:25:30.532 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:25:30.921 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:25:31.045 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:25:34.260 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:25:34.473 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:25:36.936 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:25:36.940 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:25:37.153 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:25:44.058 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:25:44.278 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:25:46.734 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:25:49.976 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:25:52.433 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:25:52.646 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:25:58.981 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:25:59.206 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:26:19.980 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:26:31.066 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:26:31.312 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:26:32.832 [info] 'WorkspaceManager[workspace]' Directory created: client/src/utils
2025-05-24 13:26:33.176 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:26:33.182 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:26:33.600 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:26:48.470 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:26:48.691 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:26:48.701 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-05-24 13:26:49.266 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (10027 bytes)
2025-05-24 13:26:49.976 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:26:50.068 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:26:51.079 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:26:52.099 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:26:52.905 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-05-24 13:26:52.906 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (10640 bytes)
2025-05-24 13:26:53.086 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:26:56.098 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:26:56.320 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:27:06.476 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:27:06.705 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:27:06.714 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-05-24 13:27:06.714 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (10640 bytes)
2025-05-24 13:27:06.949 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:27:07.174 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:27:12.233 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:27:12.459 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:27:12.464 [info] 'ViewTool' Tool called with path: server/routes.ts and view_range: [1,20]
2025-05-24 13:27:12.820 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:27:13.043 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:27:19.976 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:27:26.213 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:27:26.431 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:27:26.435 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-05-24 13:27:26.436 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (10640 bytes)
2025-05-24 13:27:26.805 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:27:27.816 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:27:28.316 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:27:28.812 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-05-24 13:27:28.813 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (11318 bytes)
2025-05-24 13:27:28.988 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:27:32.078 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:27:32.307 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:27:47.622 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:27:47.842 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:27:47.847 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-05-24 13:27:47.847 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (11318 bytes)
2025-05-24 13:27:48.413 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:27:49.364 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:27:49.977 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:27:50.152 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:27:51.034 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-05-24 13:27:51.035 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (11985 bytes)
2025-05-24 13:27:51.212 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:27:54.225 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:27:54.487 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:28:07.184 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:28:07.409 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:28:07.414 [info] 'ToolFileUtils' Reading file: client/src/pages/manager/ManagerPage.tsx
2025-05-24 13:28:07.951 [info] 'ToolFileUtils' Successfully read file: client/src/pages/manager/ManagerPage.tsx (26891 bytes)
2025-05-24 13:28:08.702 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:28:09.721 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:28:10.206 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:28:10.866 [info] 'ToolFileUtils' Reading file: client/src/pages/manager/ManagerPage.tsx
2025-05-24 13:28:10.866 [info] 'ToolFileUtils' Successfully read file: client/src/pages/manager/ManagerPage.tsx (27069 bytes)
2025-05-24 13:28:11.043 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:28:14.084 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:28:14.308 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:28:19.978 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:28:23.364 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:28:23.586 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:28:23.592 [info] 'ToolFileUtils' Reading file: client/src/pages/manager/ManagerPage.tsx
2025-05-24 13:28:23.593 [info] 'ToolFileUtils' Successfully read file: client/src/pages/manager/ManagerPage.tsx (27069 bytes)
2025-05-24 13:28:23.972 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:28:24.965 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:28:25.467 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:28:25.854 [info] 'ToolFileUtils' Reading file: client/src/pages/manager/ManagerPage.tsx
2025-05-24 13:28:25.855 [info] 'ToolFileUtils' Successfully read file: client/src/pages/manager/ManagerPage.tsx (26852 bytes)
2025-05-24 13:28:26.040 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:28:29.036 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:28:29.259 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:28:49.982 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:28:54.357 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:28:54.579 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:28:54.586 [info] 'ToolFileUtils' Reading file: client/src/pages/manager/ManagerPage.tsx
2025-05-24 13:28:54.586 [info] 'ToolFileUtils' Successfully read file: client/src/pages/manager/ManagerPage.tsx (26852 bytes)
2025-05-24 13:28:54.872 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:28:55.095 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:29:04.148 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:29:04.427 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:29:04.433 [info] 'ViewTool' Tool called with path: client/src/pages/manager/ManagerPage.tsx and view_range: [158,210]
2025-05-24 13:29:04.802 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:29:05.024 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:29:19.976 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:29:31.399 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:29:31.629 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:29:31.636 [info] 'ToolFileUtils' Reading file: client/src/pages/manager/ManagerPage.tsx
2025-05-24 13:29:31.637 [info] 'ToolFileUtils' Successfully read file: client/src/pages/manager/ManagerPage.tsx (26852 bytes)
2025-05-24 13:29:32.129 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:29:33.178 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:29:33.650 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:29:34.179 [info] 'ToolFileUtils' Reading file: client/src/pages/manager/ManagerPage.tsx
2025-05-24 13:29:34.179 [info] 'ToolFileUtils' Successfully read file: client/src/pages/manager/ManagerPage.tsx (27171 bytes)
2025-05-24 13:29:34.403 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:29:37.412 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:29:37.636 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:29:49.571 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:29:49.799 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:29:49.805 [info] 'ToolFileUtils' Reading file: client/src/pages/driver/DriverPage.tsx
2025-05-24 13:29:49.973 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:29:50.406 [info] 'ToolFileUtils' Successfully read file: client/src/pages/driver/DriverPage.tsx (16198 bytes)
2025-05-24 13:29:50.588 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:29:51.584 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:29:52.252 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:29:53.069 [info] 'ToolFileUtils' Reading file: client/src/pages/driver/DriverPage.tsx
2025-05-24 13:29:53.069 [info] 'ToolFileUtils' Successfully read file: client/src/pages/driver/DriverPage.tsx (16486 bytes)
2025-05-24 13:29:53.269 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:29:56.250 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:29:56.476 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:30:03.708 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:30:03.929 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:30:03.934 [info] 'ToolFileUtils' Reading file: client/src/pages/driver/DriverPage.tsx
2025-05-24 13:30:03.934 [info] 'ToolFileUtils' Successfully read file: client/src/pages/driver/DriverPage.tsx (16486 bytes)
2025-05-24 13:30:04.354 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:30:05.328 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:30:06.154 [info] 'ToolFileUtils' Reading file: client/src/pages/driver/DriverPage.tsx
2025-05-24 13:30:06.154 [info] 'ToolFileUtils' Successfully read file: client/src/pages/driver/DriverPage.tsx (16408 bytes)
2025-05-24 13:30:09.363 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:30:09.588 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:30:19.975 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:30:24.860 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:30:25.088 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:30:25.094 [info] 'ToolFileUtils' Reading file: client/src/pages/driver/DriverPage.tsx
2025-05-24 13:30:25.095 [info] 'ToolFileUtils' Successfully read file: client/src/pages/driver/DriverPage.tsx (16408 bytes)
2025-05-24 13:30:25.551 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:30:26.546 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:30:27.051 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:30:27.383 [info] 'ToolFileUtils' Reading file: client/src/pages/driver/DriverPage.tsx
2025-05-24 13:30:27.384 [info] 'ToolFileUtils' Successfully read file: client/src/pages/driver/DriverPage.tsx (16565 bytes)
2025-05-24 13:30:27.580 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:30:30.607 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:30:30.866 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:30:41.171 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:30:41.400 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:30:41.411 [info] 'ToolFileUtils' Reading file: client/src/pages/driver/DriverPage.tsx
2025-05-24 13:30:41.411 [info] 'ToolFileUtils' Successfully read file: client/src/pages/driver/DriverPage.tsx (16565 bytes)
2025-05-24 13:30:41.849 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:30:42.780 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:30:43.498 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:30:44.319 [info] 'ToolFileUtils' Reading file: client/src/pages/driver/DriverPage.tsx
2025-05-24 13:30:44.320 [info] 'ToolFileUtils' Successfully read file: client/src/pages/driver/DriverPage.tsx (16363 bytes)
2025-05-24 13:30:44.964 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:30:47.501 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:30:47.726 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:30:49.980 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:30:58.323 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:30:58.547 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:30:58.552 [info] 'ToolFileUtils' Reading file: client/src/pages/driver/DriverPage.tsx
2025-05-24 13:30:58.552 [info] 'ToolFileUtils' Successfully read file: client/src/pages/driver/DriverPage.tsx (16363 bytes)
2025-05-24 13:30:58.951 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:31:00.033 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:31:00.746 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:31:01.218 [info] 'ToolFileUtils' Reading file: client/src/pages/driver/DriverPage.tsx
2025-05-24 13:31:01.218 [info] 'ToolFileUtils' Successfully read file: client/src/pages/driver/DriverPage.tsx (16538 bytes)
2025-05-24 13:31:01.394 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:31:04.404 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:31:04.628 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:31:19.978 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:31:43.576 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:31:43.809 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:31:43.817 [info] 'ToolFileUtils' Reading file: client/src/pages/driver/DriverPage.tsx
2025-05-24 13:31:43.818 [info] 'ToolFileUtils' Successfully read file: client/src/pages/driver/DriverPage.tsx (16538 bytes)
2025-05-24 13:31:44.238 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:31:45.212 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:31:45.722 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:31:46.199 [info] 'ToolFileUtils' Reading file: client/src/pages/driver/DriverPage.tsx
2025-05-24 13:31:46.200 [info] 'ToolFileUtils' Successfully read file: client/src/pages/driver/DriverPage.tsx (18350 bytes)
2025-05-24 13:31:46.399 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:31:49.773 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:31:50.000 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:31:50.000 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:32:03.706 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:32:03.937 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:32:03.942 [info] 'ToolFileUtils' Reading file: client/src/pages/driver/DriverPage.tsx
2025-05-24 13:32:03.942 [info] 'ToolFileUtils' Successfully read file: client/src/pages/driver/DriverPage.tsx (18350 bytes)
2025-05-24 13:32:04.271 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:32:04.492 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:32:13.611 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:32:13.839 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:32:13.845 [info] 'ToolFileUtils' Reading file: client/src/pages/driver/DriverPage.tsx
2025-05-24 13:32:13.845 [info] 'ToolFileUtils' Successfully read file: client/src/pages/driver/DriverPage.tsx (18350 bytes)
2025-05-24 13:32:14.381 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:32:15.423 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:32:16.594 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:32:17.379 [info] 'ToolFileUtils' Reading file: client/src/pages/driver/DriverPage.tsx
2025-05-24 13:32:17.379 [info] 'ToolFileUtils' Successfully read file: client/src/pages/driver/DriverPage.tsx (18342 bytes)
2025-05-24 13:32:18.015 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:32:19.976 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:32:20.562 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:32:20.846 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:32:29.250 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:32:29.487 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:32:29.497 [info] 'ToolFileUtils' Reading file: client/src/pages/driver/DriverLayout.tsx
2025-05-24 13:32:30.033 [info] 'ToolFileUtils' Successfully read file: client/src/pages/driver/DriverLayout.tsx (2125 bytes)
2025-05-24 13:32:30.268 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:32:31.289 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:32:32.746 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:32:32.874 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/26d44819
2025-05-24 13:32:33.565 [info] 'ToolFileUtils' Reading file: client/src/pages/driver/DriverLayout.tsx
2025-05-24 13:32:33.565 [info] 'ToolFileUtils' Successfully read file: client/src/pages/driver/DriverLayout.tsx (2238 bytes)
2025-05-24 13:32:34.017 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:32:36.774 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:32:37.005 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:32:41.610 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:32:41.842 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:32:41.853 [info] 'ViewTool' Tool called with path: client/src/pages/driver/DriverLayout.tsx and view_range: [10,50]
2025-05-24 13:32:42.209 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:32:42.443 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:32:49.975 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:32:50.632 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:32:50.897 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:32:50.913 [info] 'ToolFileUtils' Reading file: client/src/pages/driver/DriverLayout.tsx
2025-05-24 13:32:50.913 [info] 'ToolFileUtils' Successfully read file: client/src/pages/driver/DriverLayout.tsx (2238 bytes)
2025-05-24 13:32:51.421 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:32:52.372 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:32:54.228 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:32:54.583 [info] 'ToolFileUtils' Reading file: client/src/pages/driver/DriverLayout.tsx
2025-05-24 13:32:54.583 [info] 'ToolFileUtils' Successfully read file: client/src/pages/driver/DriverLayout.tsx (2355 bytes)
2025-05-24 13:32:55.097 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:32:57.817 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:32:58.051 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:33:11.418 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:33:11.679 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:33:11.686 [info] 'ToolFileUtils' Reading file: client/src/pages/driver/DriverLayout.tsx
2025-05-24 13:33:11.686 [info] 'ToolFileUtils' Successfully read file: client/src/pages/driver/DriverLayout.tsx (2355 bytes)
2025-05-24 13:33:12.058 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:33:13.067 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:33:13.572 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:33:14.063 [info] 'ToolFileUtils' Reading file: client/src/pages/driver/DriverLayout.tsx
2025-05-24 13:33:14.064 [info] 'ToolFileUtils' Successfully read file: client/src/pages/driver/DriverLayout.tsx (3236 bytes)
2025-05-24 13:33:14.248 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:33:17.337 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:33:17.562 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:33:19.976 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:33:22.899 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:33:23.124 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:33:23.130 [info] 'ViewTool' Tool called with path: client/src/components/OrderTracker.tsx and view_range: [1,50]
2025-05-24 13:33:23.310 [info] 'ViewTool' Path does not exist: client/src/components/OrderTracker.tsx
2025-05-24 13:33:23.973 [info] 'ToolFileUtils' File not found: client/src/components/OrderTracker.tsx. Similar files found: client/src/pages/OrderTracker.tsx
2025-05-24 13:33:24.155 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:33:24.380 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:33:33.520 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:33:33.762 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:33:33.769 [info] 'ToolFileUtils' Reading file: client/src/pages/OrderTracker.tsx
2025-05-24 13:33:34.323 [info] 'ToolFileUtils' Successfully read file: client/src/pages/OrderTracker.tsx (18442 bytes)
2025-05-24 13:33:34.589 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:33:34.812 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:33:40.033 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:33:40.261 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:33:40.268 [info] 'ViewTool' Tool called with path: client/src/pages/OrderTracker.tsx and view_range: [1,100]
2025-05-24 13:33:40.631 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:33:40.922 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:33:49.980 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:34:19.983 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:34:33.692 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:34:33.929 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:34:35.783 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:34:35.805 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:34:36.244 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:34:38.639 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:34:38.860 [error] 'RemoteAgentsMessenger' Unexpected message type: open-file
2025-05-24 13:34:49.980 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:34:51.871 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:34:52.113 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:34:52.123 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-05-24 13:34:52.872 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (11985 bytes)
2025-05-24 13:34:53.117 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:34:53.350 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:35:07.224 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:35:07.454 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:35:07.460 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-05-24 13:35:07.461 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (11985 bytes)
2025-05-24 13:35:08.198 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:35:09.192 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:35:09.701 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:35:10.143 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-05-24 13:35:10.143 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (13115 bytes)
2025-05-24 13:35:10.333 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:35:14.318 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:35:14.872 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:35:19.981 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:35:25.185 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:35:25.423 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:35:25.430 [info] 'ToolFileUtils' Reading file: client/src/pages/manager/ManagerPage.tsx
2025-05-24 13:35:26.001 [info] 'ToolFileUtils' Successfully read file: client/src/pages/manager/ManagerPage.tsx (27171 bytes)
2025-05-24 13:35:26.812 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:35:27.752 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:35:28.289 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:35:28.667 [info] 'ToolFileUtils' Reading file: client/src/pages/manager/ManagerPage.tsx
2025-05-24 13:35:28.667 [info] 'ToolFileUtils' Successfully read file: client/src/pages/manager/ManagerPage.tsx (27205 bytes)
2025-05-24 13:35:28.866 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:35:32.936 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:35:33.819 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:35:49.747 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:35:49.984 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:35:49.986 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:35:49.992 [info] 'ToolFileUtils' Reading file: client/src/pages/manager/ManagerPage.tsx
2025-05-24 13:35:49.992 [info] 'ToolFileUtils' Successfully read file: client/src/pages/manager/ManagerPage.tsx (27205 bytes)
2025-05-24 13:35:50.242 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:35:50.484 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:35:57.201 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:35:57.441 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:35:57.446 [info] 'ViewTool' Tool called with path: client/src/pages/manager/ManagerPage.tsx and view_range: [500,550]
2025-05-24 13:35:57.811 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:35:58.044 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:36:16.437 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:36:16.699 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:36:16.732 [info] 'ToolFileUtils' Reading file: client/src/pages/manager/ManagerPage.tsx
2025-05-24 13:36:16.732 [info] 'ToolFileUtils' Successfully read file: client/src/pages/manager/ManagerPage.tsx (27205 bytes)
2025-05-24 13:36:17.240 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:36:18.238 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:36:19.036 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:36:19.830 [info] 'ToolFileUtils' Reading file: client/src/pages/manager/ManagerPage.tsx
2025-05-24 13:36:19.830 [info] 'ToolFileUtils' Successfully read file: client/src/pages/manager/ManagerPage.tsx (27679 bytes)
2025-05-24 13:36:19.980 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:36:20.212 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:36:23.653 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:36:24.447 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:36:30.564 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:36:30.809 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:36:31.098 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:36:31.334 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:36:38.729 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:36:38.964 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:36:39.329 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:36:39.563 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:36:49.980 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:36:56.155 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:36:56.155 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:36:56.155 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:36:56.155 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:36:56.155 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:36:56.155 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:36:56.254 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:36:56.255 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:37:00.844 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:37:00.845 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:37:00.845 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:37:00.846 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:37:00.846 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:37:00.846 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:37:00.847 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:37:00.847 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:37:00.847 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:37:00.848 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:37:00.848 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:37:00.848 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:37:00.848 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:37:00.849 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:37:00.863 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:37:00.864 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:37:00.864 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:37:00.864 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:37:01.082 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:37:01.082 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:37:01.186 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:37:01.186 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:37:01.187 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:37:01.187 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:37:08.524 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:37:08.524 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:37:08.524 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:37:09.417 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:37:09.916 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:37:19.984 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:37:49.988 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:38:19.986 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:38:49.978 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:39:04.821 [error] 'AugmentExtension' API request e15fc903-c067-4ac3-8ec9-05e9b20d1fd9 to https://i1.api.augmentcode.com/find-missing response 503: Service Unavailable
2025-05-24 13:39:05.166 [info] 'DiskFileManager[workspace]' Operation failed with error Error: HTTP error: 503 Service Unavailable, retrying in 100 ms; retries = 0
2025-05-24 13:39:05.775 [error] 'AugmentExtension' API request 43ce6b7a-809b-4c37-83ee-b8c6ec65434a to https://i1.api.augmentcode.com/find-missing response 503: Service Unavailable
2025-05-24 13:39:06.073 [info] 'DiskFileManager[workspace]' Operation failed with error Error: HTTP error: 503 Service Unavailable, retrying in 200 ms; retries = 1
2025-05-24 13:39:06.648 [info] 'DiskFileManager[workspace]' Operation succeeded after 2 transient failures
2025-05-24 13:39:19.978 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:39:49.986 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:40:19.983 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:40:49.978 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:41:19.980 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:41:49.979 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:42:19.978 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:42:50.002 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:43:19.991 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:43:49.987 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:44:14.398 [info] 'AugmentExtension' Retrieving model config
2025-05-24 13:44:14.716 [info] 'AugmentExtension' Retrieved model config
2025-05-24 13:44:14.716 [info] 'AugmentExtension' Returning model config
2025-05-24 13:44:19.979 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:44:49.992 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:45:19.990 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:45:49.989 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:46:19.979 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:46:49.993 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:47:19.987 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:47:49.987 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:48:19.984 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:48:49.990 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:49:19.994 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:49:49.989 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:50:19.980 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:50:49.980 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:51:19.982 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:51:49.986 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:52:19.988 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:52:49.992 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:53:19.990 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:53:49.982 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:54:19.983 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:54:49.998 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:55:19.983 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:55:49.989 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:56:13.455 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:56:13.679 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:56:14.881 [error] 'RemoteAgentsMessenger' Unexpected message type: used-chat
2025-05-24 13:56:14.881 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:56:14.881 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:56:14.911 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:56:15.136 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:56:15.386 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:56:19.990 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:56:21.192 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:56:21.209 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:56:21.849 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:56:24.166 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:56:24.168 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:56:24.419 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:56:24.433 [info] 'ViewTool' Tool called with path: server/routes.ts and view_range: [140,160]
2025-05-24 13:56:25.429 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:56:25.677 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:56:26.811 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:56:41.054 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:56:41.291 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:56:41.299 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-05-24 13:56:41.299 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (13115 bytes)
2025-05-24 13:56:42.063 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:56:43.084 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:56:43.590 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:56:44.000 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-05-24 13:56:44.000 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (13171 bytes)
2025-05-24 13:56:44.176 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:56:47.691 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:56:47.967 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:56:49.987 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:56:55.764 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:56:56.016 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:57:04.454 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:57:04.691 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:57:12.256 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:57:12.257 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:57:12.436 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:57:12.437 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:57:13.799 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:57:14.041 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:57:14.352 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:57:14.599 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:57:19.997 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:57:23.484 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:57:23.737 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:57:23.749 [info] 'ViewTool' Tool called with path: server/routes.ts and view_range: [162,185]
2025-05-24 13:57:24.112 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:57:24.361 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:57:36.944 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:57:37.210 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:57:42.152 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:57:42.410 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:57:49.990 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:57:51.474 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:57:51.768 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:57:53.462 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:57:53.728 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:58:00.514 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:58:00.763 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:58:02.432 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:58:02.684 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:58:07.448 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:58:07.695 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:58:08.045 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:58:08.306 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:58:15.296 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:58:15.549 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:58:15.817 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:58:16.097 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:58:19.984 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:58:20.593 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:58:20.893 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:58:21.117 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:58:21.378 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:58:26.259 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:58:26.546 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:58:26.804 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:58:27.091 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:58:32.030 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:58:32.285 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:58:32.475 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:58:32.764 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:58:38.530 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:58:38.785 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:58:39.066 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:58:39.323 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:58:43.372 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:58:43.614 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:58:43.801 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:58:44.062 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:58:48.780 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:58:49.032 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:58:49.305 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:58:49.581 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:58:49.990 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:58:54.260 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:58:54.519 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:58:54.708 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:58:54.952 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:59:08.752 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:59:08.999 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:59:09.008 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-05-24 13:59:09.008 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (13171 bytes)
2025-05-24 13:59:09.379 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:59:10.377 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:59:10.882 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:59:11.290 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-05-24 13:59:11.291 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (13565 bytes)
2025-05-24 13:59:11.473 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:59:14.480 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:59:14.738 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:59:19.990 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:59:20.878 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:59:21.140 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:59:21.380 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:59:21.636 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:59:26.180 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:59:26.429 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:59:36.841 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:59:37.095 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:59:42.718 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:59:42.966 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:59:43.330 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:59:43.583 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:59:49.988 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:59:51.224 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:59:51.224 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:59:53.936 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:59:53.936 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:59:54.216 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:59:54.217 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:59:55.802 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:59:55.803 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:59:55.803 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:59:55.803 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:59:55.858 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:59:55.858 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:59:56.406 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:59:56.407 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:59:59.481 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 13:59:59.481 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:00:04.982 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:00:04.994 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:00:04.994 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:00:04.995 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:00:04.995 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:00:04.996 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:00:04.996 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:00:04.998 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:00:04.998 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:00:04.999 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:00:04.999 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:00:05.000 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:00:05.000 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:00:05.000 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:00:05.001 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:00:05.001 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:00:05.001 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:00:05.002 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:00:05.002 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:00:05.003 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:00:05.003 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:00:05.004 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:00:09.978 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:00:10.356 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:00:10.990 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:00:19.986 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:00:35.642 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:00:35.828 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:00:49.558 [error] 'RemoteAgentsMessenger' Unexpected message type: used-chat
2025-05-24 14:00:49.558 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:00:49.558 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:00:49.641 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:00:49.996 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:00:50.000 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:00:50.060 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:00:54.653 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:00:54.663 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:00:55.080 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:00:58.402 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:00:58.406 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:00:58.671 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:00:58.732 [info] 'ViewTool' Tool called with path: server/storage.ts and view_range: [1400,1420]
2025-05-24 14:00:59.694 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:00:59.994 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:01:00.629 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:01:00.629 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:01:01.456 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:01:05.507 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:01:05.507 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:01:06.244 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:01:06.245 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:01:13.098 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:01:13.375 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:01:13.388 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-05-24 14:01:14.011 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (13565 bytes)
2025-05-24 14:01:14.767 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:01:15.764 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:01:16.272 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:01:16.681 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-05-24 14:01:16.681 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (14283 bytes)
2025-05-24 14:01:16.862 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:01:19.865 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:01:19.990 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:01:20.137 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:01:34.712 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:01:34.987 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:01:34.997 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-05-24 14:01:34.997 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (14283 bytes)
2025-05-24 14:01:35.382 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:01:36.377 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:01:36.879 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:01:37.225 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-05-24 14:01:37.225 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (14566 bytes)
2025-05-24 14:01:37.409 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:01:40.428 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:01:40.704 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:01:49.990 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:01:51.456 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:01:51.727 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:01:51.736 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-05-24 14:01:51.736 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (14566 bytes)
2025-05-24 14:01:52.109 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:01:53.106 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:01:53.608 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:01:53.959 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-05-24 14:01:53.959 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (14825 bytes)
2025-05-24 14:01:54.138 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:01:57.160 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:01:57.427 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:02:09.150 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:02:09.469 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:02:09.478 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-05-24 14:02:09.478 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (14825 bytes)
2025-05-24 14:02:09.893 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:02:10.881 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:02:11.392 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:02:11.733 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-05-24 14:02:11.734 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (15002 bytes)
2025-05-24 14:02:11.922 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:02:14.965 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:02:15.234 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:02:19.990 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:02:21.894 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:02:22.171 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:02:22.469 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:02:22.736 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:02:28.458 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:02:28.720 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:02:29.025 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:02:29.328 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:02:34.464 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:02:34.779 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:02:35.115 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:02:35.436 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:02:46.680 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:02:46.995 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:02:47.185 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:02:47.487 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:02:49.982 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:02:52.695 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:02:52.999 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:02:53.282 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:02:53.555 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:02:57.914 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:02:58.206 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:02:58.396 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:02:58.666 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:03:04.016 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:03:04.283 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:03:04.556 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:03:04.819 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:03:10.146 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:03:10.455 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:03:10.677 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:03:10.945 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:03:16.400 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:03:16.665 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:03:16.719 [info] 'ViewTool' Tool called with path: package.json and view_range: [1,30]
2025-05-24 14:03:17.626 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:03:17.901 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:03:19.992 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:03:24.452 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:03:24.721 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:03:25.025 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:03:25.293 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:03:30.568 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:03:30.832 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:03:31.017 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:03:31.276 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:03:38.188 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:03:38.475 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:03:40.172 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:03:40.448 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:03:47.246 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:03:47.519 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:03:47.831 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:03:48.098 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:03:49.991 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:03:52.594 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:03:52.910 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:03:53.105 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:03:53.435 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:03:59.586 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:03:59.865 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:04:01.753 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:04:02.051 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:04:09.024 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:04:09.295 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:04:12.030 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:04:12.302 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:04:17.748 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:04:18.036 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:04:19.998 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:04:29.331 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:04:29.616 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:04:34.520 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:04:34.799 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:04:35.054 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:04:35.331 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:04:43.164 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:04:43.436 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:04:43.794 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:04:44.140 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:04:49.869 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:04:50.002 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:04:50.172 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:04:50.365 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:04:50.648 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:04:55.100 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:04:55.371 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:04:55.676 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:04:55.947 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:05:00.168 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:05:00.439 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:05:00.731 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:05:01.012 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:05:05.858 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:05:06.117 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:05:06.419 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:05:06.684 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:05:13.354 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:05:13.643 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:05:13.978 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:05:14.247 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:05:20.057 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:05:22.927 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:05:23.616 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:05:34.951 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:05:35.239 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:05:40.076 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:05:40.363 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:05:49.994 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:05:50.770 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:05:51.035 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:05:55.030 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:05:55.300 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:05:55.588 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:05:55.863 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:06:00.700 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:06:01.004 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:06:01.338 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:06:01.593 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:06:09.120 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:06:09.120 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:06:09.264 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:06:09.265 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:06:09.410 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:06:09.428 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:06:09.446 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:06:09.446 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:06:11.904 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:06:12.171 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:06:13.870 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:06:14.150 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:06:19.999 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:06:25.638 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:06:25.895 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:06:25.910 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-05-24 14:06:26.472 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (15002 bytes)
2025-05-24 14:06:27.207 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:06:28.220 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:06:28.726 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:06:29.313 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-05-24 14:06:29.314 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (15261 bytes)
2025-05-24 14:06:29.500 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:06:32.547 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:06:32.822 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:06:37.225 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:06:37.483 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:06:39.173 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:06:39.439 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:06:44.028 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:06:44.299 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:06:44.590 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:06:44.846 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:06:49.986 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:06:50.692 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:06:50.951 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:06:51.186 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:06:51.486 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:06:56.942 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:06:57.238 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:06:57.491 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:06:57.748 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:07:02.290 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:07:02.595 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:07:02.862 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:07:03.123 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:07:07.885 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:07:08.155 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:07:08.368 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:07:08.627 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:07:14.136 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:07:14.393 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:07:14.403 [info] 'ViewTool' Tool called with path: server/index.ts and view_range: [1,50]
2025-05-24 14:07:15.496 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:07:15.761 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:07:19.992 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:07:22.632 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:07:22.914 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:07:23.159 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:07:23.418 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:07:29.336 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:07:29.603 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:07:29.819 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:07:30.073 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:07:35.027 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:07:35.291 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:07:35.562 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:07:35.824 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:07:39.882 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:07:40.147 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:07:49.992 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:07:50.602 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:07:50.868 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:07:57.127 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:07:57.392 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:07:59.065 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:07:59.385 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:08:06.887 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:08:06.887 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:08:07.253 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:08:07.253 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:08:07.299 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:08:07.299 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:08:07.526 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:08:07.526 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:08:07.930 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:08:08.190 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:08:09.952 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:08:10.208 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:08:19.986 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:08:29.152 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:08:29.426 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:08:29.437 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-05-24 14:08:29.438 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (15261 bytes)
2025-05-24 14:08:29.850 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:08:30.837 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:08:31.345 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:08:31.826 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-05-24 14:08:31.826 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (14797 bytes)
2025-05-24 14:08:32.002 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:08:35.303 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:08:36.891 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:08:46.358 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:08:46.626 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:08:49.994 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:08:57.928 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:08:58.234 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:09:00.220 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:09:01.343 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:09:01.810 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:09:10.115 [error] 'RemoteAgentsMessenger' Unexpected message type: used-chat
2025-05-24 14:09:10.115 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:09:10.115 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:09:10.573 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:09:10.618 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:09:15.043 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:09:15.048 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:09:15.325 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:09:19.993 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:09:25.784 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:09:26.047 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:09:30.086 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:09:30.353 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:09:30.721 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:09:30.984 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:09:35.443 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:09:35.711 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:09:36.010 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:09:36.270 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:09:42.192 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:09:42.192 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:09:42.392 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:09:42.393 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:09:42.537 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:09:42.537 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:09:42.537 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:09:42.538 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:09:42.826 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:09:42.827 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:09:42.827 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:09:42.827 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:09:42.924 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:09:42.925 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:09:43.128 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:09:43.129 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:09:43.276 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:09:43.277 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:09:43.277 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:09:43.277 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:09:43.561 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:09:43.561 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:09:43.561 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:09:43.561 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:09:45.570 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:09:45.833 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:09:47.553 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:09:47.814 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:09:49.994 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:09:53.189 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:09:53.460 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:09:55.559 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:09:55.822 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:09:59.482 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:09:59.748 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:09:59.943 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:10:00.199 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:10:05.141 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:10:05.420 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:10:07.459 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:10:07.723 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:10:14.839 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:10:14.840 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:10:14.840 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:10:14.840 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:10:14.840 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:10:14.840 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:10:14.840 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:10:14.840 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:10:14.840 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:10:14.841 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:10:14.841 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:10:14.842 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:10:14.842 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:10:14.842 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:10:14.973 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:10:14.973 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:10:15.015 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:10:15.015 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:10:16.824 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:10:16.824 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:10:17.962 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:10:17.962 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:10:19.986 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:10:20.732 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:10:20.732 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:10:20.732 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:10:20.732 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:10:24.630 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:10:25.654 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:10:25.831 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:10:32.890 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:10:33.165 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:10:41.665 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:10:49.926 [error] 'RemoteAgentsMessenger' Unexpected message type: used-chat
2025-05-24 14:10:49.927 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:10:50.005 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:10:50.186 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:10:53.227 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:10:53.487 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:10:55.938 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:10:55.943 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:10:56.204 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:10:59.143 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:11:01.218 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:11:01.489 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:11:08.300 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:11:08.588 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:11:13.617 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:11:13.898 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:11:19.122 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:11:19.122 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:11:19.442 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:11:19.442 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:11:19.872 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:11:19.872 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:11:19.888 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:11:19.888 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:11:19.997 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:11:20.227 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:11:20.227 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:11:20.294 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:11:20.294 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:11:20.637 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:11:20.637 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:11:20.894 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:11:20.894 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:11:21.039 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:11:21.039 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:11:21.642 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:11:21.642 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:11:25.844 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:11:26.112 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:11:26.120 [info] 'ToolFileUtils' Reading file: client/src/pages/OrderConfirmation.tsx
2025-05-24 14:11:26.676 [info] 'ToolFileUtils' Successfully read file: client/src/pages/OrderConfirmation.tsx (10164 bytes)
2025-05-24 14:11:29.859 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:11:30.127 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:11:38.542 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:11:38.828 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:11:38.836 [info] 'ToolFileUtils' Reading file: client/src/pages/OrderConfirmation.tsx
2025-05-24 14:11:38.837 [info] 'ToolFileUtils' Successfully read file: client/src/pages/OrderConfirmation.tsx (10164 bytes)
2025-05-24 14:11:39.022 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:11:40.022 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:11:40.792 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:11:40.909 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/-130fa93d
2025-05-24 14:11:41.605 [info] 'ToolFileUtils' Reading file: client/src/pages/OrderConfirmation.tsx
2025-05-24 14:11:41.605 [info] 'ToolFileUtils' Successfully read file: client/src/pages/OrderConfirmation.tsx (10083 bytes)
2025-05-24 14:11:41.786 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:11:44.792 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:11:45.062 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:11:50.075 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:11:55.479 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:11:55.479 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:11:57.554 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:11:57.817 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:11:57.878 [info] 'ToolFileUtils' Reading file: client/src/pages/OrderConfirmation.tsx
2025-05-24 14:11:57.878 [info] 'ToolFileUtils' Successfully read file: client/src/pages/OrderConfirmation.tsx (10083 bytes)
2025-05-24 14:11:58.245 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:11:59.242 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:11:59.747 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:12:00.138 [info] 'ToolFileUtils' Reading file: client/src/pages/OrderConfirmation.tsx
2025-05-24 14:12:00.139 [info] 'ToolFileUtils' Successfully read file: client/src/pages/OrderConfirmation.tsx (11309 bytes)
2025-05-24 14:12:00.361 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:12:03.359 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:12:03.624 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:12:19.996 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:12:33.930 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:12:34.203 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:12:34.211 [info] 'ToolFileUtils' Reading file: client/src/pages/OrderConfirmation.tsx
2025-05-24 14:12:34.211 [info] 'ToolFileUtils' Successfully read file: client/src/pages/OrderConfirmation.tsx (11309 bytes)
2025-05-24 14:12:34.466 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:12:34.746 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:12:40.388 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:12:40.686 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:12:40.700 [info] 'ViewTool' Tool called with path: client/src/pages/OrderConfirmation.tsx and view_range: [250,300]
2025-05-24 14:12:41.087 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:12:41.351 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:12:42.516 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:12:42.697 [error] 'RemoteAgentsMessenger' Unexpected message type: open-file
2025-05-24 14:12:49.986 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:13:10.940 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:13:11.216 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:13:11.224 [info] 'ToolFileUtils' Reading file: client/src/pages/OrderConfirmation.tsx
2025-05-24 14:13:11.224 [info] 'ToolFileUtils' Successfully read file: client/src/pages/OrderConfirmation.tsx (11309 bytes)
2025-05-24 14:13:11.682 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:13:12.726 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:13:13.228 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:13:13.522 [info] 'ToolFileUtils' Reading file: client/src/pages/OrderConfirmation.tsx
2025-05-24 14:13:13.522 [info] 'ToolFileUtils' Successfully read file: client/src/pages/OrderConfirmation.tsx (14159 bytes)
2025-05-24 14:13:13.701 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:13:13.701 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:13:14.000 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:13:19.988 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:13:22.696 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:13:22.696 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:13:23.285 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:13:23.285 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:13:25.376 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:13:25.645 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:13:25.656 [info] 'ToolFileUtils' Reading file: client/src/App.tsx
2025-05-24 14:13:26.196 [info] 'ToolFileUtils' Successfully read file: client/src/App.tsx (3662 bytes)
2025-05-24 14:13:26.924 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:13:27.951 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:13:28.462 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:13:28.926 [info] 'ToolFileUtils' Reading file: client/src/App.tsx
2025-05-24 14:13:28.926 [info] 'ToolFileUtils' Successfully read file: client/src/App.tsx (3719 bytes)
2025-05-24 14:13:29.102 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:13:32.241 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:13:33.406 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:13:42.486 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:13:42.758 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:13:42.826 [info] 'ToolFileUtils' Reading file: client/src/App.tsx
2025-05-24 14:13:42.834 [info] 'ToolFileUtils' Successfully read file: client/src/App.tsx (3719 bytes)
2025-05-24 14:13:43.364 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:13:44.362 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:13:44.867 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:13:45.205 [info] 'ToolFileUtils' Reading file: client/src/App.tsx
2025-05-24 14:13:45.205 [info] 'ToolFileUtils' Successfully read file: client/src/App.tsx (3798 bytes)
2025-05-24 14:13:45.382 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:13:48.426 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:13:49.278 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:13:50.010 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:14:02.173 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:14:02.441 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:14:02.454 [info] 'ToolFileUtils' Reading file: client/src/pages/OrderConfirmation.tsx
2025-05-24 14:14:02.454 [info] 'ToolFileUtils' Successfully read file: client/src/pages/OrderConfirmation.tsx (14159 bytes)
2025-05-24 14:14:02.677 [error] 'ChangeTracker' invalid chunk: 
2025-05-24 14:14:02.854 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:14:03.862 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:14:04.364 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:14:04.689 [info] 'ToolFileUtils' Reading file: client/src/pages/OrderConfirmation.tsx
2025-05-24 14:14:04.690 [info] 'ToolFileUtils' Successfully read file: client/src/pages/OrderConfirmation.tsx (14152 bytes)
2025-05-24 14:14:04.868 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:14:07.931 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:14:08.198 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:14:14.462 [info] 'AugmentExtension' Retrieving model config
2025-05-24 14:14:15.027 [info] 'AugmentExtension' Retrieved model config
2025-05-24 14:14:15.027 [info] 'AugmentExtension' Returning model config
2025-05-24 14:14:19.988 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:14:39.666 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:14:39.950 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:14:41.780 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:14:41.791 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:14:42.275 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:14:49.990 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:14:50.215 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:14:50.483 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:14:50.509 [info] 'ToolFileUtils' Reading file: client/src/App.tsx
2025-05-24 14:14:50.509 [info] 'ToolFileUtils' Successfully read file: client/src/App.tsx (3798 bytes)
2025-05-24 14:14:50.882 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:14:51.919 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:14:52.482 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:14:52.815 [info] 'ToolFileUtils' Reading file: client/src/App.tsx
2025-05-24 14:14:52.815 [info] 'ToolFileUtils' Successfully read file: client/src/App.tsx (3845 bytes)
2025-05-24 14:14:52.998 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:14:56.337 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:14:56.706 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:15:03.404 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:15:03.671 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:15:03.679 [info] 'ToolFileUtils' Reading file: client/src/App.tsx
2025-05-24 14:15:03.679 [info] 'ToolFileUtils' Successfully read file: client/src/App.tsx (3845 bytes)
2025-05-24 14:15:04.067 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:15:05.068 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:15:05.572 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:15:05.927 [info] 'ToolFileUtils' Reading file: client/src/App.tsx
2025-05-24 14:15:05.927 [info] 'ToolFileUtils' Successfully read file: client/src/App.tsx (3909 bytes)
2025-05-24 14:15:06.102 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:15:09.593 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:15:10.362 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:15:16.826 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:15:17.101 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:15:17.388 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:15:17.659 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:15:19.996 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:15:25.230 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:15:25.230 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:15:28.556 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:15:28.826 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:15:40.106 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:15:40.386 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:15:44.976 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:15:45.254 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:15:49.998 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:15:55.673 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:15:55.959 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:16:00.300 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:16:00.575 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:16:00.898 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:16:01.172 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:16:06.526 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:16:06.794 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:16:07.084 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:16:07.361 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:16:15.678 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:16:15.959 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:16:17.716 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:16:17.991 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:16:19.996 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:16:27.940 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:16:28.216 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:16:30.304 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:16:30.585 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:16:34.222 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:16:34.503 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:16:34.723 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:16:34.995 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:16:43.298 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:16:43.567 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:16:43.580 [info] 'ViewTool' Tool called with path: client/src/pages/RealOrderTracker.tsx and view_range: [1,50]
2025-05-24 14:16:44.474 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:16:44.750 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:16:49.998 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:16:58.534 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:16:58.810 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:16:58.819 [info] 'ToolFileUtils' Reading file: client/src/pages/RealOrderTracker.tsx
2025-05-24 14:16:58.819 [info] 'ToolFileUtils' Successfully read file: client/src/pages/RealOrderTracker.tsx (11268 bytes)
2025-05-24 14:16:59.606 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:17:00.558 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:17:00.979 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/-558691a2
2025-05-24 14:17:01.063 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:17:01.588 [info] 'ToolFileUtils' Reading file: client/src/pages/RealOrderTracker.tsx
2025-05-24 14:17:01.589 [info] 'ToolFileUtils' Successfully read file: client/src/pages/RealOrderTracker.tsx (11469 bytes)
2025-05-24 14:17:01.791 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:17:01.822 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:17:02.088 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:17:10.810 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:17:11.089 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:17:11.097 [info] 'ToolFileUtils' Reading file: client/src/pages/RealOrderTracker.tsx
2025-05-24 14:17:11.098 [info] 'ToolFileUtils' Successfully read file: client/src/pages/RealOrderTracker.tsx (11469 bytes)
2025-05-24 14:17:11.538 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:17:12.481 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:17:12.984 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:17:13.370 [info] 'ToolFileUtils' Reading file: client/src/pages/RealOrderTracker.tsx
2025-05-24 14:17:13.370 [info] 'ToolFileUtils' Successfully read file: client/src/pages/RealOrderTracker.tsx (11476 bytes)
2025-05-24 14:17:13.547 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:17:13.562 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:17:13.907 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:17:19.671 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:17:19.862 [error] 'RemoteAgentsMessenger' Unexpected message type: open-file
2025-05-24 14:17:20.006 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:17:21.676 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:17:22.861 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:17:23.500 [info] 'ToolFileUtils' Reading file: client/src/pages/RealOrderTracker.tsx
2025-05-24 14:17:23.500 [info] 'ToolFileUtils' Successfully read file: client/src/pages/RealOrderTracker.tsx (11476 bytes)
2025-05-24 14:17:24.005 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:17:25.038 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:17:26.228 [info] 'ToolFileUtils' Reading file: client/src/pages/RealOrderTracker.tsx
2025-05-24 14:17:26.229 [info] 'ToolFileUtils' Successfully read file: client/src/pages/RealOrderTracker.tsx (11490 bytes)
2025-05-24 14:17:26.236 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:17:26.766 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:17:26.766 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:17:28.778 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:17:41.838 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:17:42.119 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:17:42.130 [info] 'ToolFileUtils' Reading file: client/src/pages/RealOrderTracker.tsx
2025-05-24 14:17:42.130 [info] 'ToolFileUtils' Successfully read file: client/src/pages/RealOrderTracker.tsx (11490 bytes)
2025-05-24 14:17:42.524 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:17:43.520 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:17:44.032 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:17:44.506 [info] 'ToolFileUtils' Reading file: client/src/pages/RealOrderTracker.tsx
2025-05-24 14:17:44.506 [info] 'ToolFileUtils' Successfully read file: client/src/pages/RealOrderTracker.tsx (11448 bytes)
2025-05-24 14:17:44.684 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:17:44.696 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:17:44.990 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:17:49.998 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:17:57.312 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:17:57.585 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:17:57.594 [info] 'ToolFileUtils' Reading file: client/src/pages/RealOrderTracker.tsx
2025-05-24 14:17:57.595 [info] 'ToolFileUtils' Successfully read file: client/src/pages/RealOrderTracker.tsx (11448 bytes)
2025-05-24 14:17:57.976 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:17:58.976 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:17:59.482 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:17:59.847 [info] 'ToolFileUtils' Reading file: client/src/pages/RealOrderTracker.tsx
2025-05-24 14:17:59.847 [info] 'ToolFileUtils' Successfully read file: client/src/pages/RealOrderTracker.tsx (11620 bytes)
2025-05-24 14:18:00.024 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:18:00.040 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:18:00.366 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:18:09.758 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:18:10.034 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:18:10.043 [info] 'ToolFileUtils' Reading file: client/src/pages/RealOrderTracker.tsx
2025-05-24 14:18:10.044 [info] 'ToolFileUtils' Successfully read file: client/src/pages/RealOrderTracker.tsx (11620 bytes)
2025-05-24 14:18:10.426 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:18:11.450 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:18:11.962 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:18:12.280 [info] 'ToolFileUtils' Reading file: client/src/pages/RealOrderTracker.tsx
2025-05-24 14:18:12.280 [info] 'ToolFileUtils' Successfully read file: client/src/pages/RealOrderTracker.tsx (11580 bytes)
2025-05-24 14:18:12.506 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:18:12.506 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:18:12.795 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:18:19.194 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:18:19.471 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:18:19.800 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:18:20.320 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:18:20.783 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:18:21.726 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1194.10215,"timestamp":"2025-05-24T14:18:21.514Z"}]
2025-05-24 14:18:31.137 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:18:31.137 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:18:37.778 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:18:37.778 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:18:41.784 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:18:41.784 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:18:42.026 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:18:42.026 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:18:44.044 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:18:44.044 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:18:47.365 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:18:47.365 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:18:47.987 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:18:47.987 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:18:49.998 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:18:53.590 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:18:53.590 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:18:53.590 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:18:53.956 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:18:54.460 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:18:59.879 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:19:00.192 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:19:12.922 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:19:13.105 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:19:16.586 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:19:19.063 [error] 'RemoteAgentsMessenger' Unexpected message type: used-chat
2025-05-24 14:19:19.063 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:19:19.063 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:19:19.423 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:19:19.572 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:19:19.988 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:19:23.141 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:19:23.421 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:19:25.712 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:19:25.732 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:19:25.998 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:19:29.097 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:19:31.770 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:19:32.045 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:19:37.494 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:19:37.771 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:19:37.789 [info] 'ViewTool' Tool called with path: client/src/pages and view_range: undefined
2025-05-24 14:19:37.990 [info] 'ViewTool' Listing directory: client/src/pages (depth: 2, showHidden: false)
2025-05-24 14:19:39.291 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:19:39.570 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:19:44.622 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:19:44.907 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:19:44.923 [info] 'ViewTool' Tool called with path: server and view_range: undefined
2025-05-24 14:19:45.126 [info] 'ViewTool' Listing directory: server (depth: 2, showHidden: false)
2025-05-24 14:19:45.834 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:19:46.119 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:19:49.988 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:19:51.242 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:19:51.559 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:19:51.569 [info] 'ViewTool' Tool called with path: . and view_range: undefined
2025-05-24 14:19:51.778 [info] 'ViewTool' Listing directory: . (depth: 2, showHidden: false)
2025-05-24 14:20:20.000 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:20:49.992 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:20:53.467 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:20:53.758 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:21:02.338 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:21:02.644 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:21:03.542 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:21:03.957 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:21:04.245 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:21:04.545 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:21:12.082 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:21:12.365 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:21:12.379 [info] 'ToolFileUtils' Reading file: client/src/App.tsx
2025-05-24 14:21:12.942 [info] 'ToolFileUtils' Successfully read file: client/src/App.tsx (3909 bytes)
2025-05-24 14:21:13.673 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:21:14.716 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:21:15.184 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:21:15.717 [info] 'ToolFileUtils' Reading file: client/src/App.tsx
2025-05-24 14:21:15.718 [info] 'ToolFileUtils' Successfully read file: client/src/App.tsx (3860 bytes)
2025-05-24 14:21:15.892 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:21:18.967 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:21:19.251 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:21:20.004 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:21:26.770 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:21:27.057 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:21:27.068 [info] 'ToolFileUtils' Reading file: client/src/App.tsx
2025-05-24 14:21:27.069 [info] 'ToolFileUtils' Successfully read file: client/src/App.tsx (3860 bytes)
2025-05-24 14:21:27.442 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:21:28.438 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:21:29.262 [info] 'ToolFileUtils' Reading file: client/src/App.tsx
2025-05-24 14:21:29.262 [info] 'ToolFileUtils' Successfully read file: client/src/App.tsx (3783 bytes)
2025-05-24 14:21:29.438 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:21:32.489 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:21:32.777 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:21:41.340 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:21:41.624 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:21:41.633 [info] 'ViewTool' Tool called with path: fix-orders-table.js and view_range: [1,30]
2025-05-24 14:21:42.536 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:21:42.821 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:21:49.157 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:21:49.445 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:21:49.454 [info] 'ViewTool' Tool called with path: test-customizations.js and view_range: [1,30]
2025-05-24 14:21:49.997 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:21:50.336 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:21:50.610 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:21:55.742 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:21:56.030 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:21:56.041 [info] 'ViewTool' Tool called with path: test-orders-fix.js and view_range: [1,30]
2025-05-24 14:21:56.961 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:21:57.250 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:22:04.214 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:22:04.511 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:22:04.522 [info] 'ViewTool' Tool called with path: server/admin-api.js and view_range: [1,30]
2025-05-24 14:22:05.421 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:22:05.709 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:22:11.660 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:22:11.945 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:22:11.984 [info] 'ViewTool' Tool called with path: server/db.js and view_range: [1,30]
2025-05-24 14:22:12.993 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:22:13.290 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:22:18.642 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:22:18.936 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:22:19.132 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:22:19.991 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:22:20.044 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:22:20.128 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:22:20.332 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:22:21.173 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:22:25.560 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:22:25.852 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:22:25.876 [info] 'ViewTool' Tool called with path: server/create-missing-tables.ts and view_range: [1,30]
2025-05-24 14:22:26.821 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:22:27.118 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:22:31.980 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:22:32.274 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:22:32.286 [info] 'ViewTool' Tool called with path: server/create-tables.ts and view_range: [1,30]
2025-05-24 14:22:33.366 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:22:33.683 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:22:40.762 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:22:41.117 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:22:41.337 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:22:41.878 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:22:42.174 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:22:42.313 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:22:49.478 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:22:50.462 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:22:50.462 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:22:51.125 [info] 'ViewTool' Tool called with path: client/src/components and view_range: undefined
2025-05-24 14:22:51.786 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1317.496959,"timestamp":"2025-05-24T14:22:51.779Z"}]
2025-05-24 14:22:52.172 [info] 'ViewTool' Listing directory: client/src/components (depth: 2, showHidden: false)
2025-05-24 14:22:54.247 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:22:54.539 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:23:00.686 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:23:00.994 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:23:08.050 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:23:08.332 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:23:15.378 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:23:15.673 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:23:16.458 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:23:16.922 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:23:17.213 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:23:17.494 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:23:19.988 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:23:22.752 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:23:23.045 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:23:23.060 [info] 'ViewTool' Tool called with path: cookies.txt and view_range: [1,10]
2025-05-24 14:23:24.003 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:23:24.300 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:23:29.264 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:23:29.593 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:23:29.605 [info] 'ViewTool' Tool called with path: cookies2.txt and view_range: [1,10]
2025-05-24 14:23:30.496 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:23:30.789 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:23:36.796 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:23:37.082 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:23:37.288 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:23:37.656 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:23:37.943 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:23:38.298 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:23:44.060 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:23:44.352 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:23:44.577 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:23:44.863 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:23:49.996 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:23:53.647 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:23:53.647 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:23:56.092 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:23:56.396 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:23:56.666 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:23:56.990 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:24:05.236 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:24:05.518 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:24:05.706 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:24:05.993 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:24:10.176 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:24:10.471 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:24:10.721 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:24:11.007 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:24:18.436 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:24:18.741 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:24:18.754 [info] 'ToolFileUtils' Reading file: client/src/pages/OrderConfirmation.tsx
2025-05-24 14:24:18.754 [info] 'ToolFileUtils' Successfully read file: client/src/pages/OrderConfirmation.tsx (14152 bytes)
2025-05-24 14:24:19.262 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:24:20.007 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:24:20.263 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:24:20.770 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:24:21.133 [info] 'ToolFileUtils' Reading file: client/src/pages/OrderConfirmation.tsx
2025-05-24 14:24:21.133 [info] 'ToolFileUtils' Successfully read file: client/src/pages/OrderConfirmation.tsx (14135 bytes)
2025-05-24 14:24:21.327 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:24:21.327 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:24:21.620 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:24:31.904 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:24:32.196 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:24:32.211 [info] 'ToolFileUtils' Reading file: client/src/pages/OrderConfirmation.tsx
2025-05-24 14:24:32.211 [info] 'ToolFileUtils' Successfully read file: client/src/pages/OrderConfirmation.tsx (14135 bytes)
2025-05-24 14:24:32.457 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:24:32.754 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:24:37.920 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:24:38.208 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:24:38.222 [info] 'ViewTool' Tool called with path: client/src/pages/OrderConfirmation.tsx and view_range: [200,220]
2025-05-24 14:24:38.613 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:24:38.899 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:24:44.604 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:24:44.944 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:24:44.956 [info] 'ViewTool' Tool called with path: client/src/pages/OrderConfirmation.tsx and view_range: [100,200]
2025-05-24 14:24:45.332 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:24:45.625 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:24:49.990 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:24:52.694 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:24:53.001 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:24:53.203 [info] 'ViewTool' Tool called with path: client/src/pages/OrderConfirmation.tsx and view_range: [80,110]
2025-05-24 14:24:53.589 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:24:53.878 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:25:02.996 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:25:03.290 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:25:03.301 [info] 'ToolFileUtils' Reading file: shared/schema.ts
2025-05-24 14:25:03.301 [info] 'ToolFileUtils' Successfully read file: shared/schema.ts (6053 bytes)
2025-05-24 14:25:03.493 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:25:04.505 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:25:04.984 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:25:05.543 [info] 'ToolFileUtils' Reading file: shared/schema.ts
2025-05-24 14:25:05.543 [info] 'ToolFileUtils' Successfully read file: shared/schema.ts (6041 bytes)
2025-05-24 14:25:05.720 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:25:05.729 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:25:06.048 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:25:13.481 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:25:13.787 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:25:13.797 [info] 'ToolFileUtils' Reading file: client/src/pages/OrderConfirmation.tsx
2025-05-24 14:25:13.797 [info] 'ToolFileUtils' Successfully read file: client/src/pages/OrderConfirmation.tsx (14135 bytes)
2025-05-24 14:25:14.193 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:25:15.236 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:25:16.015 [info] 'ToolFileUtils' Reading file: client/src/pages/OrderConfirmation.tsx
2025-05-24 14:25:16.015 [info] 'ToolFileUtils' Successfully read file: client/src/pages/OrderConfirmation.tsx (14144 bytes)
2025-05-24 14:25:16.192 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:25:16.202 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:25:16.514 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:25:19.990 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:25:24.794 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:25:25.092 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:25:26.781 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:25:27.078 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:25:32.154 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:25:32.448 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:25:32.720 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:25:33.052 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:25:37.714 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:25:38.007 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:25:38.218 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:25:38.510 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:25:43.238 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:25:43.529 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:25:43.850 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:25:44.140 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:25:48.513 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:25:48.813 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:25:49.031 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:25:49.319 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:25:49.990 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:25:53.890 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:25:54.192 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:25:54.864 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:25:55.158 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:26:20.002 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:26:20.590 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:26:20.590 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:26:20.590 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:26:21.485 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:26:21.994 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:26:23.117 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:26:23.294 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:26:50.004 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:27:19.990 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:27:50.004 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:28:19.993 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:28:27.707 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:28:27.928 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:28:37.617 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:28:37.911 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:28:50.015 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:29:16.733 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:29:19.994 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:29:31.342 [error] 'RemoteAgentsMessenger' Unexpected message type: used-chat
2025-05-24 14:29:31.345 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:29:31.345 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:29:31.390 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:29:31.828 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:29:31.862 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:29:36.407 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:29:36.440 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:29:36.893 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:29:38.596 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:29:38.604 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:29:38.904 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:29:38.918 [info] 'ViewTool' Tool called with path: client/src/pages/RealOrderTracker.tsx and view_range: [1,100]
2025-05-24 14:29:39.847 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:29:40.155 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:29:42.697 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:29:45.962 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:29:46.143 [error] 'RemoteAgentsMessenger' Unexpected message type: open-file
2025-05-24 14:29:50.006 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:29:50.528 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:29:50.835 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:29:50.858 [info] 'ViewTool' Tool called with path: client/src/App.tsx and view_range: [30,80]
2025-05-24 14:29:51.883 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:29:52.194 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:30:04.656 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:30:04.964 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:30:05.084 [info] 'ToolFileUtils' Reading file: client/src/App.tsx
2025-05-24 14:30:05.084 [info] 'ToolFileUtils' Successfully read file: client/src/App.tsx (3783 bytes)
2025-05-24 14:30:05.959 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:30:06.880 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:30:07.390 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:30:07.878 [info] 'ToolFileUtils' Reading file: client/src/App.tsx
2025-05-24 14:30:07.878 [info] 'ToolFileUtils' Successfully read file: client/src/App.tsx (3843 bytes)
2025-05-24 14:30:08.054 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:30:11.099 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:30:11.400 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:30:16.766 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:30:17.075 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:30:20.016 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:30:20.840 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:30:21.143 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:30:25.998 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:30:26.297 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:30:30.397 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:30:30.700 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:30:42.282 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:30:42.579 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:30:42.605 [info] 'ToolFileUtils' Reading file: client/src/pages/RealOrderTracker.tsx
2025-05-24 14:30:42.605 [info] 'ToolFileUtils' Successfully read file: client/src/pages/RealOrderTracker.tsx (11580 bytes)
2025-05-24 14:30:43.081 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:30:44.036 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:30:44.538 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:30:45.052 [info] 'ToolFileUtils' Reading file: client/src/pages/RealOrderTracker.tsx
2025-05-24 14:30:45.052 [info] 'ToolFileUtils' Successfully read file: client/src/pages/RealOrderTracker.tsx (11653 bytes)
2025-05-24 14:30:45.228 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:30:45.236 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:30:45.552 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:30:49.999 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:30:56.346 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:30:56.646 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:30:56.661 [info] 'ToolFileUtils' Reading file: client/src/pages/RealOrderTracker.tsx
2025-05-24 14:30:56.661 [info] 'ToolFileUtils' Successfully read file: client/src/pages/RealOrderTracker.tsx (11653 bytes)
2025-05-24 14:30:57.103 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:30:58.062 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:30:58.556 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:30:58.900 [info] 'ToolFileUtils' Reading file: client/src/pages/RealOrderTracker.tsx
2025-05-24 14:30:58.900 [info] 'ToolFileUtils' Successfully read file: client/src/pages/RealOrderTracker.tsx (11728 bytes)
2025-05-24 14:30:59.085 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:30:59.085 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:30:59.387 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:31:14.517 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:31:14.818 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:31:14.830 [info] 'ToolFileUtils' Reading file: client/src/pages/RealOrderTracker.tsx
2025-05-24 14:31:14.830 [info] 'ToolFileUtils' Successfully read file: client/src/pages/RealOrderTracker.tsx (11728 bytes)
2025-05-24 14:31:15.233 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:31:16.276 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:31:17.058 [info] 'ToolFileUtils' Reading file: client/src/pages/RealOrderTracker.tsx
2025-05-24 14:31:17.058 [info] 'ToolFileUtils' Successfully read file: client/src/pages/RealOrderTracker.tsx (13106 bytes)
2025-05-24 14:31:17.282 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:31:17.282 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:31:17.622 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:31:20.001 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:31:35.634 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:31:35.947 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:31:35.959 [info] 'ToolFileUtils' Reading file: client/src/pages/RealOrderTracker.tsx
2025-05-24 14:31:35.959 [info] 'ToolFileUtils' Successfully read file: client/src/pages/RealOrderTracker.tsx (13106 bytes)
2025-05-24 14:31:36.341 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:31:37.344 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:31:37.846 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:31:38.196 [info] 'ToolFileUtils' Reading file: client/src/pages/RealOrderTracker.tsx
2025-05-24 14:31:38.196 [info] 'ToolFileUtils' Successfully read file: client/src/pages/RealOrderTracker.tsx (13909 bytes)
2025-05-24 14:31:38.408 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:31:38.408 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:31:38.779 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:31:50.003 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:31:56.770 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:31:57.075 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:31:57.096 [info] 'ToolFileUtils' Reading file: client/src/pages/RealOrderTracker.tsx
2025-05-24 14:31:57.096 [info] 'ToolFileUtils' Successfully read file: client/src/pages/RealOrderTracker.tsx (13909 bytes)
2025-05-24 14:31:57.476 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:31:58.477 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:31:59.010 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:31:59.309 [info] 'ToolFileUtils' Reading file: client/src/pages/RealOrderTracker.tsx
2025-05-24 14:31:59.309 [info] 'ToolFileUtils' Successfully read file: client/src/pages/RealOrderTracker.tsx (14449 bytes)
2025-05-24 14:32:00.057 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:32:02.536 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:32:02.839 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:32:14.312 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:32:14.711 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:32:14.723 [info] 'ToolFileUtils' Reading file: client/src/pages/RealOrderTracker.tsx
2025-05-24 14:32:14.723 [info] 'ToolFileUtils' Successfully read file: client/src/pages/RealOrderTracker.tsx (14449 bytes)
2025-05-24 14:32:15.113 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:32:16.120 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:32:16.622 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:32:16.958 [info] 'ToolFileUtils' Reading file: client/src/pages/RealOrderTracker.tsx
2025-05-24 14:32:16.958 [info] 'ToolFileUtils' Successfully read file: client/src/pages/RealOrderTracker.tsx (14750 bytes)
2025-05-24 14:32:17.165 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:32:17.165 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:32:17.489 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:32:20.009 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:32:35.328 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:32:35.644 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:32:35.660 [info] 'ToolFileUtils' Reading file: client/src/pages/RealOrderTracker.tsx
2025-05-24 14:32:35.660 [info] 'ToolFileUtils' Successfully read file: client/src/pages/RealOrderTracker.tsx (14750 bytes)
2025-05-24 14:32:36.053 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:32:37.079 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:32:37.586 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:32:37.912 [info] 'ToolFileUtils' Reading file: client/src/pages/RealOrderTracker.tsx
2025-05-24 14:32:37.912 [info] 'ToolFileUtils' Successfully read file: client/src/pages/RealOrderTracker.tsx (16091 bytes)
2025-05-24 14:32:38.101 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:32:38.119 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:32:38.481 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:32:50.007 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:33:14.274 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:33:14.595 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:33:14.612 [info] 'ToolFileUtils' Reading file: client/src/pages/RealOrderTracker.tsx
2025-05-24 14:33:14.613 [info] 'ToolFileUtils' Successfully read file: client/src/pages/RealOrderTracker.tsx (16091 bytes)
2025-05-24 14:33:15.104 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:33:16.120 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:33:16.618 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:33:17.007 [info] 'ToolFileUtils' Reading file: client/src/pages/RealOrderTracker.tsx
2025-05-24 14:33:17.007 [info] 'ToolFileUtils' Successfully read file: client/src/pages/RealOrderTracker.tsx (19829 bytes)
2025-05-24 14:33:17.198 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:33:17.198 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:33:17.547 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:33:20.009 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:33:29.410 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:33:29.711 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:33:29.969 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:33:30.271 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:33:36.778 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:33:37.095 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:33:37.354 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:33:37.654 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:33:41.626 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:33:41.931 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:33:42.163 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:33:43.602 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:33:50.004 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:33:52.894 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:33:53.206 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:33:53.221 [info] 'ToolFileUtils' Reading file: client/src/pages/RealOrderTracker.tsx
2025-05-24 14:33:53.221 [info] 'ToolFileUtils' Successfully read file: client/src/pages/RealOrderTracker.tsx (19829 bytes)
2025-05-24 14:33:53.667 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:33:54.709 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:33:55.468 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:33:55.842 [info] 'ToolFileUtils' Reading file: client/src/pages/RealOrderTracker.tsx
2025-05-24 14:33:55.842 [info] 'ToolFileUtils' Successfully read file: client/src/pages/RealOrderTracker.tsx (19811 bytes)
2025-05-24 14:33:56.134 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:33:56.134 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:33:56.496 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:34:03.467 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:34:03.777 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:34:03.804 [info] 'ToolFileUtils' Reading file: client/src/pages/RealOrderTracker.tsx
2025-05-24 14:34:03.804 [info] 'ToolFileUtils' Successfully read file: client/src/pages/RealOrderTracker.tsx (19811 bytes)
2025-05-24 14:34:04.195 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:34:05.258 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:34:05.758 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:34:06.249 [info] 'ToolFileUtils' Reading file: client/src/pages/RealOrderTracker.tsx
2025-05-24 14:34:06.249 [info] 'ToolFileUtils' Successfully read file: client/src/pages/RealOrderTracker.tsx (19801 bytes)
2025-05-24 14:34:06.431 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:34:06.431 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:34:07.243 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:34:16.578 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:34:16.887 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:34:18.683 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:34:18.983 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:34:19.996 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:34:24.308 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:34:24.626 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:34:24.837 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:34:25.140 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:34:30.476 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:34:30.787 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:34:31.041 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:34:31.381 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:34:36.036 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:34:36.340 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:34:36.577 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:34:36.888 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:34:41.792 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:34:42.135 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:34:42.185 [info] 'ViewTool' Tool called with path: server/index.ts and view_range: [1,50]
2025-05-24 14:34:43.081 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:34:43.397 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:34:49.992 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:34:50.293 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:34:50.606 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:34:50.895 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:34:51.196 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:34:56.090 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:34:56.411 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:34:56.632 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:34:56.926 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:35:02.425 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:35:02.721 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:35:02.732 [info] 'ViewTool' Tool called with path: server/init-db.ts and view_range: [1,50]
2025-05-24 14:35:03.626 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:35:03.932 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:35:10.764 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:35:11.088 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:35:11.101 [info] 'ViewTool' Tool called with path: server/db.ts and view_range: [1,50]
2025-05-24 14:35:12.028 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:35:12.341 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:35:17.817 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:35:18.116 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:35:18.128 [info] 'ViewTool' Tool called with path: .env and view_range: [1,10]
2025-05-24 14:35:18.615 [info] 'ViewTool' Path does not exist: .env
2025-05-24 14:35:19.316 [info] 'ToolFileUtils' File not found: .env. No similar files found
2025-05-24 14:35:19.517 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:35:19.837 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:35:20.002 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:35:25.684 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:35:26.367 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:35:26.378 [info] 'ViewTool' Tool called with path: . and view_range: undefined
2025-05-24 14:35:26.588 [info] 'ViewTool' Listing directory: . (depth: 2, showHidden: false)
2025-05-24 14:35:50.000 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:36:19.992 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:36:29.977 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:36:30.290 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:36:50.006 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:36:59.656 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:36:59.656 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:36:59.657 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:36:59.657 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:36:59.678 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:36:59.678 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:37:09.286 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:37:09.286 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:37:09.286 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:37:09.659 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:37:10.167 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:37:20.001 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:37:30.776 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:37:30.958 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:37:35.652 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:37:45.879 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:37:46.060 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:37:48.186 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:37:48.365 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:37:49.428 [error] 'RemoteAgentsMessenger' Unexpected message type: used-chat
2025-05-24 14:37:49.431 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:37:49.431 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:37:49.487 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:37:49.956 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:37:49.958 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:37:49.995 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:37:55.314 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:37:55.337 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:37:55.969 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:37:58.668 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:37:58.679 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:37:58.973 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:37:58.989 [info] 'ViewTool' Tool called with path: client/src/pages/DriverPage.tsx and view_range: [1,100]
2025-05-24 14:37:59.381 [info] 'ViewTool' Path does not exist: client/src/pages/DriverPage.tsx
2025-05-24 14:38:00.068 [info] 'ToolFileUtils' File not found: client/src/pages/DriverPage.tsx. Similar files found: client/src/pages/driver/DriverPage.tsx
2025-05-24 14:38:00.259 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:38:00.572 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:38:05.810 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:38:06.123 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:38:06.137 [info] 'ViewTool' Tool called with path: client/src/pages/driver and view_range: undefined
2025-05-24 14:38:06.346 [info] 'ViewTool' Listing directory: client/src/pages/driver (depth: 2, showHidden: false)
2025-05-24 14:38:06.882 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:38:07.200 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:38:11.580 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:38:11.893 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:38:11.913 [info] 'ViewTool' Tool called with path: client/src/pages/driver/DriverPage.tsx and view_range: [1,100]
2025-05-24 14:38:12.015 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:38:12.833 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:38:13.150 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:38:18.925 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:38:19.245 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:38:19.263 [info] 'ViewTool' Tool called with path: client/src/utils/orderStatusWorkflow.ts and view_range: [1,100]
2025-05-24 14:38:20.008 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:38:20.238 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:38:20.601 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:38:26.676 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:38:26.981 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:38:33.312 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:38:33.636 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:38:43.530 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:38:43.875 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:38:49.981 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:38:50.004 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:38:50.311 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:38:55.709 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:38:55.709 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:39:12.439 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:39:12.754 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:39:12.771 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-05-24 14:39:13.326 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (14797 bytes)
2025-05-24 14:39:14.136 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:39:15.058 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:39:15.562 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:39:16.137 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-05-24 14:39:16.138 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (14831 bytes)
2025-05-24 14:39:16.317 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:39:19.336 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:39:19.665 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:39:20.007 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:39:31.213 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:39:31.552 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:39:31.575 [info] 'ToolFileUtils' Reading file: client/src/pages/manager/ManagerPage.tsx
2025-05-24 14:39:32.115 [info] 'ToolFileUtils' Successfully read file: client/src/pages/manager/ManagerPage.tsx (27679 bytes)
2025-05-24 14:39:32.851 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:39:33.861 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:39:34.364 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:39:34.765 [info] 'ToolFileUtils' Reading file: client/src/pages/manager/ManagerPage.tsx
2025-05-24 14:39:34.765 [info] 'ToolFileUtils' Successfully read file: client/src/pages/manager/ManagerPage.tsx (27717 bytes)
2025-05-24 14:39:34.948 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:39:37.953 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:39:38.283 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:39:47.820 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:39:48.154 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:39:48.205 [info] 'ToolFileUtils' Reading file: client/src/pages/manager/ManagerPage.tsx
2025-05-24 14:39:48.205 [info] 'ToolFileUtils' Successfully read file: client/src/pages/manager/ManagerPage.tsx (27717 bytes)
2025-05-24 14:39:50.004 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:39:51.424 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:39:51.750 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:40:01.433 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:40:01.757 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:40:01.771 [info] 'ToolFileUtils' Reading file: client/src/pages/manager/ManagerPage.tsx
2025-05-24 14:40:01.772 [info] 'ToolFileUtils' Successfully read file: client/src/pages/manager/ManagerPage.tsx (27717 bytes)
2025-05-24 14:40:02.239 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:40:03.221 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:40:04.042 [info] 'ToolFileUtils' Reading file: client/src/pages/manager/ManagerPage.tsx
2025-05-24 14:40:04.043 [info] 'ToolFileUtils' Successfully read file: client/src/pages/manager/ManagerPage.tsx (27767 bytes)
2025-05-24 14:40:04.238 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:40:07.264 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:40:07.585 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:40:18.902 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:40:19.247 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:40:19.260 [info] 'ToolFileUtils' Reading file: client/src/pages/manager/ManagerPage.tsx
2025-05-24 14:40:19.260 [info] 'ToolFileUtils' Successfully read file: client/src/pages/manager/ManagerPage.tsx (27767 bytes)
2025-05-24 14:40:19.632 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:40:19.998 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:40:20.632 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:40:21.451 [info] 'ToolFileUtils' Reading file: client/src/pages/manager/ManagerPage.tsx
2025-05-24 14:40:21.451 [info] 'ToolFileUtils' Successfully read file: client/src/pages/manager/ManagerPage.tsx (28160 bytes)
2025-05-24 14:40:24.689 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:40:25.012 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:40:38.512 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:40:38.837 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:40:38.853 [info] 'ToolFileUtils' Reading file: client/src/pages/manager/ManagerPage.tsx
2025-05-24 14:40:38.854 [info] 'ToolFileUtils' Successfully read file: client/src/pages/manager/ManagerPage.tsx (28160 bytes)
2025-05-24 14:40:39.246 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:40:40.280 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:40:40.790 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:40:41.138 [info] 'ToolFileUtils' Reading file: client/src/pages/manager/ManagerPage.tsx
2025-05-24 14:40:41.139 [info] 'ToolFileUtils' Successfully read file: client/src/pages/manager/ManagerPage.tsx (28281 bytes)
2025-05-24 14:40:41.353 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:40:44.330 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:40:44.701 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:40:50.006 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:40:53.951 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:40:54.288 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:40:54.301 [info] 'ToolFileUtils' Reading file: client/src/pages/manager/ManagerPage.tsx
2025-05-24 14:40:54.301 [info] 'ToolFileUtils' Successfully read file: client/src/pages/manager/ManagerPage.tsx (28281 bytes)
2025-05-24 14:40:54.529 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:40:54.847 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:41:00.788 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:41:01.115 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:41:01.138 [info] 'ViewTool' Tool called with path: client/src/pages/manager/ManagerPage.tsx and view_range: [375,385]
2025-05-24 14:41:01.499 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:41:01.818 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:41:11.482 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:41:11.807 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:41:11.824 [info] 'ToolFileUtils' Reading file: client/src/pages/manager/ManagerPage.tsx
2025-05-24 14:41:11.824 [info] 'ToolFileUtils' Successfully read file: client/src/pages/manager/ManagerPage.tsx (28281 bytes)
2025-05-24 14:41:12.199 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:41:13.198 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:41:13.710 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:41:14.053 [info] 'ToolFileUtils' Reading file: client/src/pages/manager/ManagerPage.tsx
2025-05-24 14:41:14.053 [info] 'ToolFileUtils' Successfully read file: client/src/pages/manager/ManagerPage.tsx (28365 bytes)
2025-05-24 14:41:14.232 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:41:17.279 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:41:17.614 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:41:20.004 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:41:28.898 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:41:29.218 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:41:29.233 [info] 'ToolFileUtils' Reading file: client/src/pages/manager/ManagerPage.tsx
2025-05-24 14:41:29.233 [info] 'ToolFileUtils' Successfully read file: client/src/pages/manager/ManagerPage.tsx (28365 bytes)
2025-05-24 14:41:29.647 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:41:30.658 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:41:31.166 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:41:31.521 [info] 'ToolFileUtils' Reading file: client/src/pages/manager/ManagerPage.tsx
2025-05-24 14:41:31.521 [info] 'ToolFileUtils' Successfully read file: client/src/pages/manager/ManagerPage.tsx (28463 bytes)
2025-05-24 14:41:31.719 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:41:34.739 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:41:35.070 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:41:44.154 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:41:44.478 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:41:44.496 [info] 'ToolFileUtils' Reading file: client/src/pages/manager/ManagerPage.tsx
2025-05-24 14:41:44.496 [info] 'ToolFileUtils' Successfully read file: client/src/pages/manager/ManagerPage.tsx (28463 bytes)
2025-05-24 14:41:44.891 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:41:45.893 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:41:46.400 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:41:46.727 [info] 'ToolFileUtils' Reading file: client/src/pages/manager/ManagerPage.tsx
2025-05-24 14:41:46.727 [info] 'ToolFileUtils' Successfully read file: client/src/pages/manager/ManagerPage.tsx (28531 bytes)
2025-05-24 14:41:46.902 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:41:49.966 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:41:50.007 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:41:50.297 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:41:58.582 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:41:58.902 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:41:58.918 [info] 'ToolFileUtils' Reading file: client/src/pages/manager/ManagerPage.tsx
2025-05-24 14:41:58.918 [info] 'ToolFileUtils' Successfully read file: client/src/pages/manager/ManagerPage.tsx (28531 bytes)
2025-05-24 14:41:59.301 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:42:00.295 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:42:00.806 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:42:01.175 [info] 'ToolFileUtils' Reading file: client/src/pages/manager/ManagerPage.tsx
2025-05-24 14:42:01.176 [info] 'ToolFileUtils' Successfully read file: client/src/pages/manager/ManagerPage.tsx (28599 bytes)
2025-05-24 14:42:01.354 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:42:04.381 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:42:04.698 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:42:12.144 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:42:12.493 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:42:12.507 [info] 'ToolFileUtils' Reading file: client/src/pages/manager/ManagerPage.tsx
2025-05-24 14:42:12.507 [info] 'ToolFileUtils' Successfully read file: client/src/pages/manager/ManagerPage.tsx (28599 bytes)
2025-05-24 14:42:12.933 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:42:13.950 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:42:14.463 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:42:14.804 [info] 'ToolFileUtils' Reading file: client/src/pages/manager/ManagerPage.tsx
2025-05-24 14:42:14.804 [info] 'ToolFileUtils' Successfully read file: client/src/pages/manager/ManagerPage.tsx (28667 bytes)
2025-05-24 14:42:15.074 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:42:18.053 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:42:18.377 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:42:20.006 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:42:26.680 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:42:27.005 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:42:27.021 [info] 'ToolFileUtils' Reading file: client/src/pages/manager/ManagerPage.tsx
2025-05-24 14:42:27.021 [info] 'ToolFileUtils' Successfully read file: client/src/pages/manager/ManagerPage.tsx (28667 bytes)
2025-05-24 14:42:27.389 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:42:28.391 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:42:29.207 [info] 'ToolFileUtils' Reading file: client/src/pages/manager/ManagerPage.tsx
2025-05-24 14:42:29.207 [info] 'ToolFileUtils' Successfully read file: client/src/pages/manager/ManagerPage.tsx (28735 bytes)
2025-05-24 14:42:32.445 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:42:32.837 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:42:41.314 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:42:41.629 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:42:41.646 [info] 'ToolFileUtils' Reading file: client/src/pages/manager/ManagerPage.tsx
2025-05-24 14:42:41.646 [info] 'ToolFileUtils' Successfully read file: client/src/pages/manager/ManagerPage.tsx (28735 bytes)
2025-05-24 14:42:42.250 [error] 'FuzzySymbolSearcher' Failed to read file tokens for 6335c6dfaa6f175b831d71698d2919d1f91a523831703361c6cc5e011716d5a6: deleted
2025-05-24 14:42:42.441 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:42:43.448 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:42:43.947 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:42:44.327 [info] 'ToolFileUtils' Reading file: client/src/pages/manager/ManagerPage.tsx
2025-05-24 14:42:44.327 [info] 'ToolFileUtils' Successfully read file: client/src/pages/manager/ManagerPage.tsx (28785 bytes)
2025-05-24 14:42:44.504 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:42:47.520 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:42:47.845 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:42:50.000 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:42:55.110 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:42:55.450 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:42:55.464 [info] 'ToolFileUtils' Reading file: client/src/pages/manager/ManagerPage.tsx
2025-05-24 14:42:55.464 [info] 'ToolFileUtils' Successfully read file: client/src/pages/manager/ManagerPage.tsx (28785 bytes)
2025-05-24 14:42:55.843 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:42:56.840 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:42:57.350 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:42:57.674 [info] 'ToolFileUtils' Reading file: client/src/pages/manager/ManagerPage.tsx
2025-05-24 14:42:57.674 [info] 'ToolFileUtils' Successfully read file: client/src/pages/manager/ManagerPage.tsx (28825 bytes)
2025-05-24 14:42:57.852 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:43:00.933 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:43:01.280 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:43:11.170 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:43:11.495 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:43:11.558 [info] 'ToolFileUtils' Reading file: client/src/pages/manager/ManagerPage.tsx
2025-05-24 14:43:11.558 [info] 'ToolFileUtils' Successfully read file: client/src/pages/manager/ManagerPage.tsx (28825 bytes)
2025-05-24 14:43:11.937 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:43:12.947 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:43:13.442 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:43:13.809 [info] 'ToolFileUtils' Reading file: client/src/pages/manager/ManagerPage.tsx
2025-05-24 14:43:13.809 [info] 'ToolFileUtils' Successfully read file: client/src/pages/manager/ManagerPage.tsx (29108 bytes)
2025-05-24 14:43:13.996 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:43:18.171 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:43:19.422 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:43:20.012 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:43:36.777 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:43:36.777 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:43:36.777 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:43:36.778 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:43:36.778 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:43:36.779 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:43:43.316 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:43:43.317 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:43:43.317 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:43:43.625 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:43:44.130 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:43:50.001 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:44:14.462 [info] 'AugmentExtension' Retrieving model config
2025-05-24 14:44:14.823 [info] 'AugmentExtension' Retrieved model config
2025-05-24 14:44:14.823 [info] 'AugmentExtension' Returning model config
2025-05-24 14:44:19.998 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:44:50.006 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:45:20.002 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:45:50.000 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:46:03.121 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:46:03.325 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:46:11.777 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:46:12.008 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:46:20.000 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:46:20.856 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:46:21.044 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:46:50.006 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:47:16.775 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:47:17.099 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 14:47:20.008 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
