2025-05-24 11:44:18.720 [info] [main] Log level: Info
2025-05-24 11:44:18.720 [info] [main] Validating found git in: "git"
2025-05-24 11:44:18.720 [info] [main] Using git "2.47.2" from "git"
2025-05-24 11:44:18.720 [info] [Model][doInitialScan] Initial repository scan started
2025-05-24 11:44:18.721 [info] > git rev-parse --show-toplevel [3ms]
2025-05-24 11:44:18.721 [info] > git rev-parse --git-dir --git-common-dir [277ms]
2025-05-24 11:44:18.721 [info] [Model][openRepository] Opened repository (path): /home/<USER>/workspace
2025-05-24 11:44:18.721 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/workspace
2025-05-24 11:44:18.721 [info] > git config --get commit.template [10ms]
2025-05-24 11:44:18.721 [info] > git fetch [26ms]
2025-05-24 11:44:18.721 [info] > git rev-parse --show-toplevel [14ms]
2025-05-24 11:44:18.721 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 11:44:18.722 [info] > git config --get commit.template [15ms]
2025-05-24 11:44:18.722 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 11:44:18.722 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [756ms]
2025-05-24 11:44:18.722 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [58ms]
2025-05-24 11:44:18.722 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [830ms]
2025-05-24 11:44:18.722 [info] > git rev-parse --show-toplevel [845ms]
2025-05-24 11:44:18.722 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [15ms]
2025-05-24 11:44:18.722 [info] > git config --get --local branch.main.vscode-merge-base [33ms]
2025-05-24 11:44:18.722 [warning] [Git][config] git config failed: Failed to execute git
2025-05-24 11:44:18.722 [info] > git config --get commit.template [57ms]
2025-05-24 11:44:18.722 [info] > git config --get --local branch.main.vscode-merge-base [10ms]
2025-05-24 11:44:18.722 [warning] [Git][config] git config failed: Failed to execute git
2025-05-24 11:44:18.722 [info] > git rev-parse --show-toplevel [35ms]
2025-05-24 11:44:18.723 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 11:44:18.723 [info] > git reflog main --grep-reflog=branch: Created from *. [24ms]
2025-05-24 11:44:18.723 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [20ms]
2025-05-24 11:44:18.723 [info] > git reflog main --grep-reflog=branch: Created from *. [44ms]
2025-05-24 11:44:18.723 [info] > git status -z -uall [10ms]
2025-05-24 11:44:18.723 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 11:44:18.723 [info] > git rev-parse --show-toplevel [41ms]
2025-05-24 11:44:18.723 [info] > git rev-parse --show-toplevel [4ms]
2025-05-24 11:44:18.723 [info] > git rev-parse --show-toplevel [1ms]
2025-05-24 11:44:18.723 [info] > git rev-parse --show-toplevel [368ms]
2025-05-24 11:44:18.723 [info] > git rev-parse --show-toplevel [17ms]
2025-05-24 11:44:18.723 [info] > git rev-parse --show-toplevel [65ms]
2025-05-24 11:44:18.732 [info] > git rev-parse --show-toplevel [20ms]
2025-05-24 11:44:18.737 [info] [Model][doInitialScan] Initial repository scan completed - repositories (1), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-05-24 11:44:18.922 [info] > git show --textconv :shared/schema.ts [124ms]
2025-05-24 11:44:18.922 [info] > git check-ignore -v -z --stdin [6ms]
2025-05-24 11:44:18.923 [info] > git config --get commit.template [22ms]
2025-05-24 11:44:18.924 [info] > git ls-files --stage -- shared/schema.ts [113ms]
2025-05-24 11:44:18.970 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 11:44:18.972 [info] > git cat-file -s d32f4ffe71fcf6e84526cd8ee980575cb739b036 [38ms]
2025-05-24 11:44:18.991 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [21ms]
2025-05-24 11:44:19.030 [info] > git status -z -uall [20ms]
2025-05-24 11:44:19.031 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 11:44:19.787 [info] > git blame --root --incremental 950dc2c924e9cc2598727254b321b92999218d81 -- shared/schema.ts [9ms]
2025-05-24 11:48:57.031 [info] > git fetch [12ms]
2025-05-24 11:48:57.052 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 11:48:57.054 [info] > git config --get commit.template [22ms]
2025-05-24 11:48:57.054 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 11:48:57.056 [info] > git config --get commit.template [13ms]
2025-05-24 11:48:57.066 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 11:48:57.068 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 11:48:57.089 [info] > git status -z -uall [11ms]
2025-05-24 11:48:57.090 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 11:48:57.108 [info] > git blame --root --incremental 63dc5bf3c394b7b74ba063fd015aa649de876bb8 -- shared/schema.ts [5ms]
2025-05-24 11:48:57.208 [info] > git ls-files --stage -- shared/schema.ts [1ms]
2025-05-24 11:48:57.218 [info] > git cat-file -s d32f4ffe71fcf6e84526cd8ee980575cb739b036 [2ms]
2025-05-24 11:48:57.422 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [17ms]
2025-05-24 11:48:57.424 [info] > git show --textconv :shared/schema.ts [2ms]
2025-05-24 11:48:57.424 [info] > git show -s --decorate=full --shortstat --format=%H%n%aN%n%aE%n%at%n%ct%n%P%n%D%n%B -z 63dc5bf3c394b7b74ba063fd015aa649de876bb8 -- [10ms]
2025-05-24 11:48:57.441 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [9ms]
2025-05-24 11:48:57.452 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [13ms]
2025-05-24 11:48:57.474 [info] > git config --get --local branch.main.vscode-merge-base [24ms]
2025-05-24 11:48:57.474 [warning] [Git][config] git config failed: Failed to execute git
2025-05-24 11:48:57.483 [info] > git show --textconv 63dc5bf3c394b7b74ba063fd015aa649de876bb8:package.json [10ms]
2025-05-24 11:48:57.488 [info] > git reflog main --grep-reflog=branch: Created from *. [6ms]
2025-05-24 11:48:57.502 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 11:48:57.511 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 11:48:57.519 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 11:48:57.682 [info] > git diff 63dc5bf3c394b7b74ba063fd015aa649de876bb8 -- package.json [2ms]
2025-05-24 11:48:57.728 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 11:48:57.740 [info] > git show -s --decorate=full --shortstat --format=%H%n%aN%n%aE%n%at%n%ct%n%P%n%D%n%B -z 63dc5bf3c394b7b74ba063fd015aa649de876bb8 -- [3ms]
2025-05-24 11:48:57.751 [info] > git show -s --decorate=full --shortstat --format=%H%n%aN%n%aE%n%at%n%ct%n%P%n%D%n%B -z 63dc5bf3c394b7b74ba063fd015aa649de876bb8 -- [3ms]
2025-05-24 11:48:59.303 [info] > git ls-files --stage -- shared/schema.ts [1ms]
2025-05-24 11:48:59.315 [info] > git cat-file -s d32f4ffe71fcf6e84526cd8ee980575cb739b036 [2ms]
2025-05-24 11:48:59.528 [info] > git show --textconv :shared/schema.ts [4ms]
2025-05-24 11:49:02.075 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 11:49:02.076 [info] > git config --get commit.template [8ms]
2025-05-24 11:49:02.076 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 11:49:02.093 [info] > git status -z -uall [10ms]
2025-05-24 11:49:02.094 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 11:49:02.415 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 11:56:46.035 [info] > git fetch [22ms]
2025-05-24 11:56:46.050 [info] > git config --get commit.template [16ms]
2025-05-24 11:56:46.062 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 11:56:46.063 [info] > git config --get commit.template [14ms]
2025-05-24 11:56:46.076 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [14ms]
2025-05-24 11:56:46.078 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 11:56:46.078 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 11:56:46.103 [info] > git status -z -uall [12ms]
2025-05-24 11:56:46.104 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 11:56:46.127 [info] > git blame --root --incremental ac10dde1d079a50eed8574fc6fbc8a5697095273 -- shared/schema.ts [6ms]
2025-05-24 11:56:46.229 [info] > git ls-files --stage -- shared/schema.ts [1ms]
2025-05-24 11:56:46.244 [info] > git cat-file -s d32f4ffe71fcf6e84526cd8ee980575cb739b036 [2ms]
2025-05-24 11:56:46.446 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [25ms]
2025-05-24 11:56:46.447 [info] > git show -s --decorate=full --shortstat --format=%H%n%aN%n%aE%n%at%n%ct%n%P%n%D%n%B -z ac10dde1d079a50eed8574fc6fbc8a5697095273 -- [12ms]
2025-05-24 11:56:46.476 [info] > git show --textconv :shared/schema.ts [30ms]
2025-05-24 11:56:46.476 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [15ms]
2025-05-24 11:56:46.487 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [14ms]
2025-05-24 11:56:46.491 [info] > git config --get --local branch.main.vscode-merge-base [4ms]
2025-05-24 11:56:46.491 [warning] [Git][config] git config failed: Failed to execute git
2025-05-24 11:56:46.506 [info] > git reflog main --grep-reflog=branch: Created from *. [5ms]
2025-05-24 11:56:46.521 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 11:56:46.534 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 11:56:46.549 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 11:56:46.638 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 11:56:46.778 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 11:56:46.802 [info] > git show -s --decorate=full --shortstat --format=%H%n%aN%n%aE%n%at%n%ct%n%P%n%D%n%B -z ac10dde1d079a50eed8574fc6fbc8a5697095273 -- [3ms]
2025-05-24 11:56:46.826 [info] > git show -s --decorate=full --shortstat --format=%H%n%aN%n%aE%n%at%n%ct%n%P%n%D%n%B -z ac10dde1d079a50eed8574fc6fbc8a5697095273 -- [4ms]
2025-05-24 11:56:47.612 [info] > git ls-files --stage -- shared/schema.ts [2ms]
2025-05-24 11:56:47.631 [info] > git cat-file -s d32f4ffe71fcf6e84526cd8ee980575cb739b036 [1ms]
2025-05-24 11:56:47.843 [info] > git show --textconv :shared/schema.ts [2ms]
2025-05-24 11:56:51.119 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 11:56:51.119 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 11:56:51.119 [info] > git config --get commit.template [18ms]
2025-05-24 11:56:51.151 [info] > git status -z -uall [15ms]
2025-05-24 11:56:51.151 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 11:56:51.471 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 11:56:56.202 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 11:56:56.203 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 11:56:56.203 [info] > git config --get commit.template [23ms]
2025-05-24 11:56:56.253 [info] > git status -z -uall [27ms]
2025-05-24 11:56:56.253 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 11:56:56.575 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 11:57:01.293 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 11:57:01.296 [info] > git config --get commit.template [22ms]
2025-05-24 11:57:01.296 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 11:57:01.334 [info] > git status -z -uall [21ms]
2025-05-24 11:57:01.335 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 11:57:01.655 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 11:57:06.377 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 11:57:06.378 [info] > git config --get commit.template [16ms]
2025-05-24 11:57:06.379 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 11:57:06.412 [info] > git status -z -uall [21ms]
2025-05-24 11:57:06.413 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 11:57:06.778 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 11:57:11.441 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 11:57:11.442 [info] > git config --get commit.template [12ms]
2025-05-24 11:57:11.443 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 11:57:11.472 [info] > git status -z -uall [16ms]
2025-05-24 11:57:11.473 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 11:57:11.792 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:14:36.497 [info] > git fetch [30ms]
2025-05-24 12:14:36.525 [info] > git config --get commit.template [29ms]
2025-05-24 12:14:36.555 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:14:36.556 [info] > git config --get commit.template [33ms]
2025-05-24 12:14:36.557 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:14:36.581 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:14:36.582 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:14:36.633 [info] > git status -z -uall [23ms]
2025-05-24 12:14:36.634 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:14:36.686 [info] > git blame --root --incremental 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- shared/schema.ts [23ms]
2025-05-24 12:14:36.686 [info] > git ls-files --stage -- shared/schema.ts [1ms]
2025-05-24 12:14:36.711 [info] > git cat-file -s d32f4ffe71fcf6e84526cd8ee980575cb739b036 [2ms]
2025-05-24 12:14:36.915 [info] > git show --textconv :shared/schema.ts [2ms]
2025-05-24 12:14:36.981 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [21ms]
2025-05-24 12:14:37.000 [info] > git show -s --decorate=full --shortstat --format=%H%n%aN%n%aE%n%at%n%ct%n%P%n%D%n%B -z 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- [20ms]
2025-05-24 12:14:37.052 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [31ms]
2025-05-24 12:14:37.075 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [75ms]
2025-05-24 12:14:37.078 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [31ms]
2025-05-24 12:14:37.078 [info] > git config --get --local branch.main.vscode-merge-base [3ms]
2025-05-24 12:14:37.078 [warning] [Git][config] git config failed: Failed to execute git
2025-05-24 12:14:37.104 [info] > git reflog main --grep-reflog=branch: Created from *. [5ms]
2025-05-24 12:14:37.129 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 12:14:37.154 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:14:37.179 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:14:37.419 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:14:37.445 [info] > git show -s --decorate=full --shortstat --format=%H%n%aN%n%aE%n%at%n%ct%n%P%n%D%n%B -z 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- [2ms]
2025-05-24 12:14:37.474 [info] > git show -s --decorate=full --shortstat --format=%H%n%aN%n%aE%n%at%n%ct%n%P%n%D%n%B -z 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- [3ms]
2025-05-24 12:14:38.050 [info] > git ls-files --stage -- shared/schema.ts [2ms]
2025-05-24 12:14:38.074 [info] > git cat-file -s d32f4ffe71fcf6e84526cd8ee980575cb739b036 [1ms]
2025-05-24 12:14:38.288 [info] > git show --textconv :shared/schema.ts [2ms]
2025-05-24 12:14:41.611 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:14:41.611 [info] > git config --get commit.template [25ms]
2025-05-24 12:14:41.612 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:14:41.661 [info] > git status -z -uall [23ms]
2025-05-24 12:14:41.663 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:14:41.989 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:14:47.071 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:14:47.072 [info] > git config --get commit.template [26ms]
2025-05-24 12:14:47.073 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:14:47.124 [info] > git status -z -uall [26ms]
2025-05-24 12:14:47.125 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:14:47.454 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:14:52.156 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:14:52.256 [info] > git config --get commit.template [100ms]
2025-05-24 12:14:52.257 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [68ms]
2025-05-24 12:14:52.364 [info] > git status -z -uall [38ms]
2025-05-24 12:14:52.365 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 12:14:52.696 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:14:57.410 [info] > git config --get commit.template [5ms]
2025-05-24 12:14:57.440 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:14:57.442 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:14:57.491 [info] > git status -z -uall [26ms]
2025-05-24 12:14:57.492 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:14:57.866 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [49ms]
2025-05-24 12:15:02.518 [info] > git config --get commit.template [1ms]
2025-05-24 12:15:02.541 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:15:02.543 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:15:02.585 [info] > git status -z -uall [22ms]
2025-05-24 12:15:02.586 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:15:02.910 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:15:07.676 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:15:07.693 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [22ms]
2025-05-24 12:15:07.694 [info] > git config --get commit.template [69ms]
2025-05-24 12:15:07.771 [info] > git status -z -uall [31ms]
2025-05-24 12:15:07.774 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 12:15:08.114 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-05-24 12:15:12.851 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:15:12.924 [info] > git config --get commit.template [118ms]
2025-05-24 12:15:12.925 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [74ms]
2025-05-24 12:15:13.050 [info] > git status -z -uall [63ms]
2025-05-24 12:15:13.061 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [12ms]
2025-05-24 12:15:13.422 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 12:15:18.129 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:15:18.130 [info] > git config --get commit.template [38ms]
2025-05-24 12:15:18.131 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:15:18.188 [info] > git status -z -uall [28ms]
2025-05-24 12:15:18.190 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 12:15:18.529 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-05-24 12:15:23.242 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:15:23.243 [info] > git config --get commit.template [25ms]
2025-05-24 12:15:23.244 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:15:23.288 [info] > git status -z -uall [22ms]
2025-05-24 12:15:23.288 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:15:23.617 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:15:28.347 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:15:28.348 [info] > git config --get commit.template [30ms]
2025-05-24 12:15:28.349 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:15:28.396 [info] > git status -z -uall [22ms]
2025-05-24 12:15:28.397 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:15:28.728 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:15:33.456 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:15:33.456 [info] > git config --get commit.template [29ms]
2025-05-24 12:15:33.457 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:15:33.510 [info] > git status -z -uall [26ms]
2025-05-24 12:15:33.511 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:15:33.939 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [88ms]
2025-05-24 12:15:38.620 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:15:38.623 [info] > git config --get commit.template [54ms]
2025-05-24 12:15:38.627 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-05-24 12:15:38.742 [info] > git status -z -uall [79ms]
2025-05-24 12:15:38.742 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 12:15:39.115 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 12:15:43.790 [info] > git config --get commit.template [2ms]
2025-05-24 12:15:43.842 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:15:43.845 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:15:43.929 [info] > git status -z -uall [45ms]
2025-05-24 12:15:43.930 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 12:15:44.274 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-05-24 12:15:48.991 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:15:48.992 [info] > git config --get commit.template [31ms]
2025-05-24 12:15:48.993 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:15:49.057 [info] > git status -z -uall [37ms]
2025-05-24 12:15:49.058 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 12:15:49.388 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:15:54.106 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:15:54.106 [info] > git config --get commit.template [24ms]
2025-05-24 12:15:54.107 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:15:54.150 [info] > git status -z -uall [21ms]
2025-05-24 12:15:54.151 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:15:54.476 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:15:59.208 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:15:59.208 [info] > git config --get commit.template [27ms]
2025-05-24 12:15:59.209 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:15:59.262 [info] > git status -z -uall [25ms]
2025-05-24 12:15:59.263 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 12:15:59.597 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:16:04.313 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:16:04.314 [info] > git config --get commit.template [27ms]
2025-05-24 12:16:04.315 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:16:04.373 [info] > git status -z -uall [33ms]
2025-05-24 12:16:04.373 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-05-24 12:16:04.701 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:16:09.451 [info] > git config --get commit.template [37ms]
2025-05-24 12:16:09.452 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:16:09.453 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:16:09.617 [info] > git status -z -uall [113ms]
2025-05-24 12:16:09.617 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [81ms]
2025-05-24 12:16:09.962 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:16:14.686 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:16:14.686 [info] > git config --get commit.template [27ms]
2025-05-24 12:16:14.687 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:16:14.749 [info] > git status -z -uall [29ms]
2025-05-24 12:16:14.749 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 12:16:15.091 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:16:19.790 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:16:19.829 [info] > git config --get commit.template [40ms]
2025-05-24 12:16:19.830 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:16:19.886 [info] > git status -z -uall [29ms]
2025-05-24 12:16:19.887 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:16:20.215 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:16:24.966 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:16:24.967 [info] > git config --get commit.template [34ms]
2025-05-24 12:16:24.968 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:16:25.027 [info] > git status -z -uall [28ms]
2025-05-24 12:16:25.028 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:16:25.353 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:16:30.121 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:16:30.121 [info] > git config --get commit.template [38ms]
2025-05-24 12:16:30.123 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:16:30.179 [info] > git status -z -uall [30ms]
2025-05-24 12:16:30.182 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 12:16:30.522 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 12:16:35.265 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:16:35.266 [info] > git config --get commit.template [46ms]
2025-05-24 12:16:35.267 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:16:35.332 [info] > git status -z -uall [36ms]
2025-05-24 12:16:35.335 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 12:16:35.665 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:16:40.384 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:16:40.384 [info] > git config --get commit.template [23ms]
2025-05-24 12:16:40.385 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:16:40.431 [info] > git status -z -uall [22ms]
2025-05-24 12:16:40.432 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:16:40.759 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:16:45.467 [info] > git config --get commit.template [1ms]
2025-05-24 12:16:45.491 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:16:45.492 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:16:45.535 [info] > git status -z -uall [21ms]
2025-05-24 12:16:45.536 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:16:45.905 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:17:24.371 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:17:24.372 [info] > git config --get commit.template [26ms]
2025-05-24 12:17:24.373 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:17:24.421 [info] > git status -z -uall [24ms]
2025-05-24 12:17:24.422 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:17:24.749 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:17:29.545 [info] > git config --get commit.template [98ms]
2025-05-24 12:17:29.568 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:17:29.570 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:17:29.629 [info] > git status -z -uall [32ms]
2025-05-24 12:17:29.629 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-05-24 12:17:29.981 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:17:34.686 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:17:34.687 [info] > git config --get commit.template [24ms]
2025-05-24 12:17:34.688 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:17:34.730 [info] > git status -z -uall [21ms]
2025-05-24 12:17:34.731 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:17:35.067 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-05-24 12:17:36.695 [info] > git fetch [4ms]
2025-05-24 12:17:36.745 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:17:36.746 [info] > git config --get commit.template [23ms]
2025-05-24 12:17:36.747 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:17:36.801 [info] > git status -z -uall [28ms]
2025-05-24 12:17:36.802 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 12:17:37.137 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:17:38.304 [info] > git ls-files --stage -- shared/schema.ts [2ms]
2025-05-24 12:17:38.344 [info] > git cat-file -s d32f4ffe71fcf6e84526cd8ee980575cb739b036 [2ms]
2025-05-24 12:17:38.544 [info] > git show --textconv :shared/schema.ts [1ms]
2025-05-24 12:17:39.784 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:17:39.784 [info] > git config --get commit.template [25ms]
2025-05-24 12:17:39.785 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:17:39.885 [info] > git status -z -uall [78ms]
2025-05-24 12:17:39.885 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [54ms]
2025-05-24 12:17:40.212 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:17:44.924 [info] > git config --get commit.template [2ms]
2025-05-24 12:17:44.946 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:17:44.948 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:17:44.995 [info] > git status -z -uall [24ms]
2025-05-24 12:17:44.996 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:17:45.329 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:17:50.055 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:17:50.056 [info] > git config --get commit.template [29ms]
2025-05-24 12:17:50.057 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:17:50.129 [info] > git status -z -uall [35ms]
2025-05-24 12:17:50.129 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 12:17:50.462 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:17:55.199 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:17:55.199 [info] > git config --get commit.template [33ms]
2025-05-24 12:17:55.200 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:17:55.295 [info] > git status -z -uall [43ms]
2025-05-24 12:17:55.298 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 12:17:55.637 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:18:00.354 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:18:00.355 [info] > git config --get commit.template [25ms]
2025-05-24 12:18:00.356 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:18:00.407 [info] > git status -z -uall [24ms]
2025-05-24 12:18:00.409 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:18:00.735 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:18:05.479 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:18:05.480 [info] > git config --get commit.template [35ms]
2025-05-24 12:18:05.485 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-05-24 12:18:05.542 [info] > git status -z -uall [28ms]
2025-05-24 12:18:05.543 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:18:05.872 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:18:10.570 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:18:10.596 [info] > git config --get commit.template [27ms]
2025-05-24 12:18:10.598 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:18:10.652 [info] > git status -z -uall [27ms]
2025-05-24 12:18:10.654 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 12:18:10.978 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:18:15.682 [info] > git config --get commit.template [2ms]
2025-05-24 12:18:15.718 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:18:15.719 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:18:15.770 [info] > git status -z -uall [23ms]
2025-05-24 12:18:15.771 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 12:18:16.098 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:18:20.824 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:18:20.824 [info] > git config --get commit.template [27ms]
2025-05-24 12:18:20.825 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:18:20.885 [info] > git status -z -uall [24ms]
2025-05-24 12:18:20.885 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 12:18:21.218 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:18:25.937 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:18:25.938 [info] > git config --get commit.template [26ms]
2025-05-24 12:18:25.938 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:18:25.994 [info] > git status -z -uall [31ms]
2025-05-24 12:18:25.995 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:18:26.327 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:18:26.544 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-05-24 12:18:26.578 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/Checkout.tsx [2ms]
2025-05-24 12:18:26.781 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/Checkout.tsx [4ms]
2025-05-24 12:18:31.027 [info] > git config --get commit.template [3ms]
2025-05-24 12:18:31.051 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:18:31.052 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:18:31.096 [info] > git status -z -uall [22ms]
2025-05-24 12:18:31.097 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:18:31.424 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:18:36.155 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:18:36.156 [info] > git config --get commit.template [29ms]
2025-05-24 12:18:36.157 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:18:36.223 [info] > git status -z -uall [32ms]
2025-05-24 12:18:36.224 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:18:36.563 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:18:41.268 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:18:41.268 [info] > git config --get commit.template [20ms]
2025-05-24 12:18:41.269 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:18:41.319 [info] > git status -z -uall [32ms]
2025-05-24 12:18:41.320 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:18:41.647 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:18:46.405 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:18:46.407 [info] > git config --get commit.template [37ms]
2025-05-24 12:18:46.407 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:18:46.465 [info] > git status -z -uall [29ms]
2025-05-24 12:18:46.468 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 12:18:46.797 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:18:51.532 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:18:51.533 [info] > git config --get commit.template [31ms]
2025-05-24 12:18:51.534 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:18:51.623 [info] > git status -z -uall [51ms]
2025-05-24 12:18:51.625 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 12:18:51.973 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-05-24 12:18:56.699 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:18:56.700 [info] > git config --get commit.template [27ms]
2025-05-24 12:18:56.701 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:18:56.766 [info] > git status -z -uall [27ms]
2025-05-24 12:18:56.767 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:18:57.094 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:19:01.821 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:19:01.823 [info] > git config --get commit.template [25ms]
2025-05-24 12:19:01.823 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:19:01.872 [info] > git status -z -uall [27ms]
2025-05-24 12:19:01.873 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:19:02.201 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:19:06.929 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:19:06.931 [info] > git config --get commit.template [25ms]
2025-05-24 12:19:06.931 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:19:06.989 [info] > git status -z -uall [33ms]
2025-05-24 12:19:06.990 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-05-24 12:19:07.315 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:19:12.038 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:19:12.039 [info] > git config --get commit.template [20ms]
2025-05-24 12:19:12.039 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:19:12.089 [info] > git status -z -uall [26ms]
2025-05-24 12:19:12.095 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-05-24 12:19:12.422 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:19:17.161 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:19:17.163 [info] > git config --get commit.template [39ms]
2025-05-24 12:19:17.163 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:19:17.207 [info] > git status -z -uall [22ms]
2025-05-24 12:19:17.208 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 12:19:17.535 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:19:22.259 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:19:22.260 [info] > git config --get commit.template [26ms]
2025-05-24 12:19:22.261 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:19:22.365 [info] > git status -z -uall [73ms]
2025-05-24 12:19:22.366 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [51ms]
2025-05-24 12:19:22.699 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:19:27.415 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:19:27.416 [info] > git config --get commit.template [24ms]
2025-05-24 12:19:27.417 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:19:27.468 [info] > git status -z -uall [27ms]
2025-05-24 12:19:27.468 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:19:27.798 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:19:32.494 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:19:32.519 [info] > git config --get commit.template [25ms]
2025-05-24 12:19:32.520 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:19:32.567 [info] > git status -z -uall [26ms]
2025-05-24 12:19:32.568 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:19:32.936 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [13ms]
2025-05-24 12:19:37.637 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:19:37.638 [info] > git config --get commit.template [38ms]
2025-05-24 12:19:37.639 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:19:37.684 [info] > git status -z -uall [20ms]
2025-05-24 12:19:37.686 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:19:38.016 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:19:42.760 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:19:42.761 [info] > git config --get commit.template [35ms]
2025-05-24 12:19:42.761 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:19:42.835 [info] > git status -z -uall [34ms]
2025-05-24 12:19:42.837 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:19:43.195 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [14ms]
2025-05-24 12:19:53.430 [info] > git config --get commit.template [5ms]
2025-05-24 12:19:53.467 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:19:53.468 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:19:53.540 [info] > git status -z -uall [37ms]
2025-05-24 12:19:53.543 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 12:19:53.881 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:20:02.463 [info] > git config --get commit.template [37ms]
2025-05-24 12:20:02.463 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:20:02.466 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 12:20:02.554 [info] > git status -z -uall [42ms]
2025-05-24 12:20:02.558 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-05-24 12:20:02.901 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:20:11.411 [info] > git config --get commit.template [1ms]
2025-05-24 12:20:11.435 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:20:11.437 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:20:11.495 [info] > git status -z -uall [36ms]
2025-05-24 12:20:11.495 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 12:20:11.823 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:20:16.524 [info] > git config --get commit.template [2ms]
2025-05-24 12:20:16.553 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:20:16.554 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:20:16.602 [info] > git status -z -uall [24ms]
2025-05-24 12:20:16.603 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 12:20:16.930 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:20:21.656 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:20:21.657 [info] > git config --get commit.template [28ms]
2025-05-24 12:20:21.659 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:20:21.711 [info] > git status -z -uall [26ms]
2025-05-24 12:20:21.712 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:20:22.042 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:25:05.026 [info] > git fetch [36ms]
2025-05-24 12:25:05.027 [info] > git config --get commit.template [2ms]
2025-05-24 12:25:05.084 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:25:05.090 [info] > git config --get commit.template [35ms]
2025-05-24 12:25:05.129 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:25:05.130 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [47ms]
2025-05-24 12:25:05.133 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 12:25:05.223 [info] > git status -z -uall [49ms]
2025-05-24 12:25:05.223 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [10ms]
2025-05-24 12:25:05.550 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:25:06.554 [info] > git ls-files --stage -- shared/schema.ts [1ms]
2025-05-24 12:25:06.579 [info] > git cat-file -s d32f4ffe71fcf6e84526cd8ee980575cb739b036 [2ms]
2025-05-24 12:25:06.803 [info] > git show --textconv :shared/schema.ts [2ms]
2025-05-24 12:25:19.116 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:25:19.116 [info] > git config --get commit.template [22ms]
2025-05-24 12:25:19.117 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:25:19.161 [info] > git status -z -uall [22ms]
2025-05-24 12:25:19.162 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:25:19.487 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:25:28.059 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:25:28.060 [info] > git config --get commit.template [22ms]
2025-05-24 12:25:28.061 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:25:28.104 [info] > git status -z -uall [23ms]
2025-05-24 12:25:28.107 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 12:25:28.439 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:25:34.086 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:25:34.086 [info] > git config --get commit.template [16ms]
2025-05-24 12:25:34.087 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:25:34.127 [info] > git status -z -uall [20ms]
2025-05-24 12:25:34.128 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:25:34.454 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:25:39.174 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:25:39.175 [info] > git config --get commit.template [22ms]
2025-05-24 12:25:39.176 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:25:39.228 [info] > git status -z -uall [26ms]
2025-05-24 12:25:39.230 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:25:39.558 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:25:48.561 [info] > git config --get commit.template [2ms]
2025-05-24 12:25:48.579 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:25:48.581 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:25:48.630 [info] > git status -z -uall [26ms]
2025-05-24 12:25:48.632 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 12:25:48.957 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:25:53.674 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:25:53.675 [info] > git config --get commit.template [19ms]
2025-05-24 12:25:53.675 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:25:53.716 [info] > git status -z -uall [20ms]
2025-05-24 12:25:53.717 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 12:25:54.045 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 12:25:58.779 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:25:58.780 [info] > git config --get commit.template [31ms]
2025-05-24 12:25:58.780 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:25:58.846 [info] > git status -z -uall [34ms]
2025-05-24 12:25:58.848 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 12:25:59.176 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:26:03.902 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:26:03.903 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:26:03.903 [info] > git config --get commit.template [30ms]
2025-05-24 12:26:03.964 [info] > git status -z -uall [33ms]
2025-05-24 12:26:03.966 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:26:04.291 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:26:32.905 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:26:32.906 [info] > git config --get commit.template [29ms]
2025-05-24 12:26:32.907 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:26:32.965 [info] > git status -z -uall [32ms]
2025-05-24 12:26:32.965 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 12:26:33.296 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 12:26:38.007 [info] > git config --get commit.template [0ms]
2025-05-24 12:26:38.041 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:26:38.042 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:26:38.138 [info] > git status -z -uall [56ms]
2025-05-24 12:26:38.140 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 12:26:38.474 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:26:43.204 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:26:43.205 [info] > git config --get commit.template [34ms]
2025-05-24 12:26:43.206 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:26:43.274 [info] > git status -z -uall [33ms]
2025-05-24 12:26:43.275 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 12:26:43.606 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 12:26:48.336 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:26:48.337 [info] > git config --get commit.template [32ms]
2025-05-24 12:26:48.338 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:26:48.382 [info] > git status -z -uall [23ms]
2025-05-24 12:26:48.383 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:26:48.724 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:26:56.048 [info] > git config --get commit.template [2ms]
2025-05-24 12:26:56.090 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:26:56.092 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:26:56.161 [info] > git status -z -uall [35ms]
2025-05-24 12:26:56.162 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:26:56.493 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:27:01.224 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:27:01.224 [info] > git config --get commit.template [37ms]
2025-05-24 12:27:01.225 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:27:01.278 [info] > git status -z -uall [27ms]
2025-05-24 12:27:01.279 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:27:01.607 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:27:06.336 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:27:06.337 [info] > git config --get commit.template [33ms]
2025-05-24 12:27:06.338 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:27:06.396 [info] > git status -z -uall [29ms]
2025-05-24 12:27:06.397 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:27:06.728 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:27:11.427 [info] > git config --get commit.template [0ms]
2025-05-24 12:27:11.453 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:27:11.455 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:27:11.502 [info] > git status -z -uall [24ms]
2025-05-24 12:27:11.504 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:27:11.836 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 12:27:18.967 [info] > git config --get commit.template [1ms]
2025-05-24 12:27:18.997 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:27:18.999 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:27:19.055 [info] > git status -z -uall [24ms]
2025-05-24 12:27:19.056 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:27:19.431 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [48ms]
2025-05-24 12:27:24.113 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:27:24.115 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:27:24.115 [info] > git config --get commit.template [32ms]
2025-05-24 12:27:24.169 [info] > git status -z -uall [27ms]
2025-05-24 12:27:24.170 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:27:24.501 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:27:29.230 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:27:29.232 [info] > git config --get commit.template [30ms]
2025-05-24 12:27:29.232 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:27:29.320 [info] > git status -z -uall [45ms]
2025-05-24 12:27:29.322 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 12:27:29.787 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [93ms]
2025-05-24 12:29:20.197 [info] > git fetch [80ms]
2025-05-24 12:29:20.225 [info] > git config --get commit.template [78ms]
2025-05-24 12:29:20.254 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:29:20.256 [info] > git config --get commit.template [34ms]
2025-05-24 12:29:20.257 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:29:20.285 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:29:20.290 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-05-24 12:29:20.345 [info] > git status -z -uall [30ms]
2025-05-24 12:29:20.346 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:29:20.673 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:29:22.145 [info] > git ls-files --stage -- shared/schema.ts [62ms]
2025-05-24 12:29:22.200 [info] > git cat-file -s d32f4ffe71fcf6e84526cd8ee980575cb739b036 [3ms]
2025-05-24 12:29:23.058 [info] > git show --textconv :shared/schema.ts [67ms]
2025-05-24 12:29:25.305 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:29:25.305 [info] > git config --get commit.template [22ms]
2025-05-24 12:29:25.306 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:29:25.356 [info] > git status -z -uall [26ms]
2025-05-24 12:29:25.356 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 12:29:25.683 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:29:30.414 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:29:30.414 [info] > git config --get commit.template [29ms]
2025-05-24 12:29:30.415 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:29:30.465 [info] > git status -z -uall [25ms]
2025-05-24 12:29:30.466 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:29:30.796 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:32:50.004 [info] > git fetch [26ms]
2025-05-24 12:32:50.032 [info] > git config --get commit.template [28ms]
2025-05-24 12:32:50.055 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:32:50.056 [info] > git config --get commit.template [23ms]
2025-05-24 12:32:50.056 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:32:50.080 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [25ms]
2025-05-24 12:32:50.081 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:32:50.122 [info] > git status -z -uall [20ms]
2025-05-24 12:32:50.123 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:32:50.448 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:32:50.676 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:32:50.706 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/Checkout.tsx [2ms]
2025-05-24 12:32:50.904 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/Checkout.tsx [3ms]
2025-05-24 12:32:51.552 [info] > git ls-files --stage -- shared/schema.ts [2ms]
2025-05-24 12:32:51.579 [info] > git cat-file -s d32f4ffe71fcf6e84526cd8ee980575cb739b036 [1ms]
2025-05-24 12:32:51.785 [info] > git show --textconv :shared/schema.ts [1ms]
2025-05-24 12:32:55.126 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:32:55.127 [info] > git config --get commit.template [22ms]
2025-05-24 12:32:55.128 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:32:55.170 [info] > git status -z -uall [21ms]
2025-05-24 12:32:55.172 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:32:55.548 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 12:33:00.226 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:33:00.226 [info] > git config --get commit.template [26ms]
2025-05-24 12:33:00.228 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:33:00.282 [info] > git status -z -uall [25ms]
2025-05-24 12:33:00.284 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:33:00.610 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:33:05.339 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:33:05.339 [info] > git config --get commit.template [23ms]
2025-05-24 12:33:05.340 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:33:05.394 [info] > git status -z -uall [28ms]
2025-05-24 12:33:05.394 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 12:33:05.780 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:33:10.451 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:33:10.452 [info] > git config --get commit.template [27ms]
2025-05-24 12:33:10.458 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-05-24 12:33:10.568 [info] > git status -z -uall [76ms]
2025-05-24 12:33:10.568 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [50ms]
2025-05-24 12:33:10.895 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:33:15.597 [info] > git config --get commit.template [2ms]
2025-05-24 12:33:15.632 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:33:15.636 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 12:33:15.696 [info] > git status -z -uall [32ms]
2025-05-24 12:33:15.698 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 12:33:16.029 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:33:20.759 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:33:20.759 [info] > git config --get commit.template [36ms]
2025-05-24 12:33:20.761 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:33:20.831 [info] > git status -z -uall [37ms]
2025-05-24 12:33:20.835 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 12:33:21.175 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:33:25.886 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:33:25.886 [info] > git config --get commit.template [24ms]
2025-05-24 12:33:25.887 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:33:25.935 [info] > git status -z -uall [24ms]
2025-05-24 12:33:25.936 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 12:33:26.263 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:33:31.063 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:33:31.063 [info] > git config --get commit.template [24ms]
2025-05-24 12:33:31.064 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:33:31.116 [info] > git status -z -uall [29ms]
2025-05-24 12:33:31.118 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 12:33:31.444 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 12:33:36.169 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:33:36.170 [info] > git config --get commit.template [26ms]
2025-05-24 12:33:36.171 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:33:36.228 [info] > git status -z -uall [27ms]
2025-05-24 12:33:36.230 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:33:36.562 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:33:41.281 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:33:41.282 [info] > git config --get commit.template [25ms]
2025-05-24 12:33:41.283 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:33:41.344 [info] > git status -z -uall [36ms]
2025-05-24 12:33:41.346 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 12:33:41.675 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:33:46.395 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:33:46.395 [info] > git config --get commit.template [24ms]
2025-05-24 12:33:46.396 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:33:46.451 [info] > git status -z -uall [30ms]
2025-05-24 12:33:46.451 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 12:33:46.777 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:33:51.511 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:33:51.512 [info] > git config --get commit.template [31ms]
2025-05-24 12:33:51.514 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:33:51.574 [info] > git status -z -uall [27ms]
2025-05-24 12:33:51.575 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:33:51.907 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:33:56.603 [info] > git config --get commit.template [2ms]
2025-05-24 12:33:56.630 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:33:56.631 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:33:56.686 [info] > git status -z -uall [30ms]
2025-05-24 12:33:56.687 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:33:57.019 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:34:01.716 [info] > git config --get commit.template [2ms]
2025-05-24 12:34:01.744 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:34:01.751 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [8ms]
2025-05-24 12:34:01.811 [info] > git status -z -uall [33ms]
2025-05-24 12:34:01.811 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 12:34:02.145 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:34:06.860 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:34:06.861 [info] > git config --get commit.template [24ms]
2025-05-24 12:34:06.862 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:34:06.912 [info] > git status -z -uall [24ms]
2025-05-24 12:34:06.912 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 12:34:07.237 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:34:11.964 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:34:11.965 [info] > git config --get commit.template [26ms]
2025-05-24 12:34:11.966 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:34:12.015 [info] > git status -z -uall [26ms]
2025-05-24 12:34:12.017 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:34:12.351 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:34:17.065 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:34:17.066 [info] > git config --get commit.template [25ms]
2025-05-24 12:34:17.066 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:34:17.117 [info] > git status -z -uall [26ms]
2025-05-24 12:34:17.121 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-05-24 12:34:17.452 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:34:51.960 [info] > git config --get commit.template [2ms]
2025-05-24 12:34:51.989 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:34:51.994 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-05-24 12:34:52.063 [info] > git status -z -uall [35ms]
2025-05-24 12:34:52.064 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:34:52.405 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:34:52.647 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:34:52.691 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/Checkout.tsx [3ms]
2025-05-24 12:34:52.901 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/Checkout.tsx [4ms]
2025-05-24 12:41:28.730 [info] > git fetch [28ms]
2025-05-24 12:41:28.759 [info] > git config --get commit.template [30ms]
2025-05-24 12:41:28.783 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:41:28.785 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:41:28.786 [info] > git config --get commit.template [28ms]
2025-05-24 12:41:28.813 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:41:28.815 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:41:28.858 [info] > git status -z -uall [22ms]
2025-05-24 12:41:28.859 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:41:29.185 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:41:29.393 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:41:29.429 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/Checkout.tsx [2ms]
2025-05-24 12:41:29.635 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/Checkout.tsx [3ms]
2025-05-24 12:41:30.270 [info] > git ls-files --stage -- shared/schema.ts [2ms]
2025-05-24 12:41:30.301 [info] > git cat-file -s d32f4ffe71fcf6e84526cd8ee980575cb739b036 [2ms]
2025-05-24 12:41:30.524 [info] > git show --textconv :shared/schema.ts [0ms]
2025-05-24 12:41:33.845 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:41:33.846 [info] > git config --get commit.template [34ms]
2025-05-24 12:41:33.846 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:41:33.903 [info] > git status -z -uall [30ms]
2025-05-24 12:41:33.905 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:41:34.236 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:41:38.965 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:41:38.966 [info] > git config --get commit.template [31ms]
2025-05-24 12:41:38.967 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:41:39.014 [info] > git status -z -uall [25ms]
2025-05-24 12:41:39.015 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:41:39.351 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:41:44.137 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:41:44.137 [info] > git config --get commit.template [67ms]
2025-05-24 12:41:44.151 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [15ms]
2025-05-24 12:41:44.248 [info] > git status -z -uall [43ms]
2025-05-24 12:41:44.251 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 12:41:44.589 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:41:49.314 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:41:49.315 [info] > git config --get commit.template [33ms]
2025-05-24 12:41:49.317 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:41:49.379 [info] > git status -z -uall [34ms]
2025-05-24 12:41:49.380 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:41:49.707 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:42:01.443 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:42:01.444 [info] > git config --get commit.template [25ms]
2025-05-24 12:42:01.445 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:42:01.507 [info] > git status -z -uall [34ms]
2025-05-24 12:42:01.507 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 12:42:01.837 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:42:06.559 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:42:06.559 [info] > git config --get commit.template [23ms]
2025-05-24 12:42:06.560 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:42:06.610 [info] > git status -z -uall [27ms]
2025-05-24 12:42:06.611 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:42:06.942 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:42:11.642 [info] > git config --get commit.template [6ms]
2025-05-24 12:42:11.677 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:42:11.678 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:42:11.733 [info] > git status -z -uall [29ms]
2025-05-24 12:42:11.739 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-05-24 12:42:12.077 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:44:10.144 [info] > git config --get commit.template [2ms]
2025-05-24 12:44:10.172 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:44:10.174 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:44:10.223 [info] > git status -z -uall [26ms]
2025-05-24 12:44:10.225 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:44:10.555 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:44:10.766 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:44:10.800 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/Checkout.tsx [3ms]
2025-05-24 12:44:11.004 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/Checkout.tsx [4ms]
2025-05-24 12:44:15.325 [info] > git config --get commit.template [28ms]
2025-05-24 12:44:15.357 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:44:15.357 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-05-24 12:44:15.418 [info] > git status -z -uall [37ms]
2025-05-24 12:44:15.419 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 12:44:15.749 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:44:15.956 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:44:15.990 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/Checkout.tsx [3ms]
2025-05-24 12:44:16.196 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/Checkout.tsx [3ms]
2025-05-24 12:46:05.686 [info] > git fetch [24ms]
2025-05-24 12:46:05.744 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:46:05.744 [info] > git config --get commit.template [59ms]
2025-05-24 12:46:05.745 [info] > git config --get commit.template [36ms]
2025-05-24 12:46:05.768 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:46:05.769 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [25ms]
2025-05-24 12:46:05.770 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:46:05.832 [info] > git status -z -uall [32ms]
2025-05-24 12:46:05.832 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 12:46:06.169 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:46:06.392 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:46:06.435 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/Checkout.tsx [4ms]
2025-05-24 12:46:06.637 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/Checkout.tsx [3ms]
2025-05-24 12:46:07.240 [info] > git ls-files --stage -- shared/schema.ts [2ms]
2025-05-24 12:46:07.270 [info] > git cat-file -s d32f4ffe71fcf6e84526cd8ee980575cb739b036 [1ms]
2025-05-24 12:46:07.477 [info] > git show --textconv :shared/schema.ts [2ms]
2025-05-24 12:46:10.819 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:46:10.819 [info] > git config --get commit.template [25ms]
2025-05-24 12:46:10.820 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:46:10.875 [info] > git status -z -uall [32ms]
2025-05-24 12:46:10.876 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:46:11.207 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:46:15.929 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:46:15.929 [info] > git config --get commit.template [22ms]
2025-05-24 12:46:15.930 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:46:15.987 [info] > git status -z -uall [30ms]
2025-05-24 12:46:15.987 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:46:16.317 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:46:21.045 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:46:21.045 [info] > git config --get commit.template [26ms]
2025-05-24 12:46:21.053 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [8ms]
2025-05-24 12:46:21.105 [info] > git status -z -uall [27ms]
2025-05-24 12:46:21.106 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:46:21.437 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:46:26.157 [info] > git config --get commit.template [1ms]
2025-05-24 12:46:26.185 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:46:26.187 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:46:26.246 [info] > git status -z -uall [28ms]
2025-05-24 12:46:26.248 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 12:46:26.585 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:46:31.301 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:46:31.302 [info] > git config --get commit.template [29ms]
2025-05-24 12:46:31.303 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:46:31.353 [info] > git status -z -uall [26ms]
2025-05-24 12:46:31.354 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:46:31.685 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:46:36.442 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:46:36.442 [info] > git config --get commit.template [48ms]
2025-05-24 12:46:36.443 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:46:36.497 [info] > git status -z -uall [25ms]
2025-05-24 12:46:36.500 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 12:46:36.828 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:46:37.074 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:46:37.164 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/Checkout.tsx [58ms]
2025-05-24 12:46:37.347 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/Checkout.tsx [5ms]
2025-05-24 12:46:41.569 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:46:41.570 [info] > git config --get commit.template [35ms]
2025-05-24 12:46:41.570 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:46:41.617 [info] > git status -z -uall [25ms]
2025-05-24 12:46:41.619 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:46:41.943 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:46:46.704 [info] > git config --get commit.template [4ms]
2025-05-24 12:46:46.730 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:46:46.734 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 12:46:46.785 [info] > git status -z -uall [28ms]
2025-05-24 12:46:46.786 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:46:47.114 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:46:51.849 [info] > git config --get commit.template [32ms]
2025-05-24 12:46:51.850 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:46:51.850 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:46:51.908 [info] > git status -z -uall [32ms]
2025-05-24 12:46:51.910 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 12:46:52.242 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:46:56.949 [info] > git config --get commit.template [4ms]
2025-05-24 12:46:56.977 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:46:56.979 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:46:57.035 [info] > git status -z -uall [31ms]
2025-05-24 12:46:57.037 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:46:57.373 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:47:47.139 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:47:47.139 [info] > git config --get commit.template [25ms]
2025-05-24 12:47:47.141 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:47:47.200 [info] > git status -z -uall [27ms]
2025-05-24 12:47:47.200 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 12:47:47.531 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:47:52.233 [info] > git config --get commit.template [1ms]
2025-05-24 12:47:52.265 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:47:52.266 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:47:52.318 [info] > git status -z -uall [26ms]
2025-05-24 12:47:52.320 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:47:52.707 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:47:57.369 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:47:57.370 [info] > git config --get commit.template [23ms]
2025-05-24 12:47:57.371 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:47:57.423 [info] > git status -z -uall [30ms]
2025-05-24 12:47:57.425 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 12:47:57.757 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:48:02.486 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:48:02.486 [info] > git config --get commit.template [25ms]
2025-05-24 12:48:02.487 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:48:02.538 [info] > git status -z -uall [23ms]
2025-05-24 12:48:02.539 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:48:02.869 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:48:07.587 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:48:07.588 [info] > git config --get commit.template [22ms]
2025-05-24 12:48:07.589 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:48:07.647 [info] > git status -z -uall [30ms]
2025-05-24 12:48:07.650 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 12:48:07.977 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:50:20.465 [info] > git fetch [40ms]
2025-05-24 12:50:20.527 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:50:20.528 [info] > git config --get commit.template [27ms]
2025-05-24 12:50:20.528 [info] > git config --get commit.template [65ms]
2025-05-24 12:50:20.551 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:50:20.552 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [26ms]
2025-05-24 12:50:20.553 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:50:20.606 [info] > git status -z -uall [29ms]
2025-05-24 12:50:20.607 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:50:20.939 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:50:21.147 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:50:21.182 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/Checkout.tsx [3ms]
2025-05-24 12:50:21.394 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/Checkout.tsx [12ms]
2025-05-24 12:50:22.078 [info] > git ls-files --stage -- shared/schema.ts [2ms]
2025-05-24 12:50:22.104 [info] > git cat-file -s d32f4ffe71fcf6e84526cd8ee980575cb739b036 [2ms]
2025-05-24 12:50:22.322 [info] > git show --textconv :shared/schema.ts [2ms]
2025-05-24 12:50:25.611 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:50:25.611 [info] > git config --get commit.template [28ms]
2025-05-24 12:50:25.612 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:50:25.714 [info] > git status -z -uall [76ms]
2025-05-24 12:50:25.715 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [53ms]
2025-05-24 12:50:26.042 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:50:32.470 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:50:32.470 [info] > git config --get commit.template [32ms]
2025-05-24 12:50:32.471 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:50:32.532 [info] > git status -z -uall [31ms]
2025-05-24 12:50:32.532 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 12:50:32.868 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:50:37.588 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:50:37.589 [info] > git config --get commit.template [30ms]
2025-05-24 12:50:37.589 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:50:37.705 [info] > git status -z -uall [89ms]
2025-05-24 12:50:37.705 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [60ms]
2025-05-24 12:50:38.037 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:50:42.759 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:50:42.759 [info] > git config --get commit.template [23ms]
2025-05-24 12:50:42.760 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:50:42.808 [info] > git status -z -uall [25ms]
2025-05-24 12:50:42.809 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:50:43.136 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:53:12.377 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:53:12.377 [info] > git config --get commit.template [26ms]
2025-05-24 12:53:12.379 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:53:12.422 [info] > git status -z -uall [21ms]
2025-05-24 12:53:12.423 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 12:53:12.751 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:53:13.001 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:53:13.085 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/context/RestaurantStatusContext.tsx [36ms]
2025-05-24 12:53:13.085 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/Checkout.tsx [4ms]
2025-05-24 12:53:13.327 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/context/RestaurantStatusContext.tsx [36ms]
2025-05-24 12:53:13.328 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/Checkout.tsx [5ms]
2025-05-24 12:53:17.474 [info] > git config --get commit.template [25ms]
2025-05-24 12:53:17.474 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:53:17.475 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:53:17.526 [info] > git status -z -uall [25ms]
2025-05-24 12:53:17.527 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 12:53:17.855 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:53:20.653 [info] > git fetch [10ms]
2025-05-24 12:53:20.685 [info] > git config --get commit.template [3ms]
2025-05-24 12:53:20.712 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:53:20.715 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:53:20.760 [info] > git status -z -uall [22ms]
2025-05-24 12:53:20.761 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 12:53:21.093 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:53:22.405 [info] > git ls-files --stage -- shared/schema.ts [2ms]
2025-05-24 12:53:22.431 [info] > git cat-file -s d32f4ffe71fcf6e84526cd8ee980575cb739b036 [1ms]
2025-05-24 12:53:22.554 [info] > git config --get commit.template [0ms]
2025-05-24 12:53:22.587 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:53:22.588 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:53:22.664 [info] > git status -z -uall [53ms]
2025-05-24 12:53:22.720 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [83ms]
2025-05-24 12:53:22.722 [info] > git show --textconv :shared/schema.ts [58ms]
2025-05-24 12:53:23.046 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:53:27.779 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:53:27.781 [info] > git config --get commit.template [28ms]
2025-05-24 12:53:27.781 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:53:27.844 [info] > git status -z -uall [34ms]
2025-05-24 12:53:27.845 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:53:28.174 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:53:32.868 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:53:32.891 [info] > git config --get commit.template [24ms]
2025-05-24 12:53:32.892 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:53:32.941 [info] > git status -z -uall [24ms]
2025-05-24 12:53:32.942 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:53:33.272 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:53:37.974 [info] > git config --get commit.template [1ms]
2025-05-24 12:53:38.002 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:53:38.004 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:53:38.053 [info] > git status -z -uall [23ms]
2025-05-24 12:53:38.054 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 12:53:38.386 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:53:43.107 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:53:43.107 [info] > git config --get commit.template [28ms]
2025-05-24 12:53:43.109 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:53:43.159 [info] > git status -z -uall [24ms]
2025-05-24 12:53:43.160 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:53:43.493 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-05-24 12:53:48.211 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:53:48.211 [info] > git config --get commit.template [23ms]
2025-05-24 12:53:48.212 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:53:48.265 [info] > git status -z -uall [30ms]
2025-05-24 12:53:48.266 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:53:48.590 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:53:53.317 [info] > git config --get commit.template [24ms]
2025-05-24 12:53:53.317 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:53:53.318 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:53:53.369 [info] > git status -z -uall [26ms]
2025-05-24 12:53:53.370 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:53:53.696 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:53:58.428 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:53:58.428 [info] > git config --get commit.template [29ms]
2025-05-24 12:53:58.429 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:53:58.479 [info] > git status -z -uall [27ms]
2025-05-24 12:53:58.480 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 12:53:58.821 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:54:03.537 [info] > git config --get commit.template [16ms]
2025-05-24 12:54:03.589 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:54:03.593 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-05-24 12:54:03.666 [info] > git status -z -uall [35ms]
2025-05-24 12:54:03.668 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:54:03.995 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:54:08.698 [info] > git config --get commit.template [3ms]
2025-05-24 12:54:08.720 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:54:08.721 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:54:08.766 [info] > git status -z -uall [22ms]
2025-05-24 12:54:08.767 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:54:09.094 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:54:43.321 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:54:43.321 [info] > git config --get commit.template [21ms]
2025-05-24 12:54:43.322 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:54:43.387 [info] > git status -z -uall [45ms]
2025-05-24 12:54:43.388 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:54:43.749 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:54:48.458 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:54:48.465 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-05-24 12:54:48.465 [info] > git config --get commit.template [47ms]
2025-05-24 12:54:48.549 [info] > git status -z -uall [53ms]
2025-05-24 12:54:48.551 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 12:54:48.903 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:55:00.707 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:55:00.708 [info] > git config --get commit.template [23ms]
2025-05-24 12:55:00.709 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:55:00.760 [info] > git status -z -uall [25ms]
2025-05-24 12:55:00.760 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 12:55:01.088 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:55:05.808 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:55:05.809 [info] > git config --get commit.template [23ms]
2025-05-24 12:55:05.809 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:55:05.863 [info] > git status -z -uall [26ms]
2025-05-24 12:55:05.869 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-05-24 12:55:06.200 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:55:10.924 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:55:10.925 [info] > git config --get commit.template [31ms]
2025-05-24 12:55:10.926 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:55:10.979 [info] > git status -z -uall [26ms]
2025-05-24 12:55:10.979 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:55:11.306 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:55:16.057 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:55:16.057 [info] > git config --get commit.template [39ms]
2025-05-24 12:55:16.059 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:55:16.133 [info] > git status -z -uall [36ms]
2025-05-24 12:55:16.134 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:55:16.469 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:55:51.078 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:55:51.079 [info] > git config --get commit.template [28ms]
2025-05-24 12:55:51.080 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:55:51.143 [info] > git status -z -uall [35ms]
2025-05-24 12:55:51.148 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-05-24 12:55:51.484 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 12:57:25.641 [info] > git fetch [27ms]
2025-05-24 12:57:25.688 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:57:25.689 [info] > git config --get commit.template [48ms]
2025-05-24 12:57:25.737 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [49ms]
2025-05-24 12:57:25.738 [info] > git config --get commit.template [71ms]
2025-05-24 12:57:25.761 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:57:25.762 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:57:25.820 [info] > git status -z -uall [32ms]
2025-05-24 12:57:25.820 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-05-24 12:57:26.147 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:57:27.388 [info] > git ls-files --stage -- shared/schema.ts [2ms]
2025-05-24 12:57:27.414 [info] > git cat-file -s d32f4ffe71fcf6e84526cd8ee980575cb739b036 [2ms]
2025-05-24 12:57:27.624 [info] > git show --textconv :shared/schema.ts [1ms]
2025-05-24 12:57:30.772 [info] > git config --get commit.template [4ms]
2025-05-24 12:57:30.801 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:57:30.803 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:57:30.856 [info] > git status -z -uall [26ms]
2025-05-24 12:57:30.857 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:57:31.191 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-05-24 12:57:36.066 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:57:36.067 [info] > git config --get commit.template [39ms]
2025-05-24 12:57:36.068 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:57:36.145 [info] > git status -z -uall [44ms]
2025-05-24 12:57:36.146 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:57:36.479 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
