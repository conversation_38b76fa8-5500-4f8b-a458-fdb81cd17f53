2025-05-24 11:44:18.720 [info] [main] Log level: Info
2025-05-24 11:44:18.720 [info] [main] Validating found git in: "git"
2025-05-24 11:44:18.720 [info] [main] Using git "2.47.2" from "git"
2025-05-24 11:44:18.720 [info] [Model][doInitialScan] Initial repository scan started
2025-05-24 11:44:18.721 [info] > git rev-parse --show-toplevel [3ms]
2025-05-24 11:44:18.721 [info] > git rev-parse --git-dir --git-common-dir [277ms]
2025-05-24 11:44:18.721 [info] [Model][openRepository] Opened repository (path): /home/<USER>/workspace
2025-05-24 11:44:18.721 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/workspace
2025-05-24 11:44:18.721 [info] > git config --get commit.template [10ms]
2025-05-24 11:44:18.721 [info] > git fetch [26ms]
2025-05-24 11:44:18.721 [info] > git rev-parse --show-toplevel [14ms]
2025-05-24 11:44:18.721 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 11:44:18.722 [info] > git config --get commit.template [15ms]
2025-05-24 11:44:18.722 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 11:44:18.722 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [756ms]
2025-05-24 11:44:18.722 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [58ms]
2025-05-24 11:44:18.722 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [830ms]
2025-05-24 11:44:18.722 [info] > git rev-parse --show-toplevel [845ms]
2025-05-24 11:44:18.722 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [15ms]
2025-05-24 11:44:18.722 [info] > git config --get --local branch.main.vscode-merge-base [33ms]
2025-05-24 11:44:18.722 [warning] [Git][config] git config failed: Failed to execute git
2025-05-24 11:44:18.722 [info] > git config --get commit.template [57ms]
2025-05-24 11:44:18.722 [info] > git config --get --local branch.main.vscode-merge-base [10ms]
2025-05-24 11:44:18.722 [warning] [Git][config] git config failed: Failed to execute git
2025-05-24 11:44:18.722 [info] > git rev-parse --show-toplevel [35ms]
2025-05-24 11:44:18.723 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 11:44:18.723 [info] > git reflog main --grep-reflog=branch: Created from *. [24ms]
2025-05-24 11:44:18.723 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [20ms]
2025-05-24 11:44:18.723 [info] > git reflog main --grep-reflog=branch: Created from *. [44ms]
2025-05-24 11:44:18.723 [info] > git status -z -uall [10ms]
2025-05-24 11:44:18.723 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 11:44:18.723 [info] > git rev-parse --show-toplevel [41ms]
2025-05-24 11:44:18.723 [info] > git rev-parse --show-toplevel [4ms]
2025-05-24 11:44:18.723 [info] > git rev-parse --show-toplevel [1ms]
2025-05-24 11:44:18.723 [info] > git rev-parse --show-toplevel [368ms]
2025-05-24 11:44:18.723 [info] > git rev-parse --show-toplevel [17ms]
2025-05-24 11:44:18.723 [info] > git rev-parse --show-toplevel [65ms]
2025-05-24 11:44:18.732 [info] > git rev-parse --show-toplevel [20ms]
2025-05-24 11:44:18.737 [info] [Model][doInitialScan] Initial repository scan completed - repositories (1), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-05-24 11:44:18.922 [info] > git show --textconv :shared/schema.ts [124ms]
2025-05-24 11:44:18.922 [info] > git check-ignore -v -z --stdin [6ms]
2025-05-24 11:44:18.923 [info] > git config --get commit.template [22ms]
2025-05-24 11:44:18.924 [info] > git ls-files --stage -- shared/schema.ts [113ms]
2025-05-24 11:44:18.970 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 11:44:18.972 [info] > git cat-file -s d32f4ffe71fcf6e84526cd8ee980575cb739b036 [38ms]
2025-05-24 11:44:18.991 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [21ms]
2025-05-24 11:44:19.030 [info] > git status -z -uall [20ms]
2025-05-24 11:44:19.031 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 11:44:19.787 [info] > git blame --root --incremental 950dc2c924e9cc2598727254b321b92999218d81 -- shared/schema.ts [9ms]
2025-05-24 11:48:57.031 [info] > git fetch [12ms]
2025-05-24 11:48:57.052 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 11:48:57.054 [info] > git config --get commit.template [22ms]
2025-05-24 11:48:57.054 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 11:48:57.056 [info] > git config --get commit.template [13ms]
2025-05-24 11:48:57.066 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 11:48:57.068 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 11:48:57.089 [info] > git status -z -uall [11ms]
2025-05-24 11:48:57.090 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 11:48:57.108 [info] > git blame --root --incremental 63dc5bf3c394b7b74ba063fd015aa649de876bb8 -- shared/schema.ts [5ms]
2025-05-24 11:48:57.208 [info] > git ls-files --stage -- shared/schema.ts [1ms]
2025-05-24 11:48:57.218 [info] > git cat-file -s d32f4ffe71fcf6e84526cd8ee980575cb739b036 [2ms]
2025-05-24 11:48:57.422 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [17ms]
2025-05-24 11:48:57.424 [info] > git show --textconv :shared/schema.ts [2ms]
2025-05-24 11:48:57.424 [info] > git show -s --decorate=full --shortstat --format=%H%n%aN%n%aE%n%at%n%ct%n%P%n%D%n%B -z 63dc5bf3c394b7b74ba063fd015aa649de876bb8 -- [10ms]
2025-05-24 11:48:57.441 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [9ms]
2025-05-24 11:48:57.452 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [13ms]
2025-05-24 11:48:57.474 [info] > git config --get --local branch.main.vscode-merge-base [24ms]
2025-05-24 11:48:57.474 [warning] [Git][config] git config failed: Failed to execute git
2025-05-24 11:48:57.483 [info] > git show --textconv 63dc5bf3c394b7b74ba063fd015aa649de876bb8:package.json [10ms]
2025-05-24 11:48:57.488 [info] > git reflog main --grep-reflog=branch: Created from *. [6ms]
2025-05-24 11:48:57.502 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 11:48:57.511 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 11:48:57.519 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 11:48:57.682 [info] > git diff 63dc5bf3c394b7b74ba063fd015aa649de876bb8 -- package.json [2ms]
2025-05-24 11:48:57.728 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 11:48:57.740 [info] > git show -s --decorate=full --shortstat --format=%H%n%aN%n%aE%n%at%n%ct%n%P%n%D%n%B -z 63dc5bf3c394b7b74ba063fd015aa649de876bb8 -- [3ms]
2025-05-24 11:48:57.751 [info] > git show -s --decorate=full --shortstat --format=%H%n%aN%n%aE%n%at%n%ct%n%P%n%D%n%B -z 63dc5bf3c394b7b74ba063fd015aa649de876bb8 -- [3ms]
2025-05-24 11:48:59.303 [info] > git ls-files --stage -- shared/schema.ts [1ms]
2025-05-24 11:48:59.315 [info] > git cat-file -s d32f4ffe71fcf6e84526cd8ee980575cb739b036 [2ms]
2025-05-24 11:48:59.528 [info] > git show --textconv :shared/schema.ts [4ms]
2025-05-24 11:49:02.075 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 11:49:02.076 [info] > git config --get commit.template [8ms]
2025-05-24 11:49:02.076 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 11:49:02.093 [info] > git status -z -uall [10ms]
2025-05-24 11:49:02.094 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 11:49:02.415 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 11:56:46.035 [info] > git fetch [22ms]
2025-05-24 11:56:46.050 [info] > git config --get commit.template [16ms]
2025-05-24 11:56:46.062 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 11:56:46.063 [info] > git config --get commit.template [14ms]
2025-05-24 11:56:46.076 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [14ms]
2025-05-24 11:56:46.078 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 11:56:46.078 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 11:56:46.103 [info] > git status -z -uall [12ms]
2025-05-24 11:56:46.104 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 11:56:46.127 [info] > git blame --root --incremental ac10dde1d079a50eed8574fc6fbc8a5697095273 -- shared/schema.ts [6ms]
2025-05-24 11:56:46.229 [info] > git ls-files --stage -- shared/schema.ts [1ms]
2025-05-24 11:56:46.244 [info] > git cat-file -s d32f4ffe71fcf6e84526cd8ee980575cb739b036 [2ms]
2025-05-24 11:56:46.446 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [25ms]
2025-05-24 11:56:46.447 [info] > git show -s --decorate=full --shortstat --format=%H%n%aN%n%aE%n%at%n%ct%n%P%n%D%n%B -z ac10dde1d079a50eed8574fc6fbc8a5697095273 -- [12ms]
2025-05-24 11:56:46.476 [info] > git show --textconv :shared/schema.ts [30ms]
2025-05-24 11:56:46.476 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [15ms]
2025-05-24 11:56:46.487 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [14ms]
2025-05-24 11:56:46.491 [info] > git config --get --local branch.main.vscode-merge-base [4ms]
2025-05-24 11:56:46.491 [warning] [Git][config] git config failed: Failed to execute git
2025-05-24 11:56:46.506 [info] > git reflog main --grep-reflog=branch: Created from *. [5ms]
2025-05-24 11:56:46.521 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 11:56:46.534 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 11:56:46.549 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 11:56:46.638 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 11:56:46.778 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 11:56:46.802 [info] > git show -s --decorate=full --shortstat --format=%H%n%aN%n%aE%n%at%n%ct%n%P%n%D%n%B -z ac10dde1d079a50eed8574fc6fbc8a5697095273 -- [3ms]
2025-05-24 11:56:46.826 [info] > git show -s --decorate=full --shortstat --format=%H%n%aN%n%aE%n%at%n%ct%n%P%n%D%n%B -z ac10dde1d079a50eed8574fc6fbc8a5697095273 -- [4ms]
2025-05-24 11:56:47.612 [info] > git ls-files --stage -- shared/schema.ts [2ms]
2025-05-24 11:56:47.631 [info] > git cat-file -s d32f4ffe71fcf6e84526cd8ee980575cb739b036 [1ms]
2025-05-24 11:56:47.843 [info] > git show --textconv :shared/schema.ts [2ms]
2025-05-24 11:56:51.119 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 11:56:51.119 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 11:56:51.119 [info] > git config --get commit.template [18ms]
2025-05-24 11:56:51.151 [info] > git status -z -uall [15ms]
2025-05-24 11:56:51.151 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 11:56:51.471 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 11:56:56.202 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 11:56:56.203 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 11:56:56.203 [info] > git config --get commit.template [23ms]
2025-05-24 11:56:56.253 [info] > git status -z -uall [27ms]
2025-05-24 11:56:56.253 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 11:56:56.575 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 11:57:01.293 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 11:57:01.296 [info] > git config --get commit.template [22ms]
2025-05-24 11:57:01.296 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 11:57:01.334 [info] > git status -z -uall [21ms]
2025-05-24 11:57:01.335 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 11:57:01.655 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 11:57:06.377 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 11:57:06.378 [info] > git config --get commit.template [16ms]
2025-05-24 11:57:06.379 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 11:57:06.412 [info] > git status -z -uall [21ms]
2025-05-24 11:57:06.413 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 11:57:06.778 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 11:57:11.441 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 11:57:11.442 [info] > git config --get commit.template [12ms]
2025-05-24 11:57:11.443 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 11:57:11.472 [info] > git status -z -uall [16ms]
2025-05-24 11:57:11.473 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 11:57:11.792 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:14:36.497 [info] > git fetch [30ms]
2025-05-24 12:14:36.525 [info] > git config --get commit.template [29ms]
2025-05-24 12:14:36.555 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:14:36.556 [info] > git config --get commit.template [33ms]
2025-05-24 12:14:36.557 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:14:36.581 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:14:36.582 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:14:36.633 [info] > git status -z -uall [23ms]
2025-05-24 12:14:36.634 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:14:36.686 [info] > git blame --root --incremental 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- shared/schema.ts [23ms]
2025-05-24 12:14:36.686 [info] > git ls-files --stage -- shared/schema.ts [1ms]
2025-05-24 12:14:36.711 [info] > git cat-file -s d32f4ffe71fcf6e84526cd8ee980575cb739b036 [2ms]
2025-05-24 12:14:36.915 [info] > git show --textconv :shared/schema.ts [2ms]
2025-05-24 12:14:36.981 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [21ms]
2025-05-24 12:14:37.000 [info] > git show -s --decorate=full --shortstat --format=%H%n%aN%n%aE%n%at%n%ct%n%P%n%D%n%B -z 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- [20ms]
2025-05-24 12:14:37.052 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [31ms]
2025-05-24 12:14:37.075 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [75ms]
2025-05-24 12:14:37.078 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [31ms]
2025-05-24 12:14:37.078 [info] > git config --get --local branch.main.vscode-merge-base [3ms]
2025-05-24 12:14:37.078 [warning] [Git][config] git config failed: Failed to execute git
2025-05-24 12:14:37.104 [info] > git reflog main --grep-reflog=branch: Created from *. [5ms]
2025-05-24 12:14:37.129 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 12:14:37.154 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:14:37.179 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:14:37.419 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:14:37.445 [info] > git show -s --decorate=full --shortstat --format=%H%n%aN%n%aE%n%at%n%ct%n%P%n%D%n%B -z 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- [2ms]
2025-05-24 12:14:37.474 [info] > git show -s --decorate=full --shortstat --format=%H%n%aN%n%aE%n%at%n%ct%n%P%n%D%n%B -z 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- [3ms]
2025-05-24 12:14:38.050 [info] > git ls-files --stage -- shared/schema.ts [2ms]
2025-05-24 12:14:38.074 [info] > git cat-file -s d32f4ffe71fcf6e84526cd8ee980575cb739b036 [1ms]
2025-05-24 12:14:38.288 [info] > git show --textconv :shared/schema.ts [2ms]
2025-05-24 12:14:41.611 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:14:41.611 [info] > git config --get commit.template [25ms]
2025-05-24 12:14:41.612 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:14:41.661 [info] > git status -z -uall [23ms]
2025-05-24 12:14:41.663 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:14:41.989 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:14:47.071 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:14:47.072 [info] > git config --get commit.template [26ms]
2025-05-24 12:14:47.073 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:14:47.124 [info] > git status -z -uall [26ms]
2025-05-24 12:14:47.125 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:14:47.454 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:14:52.156 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:14:52.256 [info] > git config --get commit.template [100ms]
2025-05-24 12:14:52.257 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [68ms]
2025-05-24 12:14:52.364 [info] > git status -z -uall [38ms]
2025-05-24 12:14:52.365 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 12:14:52.696 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:14:57.410 [info] > git config --get commit.template [5ms]
2025-05-24 12:14:57.440 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:14:57.442 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:14:57.491 [info] > git status -z -uall [26ms]
2025-05-24 12:14:57.492 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:14:57.866 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [49ms]
2025-05-24 12:15:02.518 [info] > git config --get commit.template [1ms]
2025-05-24 12:15:02.541 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:15:02.543 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:15:02.585 [info] > git status -z -uall [22ms]
2025-05-24 12:15:02.586 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:15:02.910 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:15:07.676 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:15:07.693 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [22ms]
2025-05-24 12:15:07.694 [info] > git config --get commit.template [69ms]
2025-05-24 12:15:07.771 [info] > git status -z -uall [31ms]
2025-05-24 12:15:07.774 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 12:15:08.114 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-05-24 12:15:12.851 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:15:12.924 [info] > git config --get commit.template [118ms]
2025-05-24 12:15:12.925 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [74ms]
2025-05-24 12:15:13.050 [info] > git status -z -uall [63ms]
2025-05-24 12:15:13.061 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [12ms]
2025-05-24 12:15:13.422 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 12:15:18.129 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:15:18.130 [info] > git config --get commit.template [38ms]
2025-05-24 12:15:18.131 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:15:18.188 [info] > git status -z -uall [28ms]
2025-05-24 12:15:18.190 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 12:15:18.529 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-05-24 12:15:23.242 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:15:23.243 [info] > git config --get commit.template [25ms]
2025-05-24 12:15:23.244 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:15:23.288 [info] > git status -z -uall [22ms]
2025-05-24 12:15:23.288 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:15:23.617 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:15:28.347 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:15:28.348 [info] > git config --get commit.template [30ms]
2025-05-24 12:15:28.349 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:15:28.396 [info] > git status -z -uall [22ms]
2025-05-24 12:15:28.397 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:15:28.728 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:15:33.456 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:15:33.456 [info] > git config --get commit.template [29ms]
2025-05-24 12:15:33.457 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:15:33.510 [info] > git status -z -uall [26ms]
2025-05-24 12:15:33.511 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:15:33.939 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [88ms]
2025-05-24 12:15:38.620 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:15:38.623 [info] > git config --get commit.template [54ms]
2025-05-24 12:15:38.627 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-05-24 12:15:38.742 [info] > git status -z -uall [79ms]
2025-05-24 12:15:38.742 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 12:15:39.115 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 12:15:43.790 [info] > git config --get commit.template [2ms]
2025-05-24 12:15:43.842 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:15:43.845 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:15:43.929 [info] > git status -z -uall [45ms]
2025-05-24 12:15:43.930 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 12:15:44.274 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-05-24 12:15:48.991 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:15:48.992 [info] > git config --get commit.template [31ms]
2025-05-24 12:15:48.993 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:15:49.057 [info] > git status -z -uall [37ms]
2025-05-24 12:15:49.058 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 12:15:49.388 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:15:54.106 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:15:54.106 [info] > git config --get commit.template [24ms]
2025-05-24 12:15:54.107 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:15:54.150 [info] > git status -z -uall [21ms]
2025-05-24 12:15:54.151 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:15:54.476 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:15:59.208 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:15:59.208 [info] > git config --get commit.template [27ms]
2025-05-24 12:15:59.209 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:15:59.262 [info] > git status -z -uall [25ms]
2025-05-24 12:15:59.263 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 12:15:59.597 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:16:04.313 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:16:04.314 [info] > git config --get commit.template [27ms]
2025-05-24 12:16:04.315 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:16:04.373 [info] > git status -z -uall [33ms]
2025-05-24 12:16:04.373 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-05-24 12:16:04.701 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:16:09.451 [info] > git config --get commit.template [37ms]
2025-05-24 12:16:09.452 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:16:09.453 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:16:09.617 [info] > git status -z -uall [113ms]
2025-05-24 12:16:09.617 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [81ms]
2025-05-24 12:16:09.962 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:16:14.686 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:16:14.686 [info] > git config --get commit.template [27ms]
2025-05-24 12:16:14.687 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:16:14.749 [info] > git status -z -uall [29ms]
2025-05-24 12:16:14.749 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 12:16:15.091 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:16:19.790 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:16:19.829 [info] > git config --get commit.template [40ms]
2025-05-24 12:16:19.830 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:16:19.886 [info] > git status -z -uall [29ms]
2025-05-24 12:16:19.887 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:16:20.215 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:16:24.966 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:16:24.967 [info] > git config --get commit.template [34ms]
2025-05-24 12:16:24.968 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:16:25.027 [info] > git status -z -uall [28ms]
2025-05-24 12:16:25.028 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:16:25.353 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:16:30.121 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:16:30.121 [info] > git config --get commit.template [38ms]
2025-05-24 12:16:30.123 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:16:30.179 [info] > git status -z -uall [30ms]
2025-05-24 12:16:30.182 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 12:16:30.522 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 12:16:35.265 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:16:35.266 [info] > git config --get commit.template [46ms]
2025-05-24 12:16:35.267 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:16:35.332 [info] > git status -z -uall [36ms]
2025-05-24 12:16:35.335 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 12:16:35.665 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:16:40.384 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:16:40.384 [info] > git config --get commit.template [23ms]
2025-05-24 12:16:40.385 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:16:40.431 [info] > git status -z -uall [22ms]
2025-05-24 12:16:40.432 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:16:40.759 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:16:45.467 [info] > git config --get commit.template [1ms]
2025-05-24 12:16:45.491 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:16:45.492 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:16:45.535 [info] > git status -z -uall [21ms]
2025-05-24 12:16:45.536 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:16:45.905 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:17:24.371 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:17:24.372 [info] > git config --get commit.template [26ms]
2025-05-24 12:17:24.373 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:17:24.421 [info] > git status -z -uall [24ms]
2025-05-24 12:17:24.422 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:17:24.749 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:17:29.545 [info] > git config --get commit.template [98ms]
2025-05-24 12:17:29.568 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:17:29.570 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:17:29.629 [info] > git status -z -uall [32ms]
2025-05-24 12:17:29.629 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-05-24 12:17:29.981 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:17:34.686 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:17:34.687 [info] > git config --get commit.template [24ms]
2025-05-24 12:17:34.688 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:17:34.730 [info] > git status -z -uall [21ms]
2025-05-24 12:17:34.731 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:17:35.067 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-05-24 12:17:36.695 [info] > git fetch [4ms]
2025-05-24 12:17:36.745 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:17:36.746 [info] > git config --get commit.template [23ms]
2025-05-24 12:17:36.747 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:17:36.801 [info] > git status -z -uall [28ms]
2025-05-24 12:17:36.802 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 12:17:37.137 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:17:38.304 [info] > git ls-files --stage -- shared/schema.ts [2ms]
2025-05-24 12:17:38.344 [info] > git cat-file -s d32f4ffe71fcf6e84526cd8ee980575cb739b036 [2ms]
2025-05-24 12:17:38.544 [info] > git show --textconv :shared/schema.ts [1ms]
2025-05-24 12:17:39.784 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:17:39.784 [info] > git config --get commit.template [25ms]
2025-05-24 12:17:39.785 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:17:39.885 [info] > git status -z -uall [78ms]
2025-05-24 12:17:39.885 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [54ms]
2025-05-24 12:17:40.212 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:17:44.924 [info] > git config --get commit.template [2ms]
2025-05-24 12:17:44.946 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:17:44.948 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:17:44.995 [info] > git status -z -uall [24ms]
2025-05-24 12:17:44.996 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:17:45.329 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:17:50.055 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:17:50.056 [info] > git config --get commit.template [29ms]
2025-05-24 12:17:50.057 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:17:50.129 [info] > git status -z -uall [35ms]
2025-05-24 12:17:50.129 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 12:17:50.462 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:17:55.199 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:17:55.199 [info] > git config --get commit.template [33ms]
2025-05-24 12:17:55.200 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:17:55.295 [info] > git status -z -uall [43ms]
2025-05-24 12:17:55.298 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 12:17:55.637 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:18:00.354 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:18:00.355 [info] > git config --get commit.template [25ms]
2025-05-24 12:18:00.356 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:18:00.407 [info] > git status -z -uall [24ms]
2025-05-24 12:18:00.409 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:18:00.735 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:18:05.479 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:18:05.480 [info] > git config --get commit.template [35ms]
2025-05-24 12:18:05.485 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-05-24 12:18:05.542 [info] > git status -z -uall [28ms]
2025-05-24 12:18:05.543 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:18:05.872 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:18:10.570 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:18:10.596 [info] > git config --get commit.template [27ms]
2025-05-24 12:18:10.598 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:18:10.652 [info] > git status -z -uall [27ms]
2025-05-24 12:18:10.654 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 12:18:10.978 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:18:15.682 [info] > git config --get commit.template [2ms]
2025-05-24 12:18:15.718 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:18:15.719 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:18:15.770 [info] > git status -z -uall [23ms]
2025-05-24 12:18:15.771 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 12:18:16.098 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:18:20.824 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:18:20.824 [info] > git config --get commit.template [27ms]
2025-05-24 12:18:20.825 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:18:20.885 [info] > git status -z -uall [24ms]
2025-05-24 12:18:20.885 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 12:18:21.218 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:18:25.937 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:18:25.938 [info] > git config --get commit.template [26ms]
2025-05-24 12:18:25.938 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:18:25.994 [info] > git status -z -uall [31ms]
2025-05-24 12:18:25.995 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:18:26.327 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:18:26.544 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-05-24 12:18:26.578 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/Checkout.tsx [2ms]
2025-05-24 12:18:26.781 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/Checkout.tsx [4ms]
2025-05-24 12:18:31.027 [info] > git config --get commit.template [3ms]
2025-05-24 12:18:31.051 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:18:31.052 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:18:31.096 [info] > git status -z -uall [22ms]
2025-05-24 12:18:31.097 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:18:31.424 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:18:36.155 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:18:36.156 [info] > git config --get commit.template [29ms]
2025-05-24 12:18:36.157 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:18:36.223 [info] > git status -z -uall [32ms]
2025-05-24 12:18:36.224 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:18:36.563 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:18:41.268 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:18:41.268 [info] > git config --get commit.template [20ms]
2025-05-24 12:18:41.269 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:18:41.319 [info] > git status -z -uall [32ms]
2025-05-24 12:18:41.320 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:18:41.647 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:18:46.405 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:18:46.407 [info] > git config --get commit.template [37ms]
2025-05-24 12:18:46.407 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:18:46.465 [info] > git status -z -uall [29ms]
2025-05-24 12:18:46.468 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 12:18:46.797 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:18:51.532 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:18:51.533 [info] > git config --get commit.template [31ms]
2025-05-24 12:18:51.534 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:18:51.623 [info] > git status -z -uall [51ms]
2025-05-24 12:18:51.625 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 12:18:51.973 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-05-24 12:18:56.699 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:18:56.700 [info] > git config --get commit.template [27ms]
2025-05-24 12:18:56.701 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:18:56.766 [info] > git status -z -uall [27ms]
2025-05-24 12:18:56.767 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:18:57.094 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:19:01.821 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:19:01.823 [info] > git config --get commit.template [25ms]
2025-05-24 12:19:01.823 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:19:01.872 [info] > git status -z -uall [27ms]
2025-05-24 12:19:01.873 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:19:02.201 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:19:06.929 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:19:06.931 [info] > git config --get commit.template [25ms]
2025-05-24 12:19:06.931 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:19:06.989 [info] > git status -z -uall [33ms]
2025-05-24 12:19:06.990 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-05-24 12:19:07.315 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:19:12.038 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:19:12.039 [info] > git config --get commit.template [20ms]
2025-05-24 12:19:12.039 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:19:12.089 [info] > git status -z -uall [26ms]
2025-05-24 12:19:12.095 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-05-24 12:19:12.422 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:19:17.161 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:19:17.163 [info] > git config --get commit.template [39ms]
2025-05-24 12:19:17.163 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:19:17.207 [info] > git status -z -uall [22ms]
2025-05-24 12:19:17.208 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 12:19:17.535 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:19:22.259 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:19:22.260 [info] > git config --get commit.template [26ms]
2025-05-24 12:19:22.261 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:19:22.365 [info] > git status -z -uall [73ms]
2025-05-24 12:19:22.366 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [51ms]
2025-05-24 12:19:22.699 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:19:27.415 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:19:27.416 [info] > git config --get commit.template [24ms]
2025-05-24 12:19:27.417 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:19:27.468 [info] > git status -z -uall [27ms]
2025-05-24 12:19:27.468 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:19:27.798 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:19:32.494 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:19:32.519 [info] > git config --get commit.template [25ms]
2025-05-24 12:19:32.520 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:19:32.567 [info] > git status -z -uall [26ms]
2025-05-24 12:19:32.568 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:19:32.936 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [13ms]
2025-05-24 12:19:37.637 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:19:37.638 [info] > git config --get commit.template [38ms]
2025-05-24 12:19:37.639 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:19:37.684 [info] > git status -z -uall [20ms]
2025-05-24 12:19:37.686 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:19:38.016 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:19:42.760 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:19:42.761 [info] > git config --get commit.template [35ms]
2025-05-24 12:19:42.761 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:19:42.835 [info] > git status -z -uall [34ms]
2025-05-24 12:19:42.837 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:19:43.195 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [14ms]
2025-05-24 12:19:53.430 [info] > git config --get commit.template [5ms]
2025-05-24 12:19:53.467 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:19:53.468 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:19:53.540 [info] > git status -z -uall [37ms]
2025-05-24 12:19:53.543 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 12:19:53.881 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:20:02.463 [info] > git config --get commit.template [37ms]
2025-05-24 12:20:02.463 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:20:02.466 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 12:20:02.554 [info] > git status -z -uall [42ms]
2025-05-24 12:20:02.558 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-05-24 12:20:02.901 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:20:11.411 [info] > git config --get commit.template [1ms]
2025-05-24 12:20:11.435 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:20:11.437 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:20:11.495 [info] > git status -z -uall [36ms]
2025-05-24 12:20:11.495 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 12:20:11.823 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:20:16.524 [info] > git config --get commit.template [2ms]
2025-05-24 12:20:16.553 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:20:16.554 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:20:16.602 [info] > git status -z -uall [24ms]
2025-05-24 12:20:16.603 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 12:20:16.930 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:20:21.656 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:20:21.657 [info] > git config --get commit.template [28ms]
2025-05-24 12:20:21.659 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:20:21.711 [info] > git status -z -uall [26ms]
2025-05-24 12:20:21.712 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:20:22.042 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:25:05.026 [info] > git fetch [36ms]
2025-05-24 12:25:05.027 [info] > git config --get commit.template [2ms]
2025-05-24 12:25:05.084 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:25:05.090 [info] > git config --get commit.template [35ms]
2025-05-24 12:25:05.129 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:25:05.130 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [47ms]
2025-05-24 12:25:05.133 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 12:25:05.223 [info] > git status -z -uall [49ms]
2025-05-24 12:25:05.223 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [10ms]
2025-05-24 12:25:05.550 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:25:06.554 [info] > git ls-files --stage -- shared/schema.ts [1ms]
2025-05-24 12:25:06.579 [info] > git cat-file -s d32f4ffe71fcf6e84526cd8ee980575cb739b036 [2ms]
2025-05-24 12:25:06.803 [info] > git show --textconv :shared/schema.ts [2ms]
2025-05-24 12:25:19.116 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:25:19.116 [info] > git config --get commit.template [22ms]
2025-05-24 12:25:19.117 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:25:19.161 [info] > git status -z -uall [22ms]
2025-05-24 12:25:19.162 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:25:19.487 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:25:28.059 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:25:28.060 [info] > git config --get commit.template [22ms]
2025-05-24 12:25:28.061 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:25:28.104 [info] > git status -z -uall [23ms]
2025-05-24 12:25:28.107 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 12:25:28.439 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:25:34.086 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:25:34.086 [info] > git config --get commit.template [16ms]
2025-05-24 12:25:34.087 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:25:34.127 [info] > git status -z -uall [20ms]
2025-05-24 12:25:34.128 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:25:34.454 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:25:39.174 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:25:39.175 [info] > git config --get commit.template [22ms]
2025-05-24 12:25:39.176 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:25:39.228 [info] > git status -z -uall [26ms]
2025-05-24 12:25:39.230 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:25:39.558 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:25:48.561 [info] > git config --get commit.template [2ms]
2025-05-24 12:25:48.579 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:25:48.581 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:25:48.630 [info] > git status -z -uall [26ms]
2025-05-24 12:25:48.632 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 12:25:48.957 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:25:53.674 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:25:53.675 [info] > git config --get commit.template [19ms]
2025-05-24 12:25:53.675 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:25:53.716 [info] > git status -z -uall [20ms]
2025-05-24 12:25:53.717 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 12:25:54.045 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 12:25:58.779 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:25:58.780 [info] > git config --get commit.template [31ms]
2025-05-24 12:25:58.780 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:25:58.846 [info] > git status -z -uall [34ms]
2025-05-24 12:25:58.848 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 12:25:59.176 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:26:03.902 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:26:03.903 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:26:03.903 [info] > git config --get commit.template [30ms]
2025-05-24 12:26:03.964 [info] > git status -z -uall [33ms]
2025-05-24 12:26:03.966 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:26:04.291 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:26:32.905 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:26:32.906 [info] > git config --get commit.template [29ms]
2025-05-24 12:26:32.907 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:26:32.965 [info] > git status -z -uall [32ms]
2025-05-24 12:26:32.965 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 12:26:33.296 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 12:26:38.007 [info] > git config --get commit.template [0ms]
2025-05-24 12:26:38.041 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:26:38.042 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:26:38.138 [info] > git status -z -uall [56ms]
2025-05-24 12:26:38.140 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 12:26:38.474 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:26:43.204 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:26:43.205 [info] > git config --get commit.template [34ms]
2025-05-24 12:26:43.206 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:26:43.274 [info] > git status -z -uall [33ms]
2025-05-24 12:26:43.275 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 12:26:43.606 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 12:26:48.336 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:26:48.337 [info] > git config --get commit.template [32ms]
2025-05-24 12:26:48.338 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:26:48.382 [info] > git status -z -uall [23ms]
2025-05-24 12:26:48.383 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:26:48.724 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:26:56.048 [info] > git config --get commit.template [2ms]
2025-05-24 12:26:56.090 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:26:56.092 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:26:56.161 [info] > git status -z -uall [35ms]
2025-05-24 12:26:56.162 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:26:56.493 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:27:01.224 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:27:01.224 [info] > git config --get commit.template [37ms]
2025-05-24 12:27:01.225 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:27:01.278 [info] > git status -z -uall [27ms]
2025-05-24 12:27:01.279 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:27:01.607 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:27:06.336 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:27:06.337 [info] > git config --get commit.template [33ms]
2025-05-24 12:27:06.338 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:27:06.396 [info] > git status -z -uall [29ms]
2025-05-24 12:27:06.397 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:27:06.728 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:27:11.427 [info] > git config --get commit.template [0ms]
2025-05-24 12:27:11.453 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:27:11.455 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:27:11.502 [info] > git status -z -uall [24ms]
2025-05-24 12:27:11.504 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:27:11.836 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 12:27:18.967 [info] > git config --get commit.template [1ms]
2025-05-24 12:27:18.997 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:27:18.999 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:27:19.055 [info] > git status -z -uall [24ms]
2025-05-24 12:27:19.056 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:27:19.431 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [48ms]
2025-05-24 12:27:24.113 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:27:24.115 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:27:24.115 [info] > git config --get commit.template [32ms]
2025-05-24 12:27:24.169 [info] > git status -z -uall [27ms]
2025-05-24 12:27:24.170 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:27:24.501 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:27:29.230 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:27:29.232 [info] > git config --get commit.template [30ms]
2025-05-24 12:27:29.232 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:27:29.320 [info] > git status -z -uall [45ms]
2025-05-24 12:27:29.322 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 12:27:29.787 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [93ms]
2025-05-24 12:29:20.197 [info] > git fetch [80ms]
2025-05-24 12:29:20.225 [info] > git config --get commit.template [78ms]
2025-05-24 12:29:20.254 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:29:20.256 [info] > git config --get commit.template [34ms]
2025-05-24 12:29:20.257 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:29:20.285 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:29:20.290 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-05-24 12:29:20.345 [info] > git status -z -uall [30ms]
2025-05-24 12:29:20.346 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:29:20.673 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:29:22.145 [info] > git ls-files --stage -- shared/schema.ts [62ms]
2025-05-24 12:29:22.200 [info] > git cat-file -s d32f4ffe71fcf6e84526cd8ee980575cb739b036 [3ms]
2025-05-24 12:29:23.058 [info] > git show --textconv :shared/schema.ts [67ms]
2025-05-24 12:29:25.305 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:29:25.305 [info] > git config --get commit.template [22ms]
2025-05-24 12:29:25.306 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:29:25.356 [info] > git status -z -uall [26ms]
2025-05-24 12:29:25.356 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 12:29:25.683 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:29:30.414 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:29:30.414 [info] > git config --get commit.template [29ms]
2025-05-24 12:29:30.415 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:29:30.465 [info] > git status -z -uall [25ms]
2025-05-24 12:29:30.466 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:29:30.796 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:32:50.004 [info] > git fetch [26ms]
2025-05-24 12:32:50.032 [info] > git config --get commit.template [28ms]
2025-05-24 12:32:50.055 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:32:50.056 [info] > git config --get commit.template [23ms]
2025-05-24 12:32:50.056 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:32:50.080 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [25ms]
2025-05-24 12:32:50.081 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:32:50.122 [info] > git status -z -uall [20ms]
2025-05-24 12:32:50.123 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:32:50.448 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:32:50.676 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:32:50.706 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/Checkout.tsx [2ms]
2025-05-24 12:32:50.904 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/Checkout.tsx [3ms]
2025-05-24 12:32:51.552 [info] > git ls-files --stage -- shared/schema.ts [2ms]
2025-05-24 12:32:51.579 [info] > git cat-file -s d32f4ffe71fcf6e84526cd8ee980575cb739b036 [1ms]
2025-05-24 12:32:51.785 [info] > git show --textconv :shared/schema.ts [1ms]
2025-05-24 12:32:55.126 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:32:55.127 [info] > git config --get commit.template [22ms]
2025-05-24 12:32:55.128 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:32:55.170 [info] > git status -z -uall [21ms]
2025-05-24 12:32:55.172 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:32:55.548 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 12:33:00.226 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:33:00.226 [info] > git config --get commit.template [26ms]
2025-05-24 12:33:00.228 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:33:00.282 [info] > git status -z -uall [25ms]
2025-05-24 12:33:00.284 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:33:00.610 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:33:05.339 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:33:05.339 [info] > git config --get commit.template [23ms]
2025-05-24 12:33:05.340 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:33:05.394 [info] > git status -z -uall [28ms]
2025-05-24 12:33:05.394 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 12:33:05.780 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:33:10.451 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:33:10.452 [info] > git config --get commit.template [27ms]
2025-05-24 12:33:10.458 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-05-24 12:33:10.568 [info] > git status -z -uall [76ms]
2025-05-24 12:33:10.568 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [50ms]
2025-05-24 12:33:10.895 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:33:15.597 [info] > git config --get commit.template [2ms]
2025-05-24 12:33:15.632 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:33:15.636 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 12:33:15.696 [info] > git status -z -uall [32ms]
2025-05-24 12:33:15.698 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 12:33:16.029 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:33:20.759 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:33:20.759 [info] > git config --get commit.template [36ms]
2025-05-24 12:33:20.761 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:33:20.831 [info] > git status -z -uall [37ms]
2025-05-24 12:33:20.835 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 12:33:21.175 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:33:25.886 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:33:25.886 [info] > git config --get commit.template [24ms]
2025-05-24 12:33:25.887 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:33:25.935 [info] > git status -z -uall [24ms]
2025-05-24 12:33:25.936 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 12:33:26.263 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:33:31.063 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:33:31.063 [info] > git config --get commit.template [24ms]
2025-05-24 12:33:31.064 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:33:31.116 [info] > git status -z -uall [29ms]
2025-05-24 12:33:31.118 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 12:33:31.444 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 12:33:36.169 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:33:36.170 [info] > git config --get commit.template [26ms]
2025-05-24 12:33:36.171 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:33:36.228 [info] > git status -z -uall [27ms]
2025-05-24 12:33:36.230 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:33:36.562 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:33:41.281 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:33:41.282 [info] > git config --get commit.template [25ms]
2025-05-24 12:33:41.283 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:33:41.344 [info] > git status -z -uall [36ms]
2025-05-24 12:33:41.346 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 12:33:41.675 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:33:46.395 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:33:46.395 [info] > git config --get commit.template [24ms]
2025-05-24 12:33:46.396 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:33:46.451 [info] > git status -z -uall [30ms]
2025-05-24 12:33:46.451 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 12:33:46.777 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:33:51.511 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:33:51.512 [info] > git config --get commit.template [31ms]
2025-05-24 12:33:51.514 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:33:51.574 [info] > git status -z -uall [27ms]
2025-05-24 12:33:51.575 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:33:51.907 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:33:56.603 [info] > git config --get commit.template [2ms]
2025-05-24 12:33:56.630 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:33:56.631 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:33:56.686 [info] > git status -z -uall [30ms]
2025-05-24 12:33:56.687 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:33:57.019 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:34:01.716 [info] > git config --get commit.template [2ms]
2025-05-24 12:34:01.744 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:34:01.751 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [8ms]
2025-05-24 12:34:01.811 [info] > git status -z -uall [33ms]
2025-05-24 12:34:01.811 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 12:34:02.145 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:34:06.860 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:34:06.861 [info] > git config --get commit.template [24ms]
2025-05-24 12:34:06.862 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:34:06.912 [info] > git status -z -uall [24ms]
2025-05-24 12:34:06.912 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 12:34:07.237 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:34:11.964 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:34:11.965 [info] > git config --get commit.template [26ms]
2025-05-24 12:34:11.966 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:34:12.015 [info] > git status -z -uall [26ms]
2025-05-24 12:34:12.017 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:34:12.351 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:34:17.065 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:34:17.066 [info] > git config --get commit.template [25ms]
2025-05-24 12:34:17.066 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:34:17.117 [info] > git status -z -uall [26ms]
2025-05-24 12:34:17.121 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-05-24 12:34:17.452 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:34:51.960 [info] > git config --get commit.template [2ms]
2025-05-24 12:34:51.989 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:34:51.994 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-05-24 12:34:52.063 [info] > git status -z -uall [35ms]
2025-05-24 12:34:52.064 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:34:52.405 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:34:52.647 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:34:52.691 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/Checkout.tsx [3ms]
2025-05-24 12:34:52.901 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/Checkout.tsx [4ms]
2025-05-24 12:41:28.730 [info] > git fetch [28ms]
2025-05-24 12:41:28.759 [info] > git config --get commit.template [30ms]
2025-05-24 12:41:28.783 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:41:28.785 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:41:28.786 [info] > git config --get commit.template [28ms]
2025-05-24 12:41:28.813 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:41:28.815 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:41:28.858 [info] > git status -z -uall [22ms]
2025-05-24 12:41:28.859 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:41:29.185 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:41:29.393 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:41:29.429 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/Checkout.tsx [2ms]
2025-05-24 12:41:29.635 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/Checkout.tsx [3ms]
2025-05-24 12:41:30.270 [info] > git ls-files --stage -- shared/schema.ts [2ms]
2025-05-24 12:41:30.301 [info] > git cat-file -s d32f4ffe71fcf6e84526cd8ee980575cb739b036 [2ms]
2025-05-24 12:41:30.524 [info] > git show --textconv :shared/schema.ts [0ms]
2025-05-24 12:41:33.845 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:41:33.846 [info] > git config --get commit.template [34ms]
2025-05-24 12:41:33.846 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:41:33.903 [info] > git status -z -uall [30ms]
2025-05-24 12:41:33.905 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:41:34.236 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:41:38.965 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:41:38.966 [info] > git config --get commit.template [31ms]
2025-05-24 12:41:38.967 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:41:39.014 [info] > git status -z -uall [25ms]
2025-05-24 12:41:39.015 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:41:39.351 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:41:44.137 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:41:44.137 [info] > git config --get commit.template [67ms]
2025-05-24 12:41:44.151 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [15ms]
2025-05-24 12:41:44.248 [info] > git status -z -uall [43ms]
2025-05-24 12:41:44.251 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 12:41:44.589 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:41:49.314 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:41:49.315 [info] > git config --get commit.template [33ms]
2025-05-24 12:41:49.317 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:41:49.379 [info] > git status -z -uall [34ms]
2025-05-24 12:41:49.380 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:41:49.707 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:42:01.443 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:42:01.444 [info] > git config --get commit.template [25ms]
2025-05-24 12:42:01.445 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:42:01.507 [info] > git status -z -uall [34ms]
2025-05-24 12:42:01.507 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 12:42:01.837 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:42:06.559 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:42:06.559 [info] > git config --get commit.template [23ms]
2025-05-24 12:42:06.560 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:42:06.610 [info] > git status -z -uall [27ms]
2025-05-24 12:42:06.611 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:42:06.942 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:42:11.642 [info] > git config --get commit.template [6ms]
2025-05-24 12:42:11.677 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:42:11.678 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:42:11.733 [info] > git status -z -uall [29ms]
2025-05-24 12:42:11.739 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-05-24 12:42:12.077 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:44:10.144 [info] > git config --get commit.template [2ms]
2025-05-24 12:44:10.172 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:44:10.174 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:44:10.223 [info] > git status -z -uall [26ms]
2025-05-24 12:44:10.225 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:44:10.555 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:44:10.766 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:44:10.800 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/Checkout.tsx [3ms]
2025-05-24 12:44:11.004 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/Checkout.tsx [4ms]
2025-05-24 12:44:15.325 [info] > git config --get commit.template [28ms]
2025-05-24 12:44:15.357 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:44:15.357 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-05-24 12:44:15.418 [info] > git status -z -uall [37ms]
2025-05-24 12:44:15.419 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 12:44:15.749 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:44:15.956 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:44:15.990 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/Checkout.tsx [3ms]
2025-05-24 12:44:16.196 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/Checkout.tsx [3ms]
2025-05-24 12:46:05.686 [info] > git fetch [24ms]
2025-05-24 12:46:05.744 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:46:05.744 [info] > git config --get commit.template [59ms]
2025-05-24 12:46:05.745 [info] > git config --get commit.template [36ms]
2025-05-24 12:46:05.768 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:46:05.769 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [25ms]
2025-05-24 12:46:05.770 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:46:05.832 [info] > git status -z -uall [32ms]
2025-05-24 12:46:05.832 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 12:46:06.169 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:46:06.392 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:46:06.435 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/Checkout.tsx [4ms]
2025-05-24 12:46:06.637 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/Checkout.tsx [3ms]
2025-05-24 12:46:07.240 [info] > git ls-files --stage -- shared/schema.ts [2ms]
2025-05-24 12:46:07.270 [info] > git cat-file -s d32f4ffe71fcf6e84526cd8ee980575cb739b036 [1ms]
2025-05-24 12:46:07.477 [info] > git show --textconv :shared/schema.ts [2ms]
2025-05-24 12:46:10.819 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:46:10.819 [info] > git config --get commit.template [25ms]
2025-05-24 12:46:10.820 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:46:10.875 [info] > git status -z -uall [32ms]
2025-05-24 12:46:10.876 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:46:11.207 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:46:15.929 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:46:15.929 [info] > git config --get commit.template [22ms]
2025-05-24 12:46:15.930 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:46:15.987 [info] > git status -z -uall [30ms]
2025-05-24 12:46:15.987 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:46:16.317 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:46:21.045 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:46:21.045 [info] > git config --get commit.template [26ms]
2025-05-24 12:46:21.053 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [8ms]
2025-05-24 12:46:21.105 [info] > git status -z -uall [27ms]
2025-05-24 12:46:21.106 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:46:21.437 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:46:26.157 [info] > git config --get commit.template [1ms]
2025-05-24 12:46:26.185 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:46:26.187 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:46:26.246 [info] > git status -z -uall [28ms]
2025-05-24 12:46:26.248 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 12:46:26.585 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:46:31.301 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:46:31.302 [info] > git config --get commit.template [29ms]
2025-05-24 12:46:31.303 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:46:31.353 [info] > git status -z -uall [26ms]
2025-05-24 12:46:31.354 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:46:31.685 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:46:36.442 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:46:36.442 [info] > git config --get commit.template [48ms]
2025-05-24 12:46:36.443 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:46:36.497 [info] > git status -z -uall [25ms]
2025-05-24 12:46:36.500 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 12:46:36.828 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:46:37.074 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:46:37.164 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/Checkout.tsx [58ms]
2025-05-24 12:46:37.347 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/Checkout.tsx [5ms]
2025-05-24 12:46:41.569 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:46:41.570 [info] > git config --get commit.template [35ms]
2025-05-24 12:46:41.570 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:46:41.617 [info] > git status -z -uall [25ms]
2025-05-24 12:46:41.619 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:46:41.943 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:46:46.704 [info] > git config --get commit.template [4ms]
2025-05-24 12:46:46.730 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:46:46.734 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 12:46:46.785 [info] > git status -z -uall [28ms]
2025-05-24 12:46:46.786 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:46:47.114 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:46:51.849 [info] > git config --get commit.template [32ms]
2025-05-24 12:46:51.850 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:46:51.850 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:46:51.908 [info] > git status -z -uall [32ms]
2025-05-24 12:46:51.910 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 12:46:52.242 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:46:56.949 [info] > git config --get commit.template [4ms]
2025-05-24 12:46:56.977 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:46:56.979 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:46:57.035 [info] > git status -z -uall [31ms]
2025-05-24 12:46:57.037 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:46:57.373 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:47:47.139 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:47:47.139 [info] > git config --get commit.template [25ms]
2025-05-24 12:47:47.141 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:47:47.200 [info] > git status -z -uall [27ms]
2025-05-24 12:47:47.200 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 12:47:47.531 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:47:52.233 [info] > git config --get commit.template [1ms]
2025-05-24 12:47:52.265 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:47:52.266 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:47:52.318 [info] > git status -z -uall [26ms]
2025-05-24 12:47:52.320 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:47:52.707 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:47:57.369 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:47:57.370 [info] > git config --get commit.template [23ms]
2025-05-24 12:47:57.371 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:47:57.423 [info] > git status -z -uall [30ms]
2025-05-24 12:47:57.425 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 12:47:57.757 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:48:02.486 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:48:02.486 [info] > git config --get commit.template [25ms]
2025-05-24 12:48:02.487 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:48:02.538 [info] > git status -z -uall [23ms]
2025-05-24 12:48:02.539 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:48:02.869 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:48:07.587 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:48:07.588 [info] > git config --get commit.template [22ms]
2025-05-24 12:48:07.589 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:48:07.647 [info] > git status -z -uall [30ms]
2025-05-24 12:48:07.650 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 12:48:07.977 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:50:20.465 [info] > git fetch [40ms]
2025-05-24 12:50:20.527 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:50:20.528 [info] > git config --get commit.template [27ms]
2025-05-24 12:50:20.528 [info] > git config --get commit.template [65ms]
2025-05-24 12:50:20.551 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:50:20.552 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [26ms]
2025-05-24 12:50:20.553 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:50:20.606 [info] > git status -z -uall [29ms]
2025-05-24 12:50:20.607 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:50:20.939 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:50:21.147 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:50:21.182 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/Checkout.tsx [3ms]
2025-05-24 12:50:21.394 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/Checkout.tsx [12ms]
2025-05-24 12:50:22.078 [info] > git ls-files --stage -- shared/schema.ts [2ms]
2025-05-24 12:50:22.104 [info] > git cat-file -s d32f4ffe71fcf6e84526cd8ee980575cb739b036 [2ms]
2025-05-24 12:50:22.322 [info] > git show --textconv :shared/schema.ts [2ms]
2025-05-24 12:50:25.611 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:50:25.611 [info] > git config --get commit.template [28ms]
2025-05-24 12:50:25.612 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:50:25.714 [info] > git status -z -uall [76ms]
2025-05-24 12:50:25.715 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [53ms]
2025-05-24 12:50:26.042 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:50:32.470 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:50:32.470 [info] > git config --get commit.template [32ms]
2025-05-24 12:50:32.471 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:50:32.532 [info] > git status -z -uall [31ms]
2025-05-24 12:50:32.532 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 12:50:32.868 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:50:37.588 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:50:37.589 [info] > git config --get commit.template [30ms]
2025-05-24 12:50:37.589 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:50:37.705 [info] > git status -z -uall [89ms]
2025-05-24 12:50:37.705 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [60ms]
2025-05-24 12:50:38.037 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:50:42.759 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:50:42.759 [info] > git config --get commit.template [23ms]
2025-05-24 12:50:42.760 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:50:42.808 [info] > git status -z -uall [25ms]
2025-05-24 12:50:42.809 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:50:43.136 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:53:12.377 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:53:12.377 [info] > git config --get commit.template [26ms]
2025-05-24 12:53:12.379 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:53:12.422 [info] > git status -z -uall [21ms]
2025-05-24 12:53:12.423 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 12:53:12.751 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:53:13.001 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:53:13.085 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/context/RestaurantStatusContext.tsx [36ms]
2025-05-24 12:53:13.085 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/Checkout.tsx [4ms]
2025-05-24 12:53:13.327 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/context/RestaurantStatusContext.tsx [36ms]
2025-05-24 12:53:13.328 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/Checkout.tsx [5ms]
2025-05-24 12:53:17.474 [info] > git config --get commit.template [25ms]
2025-05-24 12:53:17.474 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:53:17.475 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:53:17.526 [info] > git status -z -uall [25ms]
2025-05-24 12:53:17.527 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 12:53:17.855 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:53:20.653 [info] > git fetch [10ms]
2025-05-24 12:53:20.685 [info] > git config --get commit.template [3ms]
2025-05-24 12:53:20.712 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:53:20.715 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:53:20.760 [info] > git status -z -uall [22ms]
2025-05-24 12:53:20.761 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 12:53:21.093 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:53:22.405 [info] > git ls-files --stage -- shared/schema.ts [2ms]
2025-05-24 12:53:22.431 [info] > git cat-file -s d32f4ffe71fcf6e84526cd8ee980575cb739b036 [1ms]
2025-05-24 12:53:22.554 [info] > git config --get commit.template [0ms]
2025-05-24 12:53:22.587 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:53:22.588 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:53:22.664 [info] > git status -z -uall [53ms]
2025-05-24 12:53:22.720 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [83ms]
2025-05-24 12:53:22.722 [info] > git show --textconv :shared/schema.ts [58ms]
2025-05-24 12:53:23.046 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:53:27.779 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:53:27.781 [info] > git config --get commit.template [28ms]
2025-05-24 12:53:27.781 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:53:27.844 [info] > git status -z -uall [34ms]
2025-05-24 12:53:27.845 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:53:28.174 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:53:32.868 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:53:32.891 [info] > git config --get commit.template [24ms]
2025-05-24 12:53:32.892 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:53:32.941 [info] > git status -z -uall [24ms]
2025-05-24 12:53:32.942 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:53:33.272 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:53:37.974 [info] > git config --get commit.template [1ms]
2025-05-24 12:53:38.002 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:53:38.004 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:53:38.053 [info] > git status -z -uall [23ms]
2025-05-24 12:53:38.054 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 12:53:38.386 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:53:43.107 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:53:43.107 [info] > git config --get commit.template [28ms]
2025-05-24 12:53:43.109 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:53:43.159 [info] > git status -z -uall [24ms]
2025-05-24 12:53:43.160 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:53:43.493 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-05-24 12:53:48.211 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:53:48.211 [info] > git config --get commit.template [23ms]
2025-05-24 12:53:48.212 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:53:48.265 [info] > git status -z -uall [30ms]
2025-05-24 12:53:48.266 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:53:48.590 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:53:53.317 [info] > git config --get commit.template [24ms]
2025-05-24 12:53:53.317 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:53:53.318 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:53:53.369 [info] > git status -z -uall [26ms]
2025-05-24 12:53:53.370 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:53:53.696 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:53:58.428 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:53:58.428 [info] > git config --get commit.template [29ms]
2025-05-24 12:53:58.429 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:53:58.479 [info] > git status -z -uall [27ms]
2025-05-24 12:53:58.480 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 12:53:58.821 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:54:03.537 [info] > git config --get commit.template [16ms]
2025-05-24 12:54:03.589 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:54:03.593 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-05-24 12:54:03.666 [info] > git status -z -uall [35ms]
2025-05-24 12:54:03.668 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:54:03.995 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:54:08.698 [info] > git config --get commit.template [3ms]
2025-05-24 12:54:08.720 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:54:08.721 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:54:08.766 [info] > git status -z -uall [22ms]
2025-05-24 12:54:08.767 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:54:09.094 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:54:43.321 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:54:43.321 [info] > git config --get commit.template [21ms]
2025-05-24 12:54:43.322 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:54:43.387 [info] > git status -z -uall [45ms]
2025-05-24 12:54:43.388 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:54:43.749 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:54:48.458 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:54:48.465 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-05-24 12:54:48.465 [info] > git config --get commit.template [47ms]
2025-05-24 12:54:48.549 [info] > git status -z -uall [53ms]
2025-05-24 12:54:48.551 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 12:54:48.903 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:55:00.707 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:55:00.708 [info] > git config --get commit.template [23ms]
2025-05-24 12:55:00.709 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:55:00.760 [info] > git status -z -uall [25ms]
2025-05-24 12:55:00.760 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 12:55:01.088 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:55:05.808 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:55:05.809 [info] > git config --get commit.template [23ms]
2025-05-24 12:55:05.809 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:55:05.863 [info] > git status -z -uall [26ms]
2025-05-24 12:55:05.869 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-05-24 12:55:06.200 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:55:10.924 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:55:10.925 [info] > git config --get commit.template [31ms]
2025-05-24 12:55:10.926 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:55:10.979 [info] > git status -z -uall [26ms]
2025-05-24 12:55:10.979 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:55:11.306 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:55:16.057 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:55:16.057 [info] > git config --get commit.template [39ms]
2025-05-24 12:55:16.059 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:55:16.133 [info] > git status -z -uall [36ms]
2025-05-24 12:55:16.134 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:55:16.469 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:55:51.078 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:55:51.079 [info] > git config --get commit.template [28ms]
2025-05-24 12:55:51.080 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:55:51.143 [info] > git status -z -uall [35ms]
2025-05-24 12:55:51.148 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-05-24 12:55:51.484 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 12:57:25.641 [info] > git fetch [27ms]
2025-05-24 12:57:25.688 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:57:25.689 [info] > git config --get commit.template [48ms]
2025-05-24 12:57:25.737 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [49ms]
2025-05-24 12:57:25.738 [info] > git config --get commit.template [71ms]
2025-05-24 12:57:25.761 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:57:25.762 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:57:25.820 [info] > git status -z -uall [32ms]
2025-05-24 12:57:25.820 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-05-24 12:57:26.147 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:57:27.388 [info] > git ls-files --stage -- shared/schema.ts [2ms]
2025-05-24 12:57:27.414 [info] > git cat-file -s d32f4ffe71fcf6e84526cd8ee980575cb739b036 [2ms]
2025-05-24 12:57:27.624 [info] > git show --textconv :shared/schema.ts [1ms]
2025-05-24 12:57:30.772 [info] > git config --get commit.template [4ms]
2025-05-24 12:57:30.801 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:57:30.803 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:57:30.856 [info] > git status -z -uall [26ms]
2025-05-24 12:57:30.857 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:57:31.191 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-05-24 12:57:36.066 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:57:36.067 [info] > git config --get commit.template [39ms]
2025-05-24 12:57:36.068 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:57:36.145 [info] > git status -z -uall [44ms]
2025-05-24 12:57:36.146 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:57:36.479 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:57:41.671 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:57:41.672 [info] > git config --get commit.template [20ms]
2025-05-24 12:57:41.672 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:57:41.734 [info] > git status -z -uall [33ms]
2025-05-24 12:57:41.735 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:57:42.063 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:57:46.784 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:57:46.787 [info] > git config --get commit.template [24ms]
2025-05-24 12:57:46.788 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 12:57:46.827 [info] > git status -z -uall [20ms]
2025-05-24 12:57:46.840 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [14ms]
2025-05-24 12:57:47.166 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:57:51.876 [info] > git config --get commit.template [2ms]
2025-05-24 12:57:51.908 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:57:51.909 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:57:51.960 [info] > git status -z -uall [23ms]
2025-05-24 12:57:51.961 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:57:52.289 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:57:57.004 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:57:57.004 [info] > git config --get commit.template [19ms]
2025-05-24 12:57:57.005 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:57:57.038 [info] > git status -z -uall [15ms]
2025-05-24 12:57:57.039 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 12:57:57.373 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:58:02.081 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:58:02.082 [info] > git config --get commit.template [19ms]
2025-05-24 12:58:02.083 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:58:02.115 [info] > git status -z -uall [16ms]
2025-05-24 12:58:02.116 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 12:58:02.442 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:58:07.166 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:58:07.167 [info] > git config --get commit.template [25ms]
2025-05-24 12:58:07.168 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:58:07.216 [info] > git status -z -uall [24ms]
2025-05-24 12:58:07.218 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:58:07.544 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:58:12.262 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:58:12.262 [info] > git config --get commit.template [20ms]
2025-05-24 12:58:12.263 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:58:12.306 [info] > git status -z -uall [21ms]
2025-05-24 12:58:12.308 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 12:58:12.632 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:58:17.352 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:58:17.352 [info] > git config --get commit.template [20ms]
2025-05-24 12:58:17.353 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:58:17.387 [info] > git status -z -uall [16ms]
2025-05-24 12:58:17.388 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:58:17.717 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:58:22.419 [info] > git config --get commit.template [0ms]
2025-05-24 12:58:22.454 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:58:22.456 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:58:22.529 [info] > git status -z -uall [32ms]
2025-05-24 12:58:22.535 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-05-24 12:58:22.879 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:58:27.584 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:58:27.584 [info] > git config --get commit.template [22ms]
2025-05-24 12:58:27.585 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:58:27.633 [info] > git status -z -uall [26ms]
2025-05-24 12:58:27.634 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:58:27.971 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:58:32.704 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:58:32.707 [info] > git config --get commit.template [43ms]
2025-05-24 12:58:32.708 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 12:58:32.773 [info] > git status -z -uall [33ms]
2025-05-24 12:58:32.775 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 12:58:33.110 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:58:37.821 [info] > git config --get commit.template [2ms]
2025-05-24 12:58:37.846 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:58:37.847 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:58:37.899 [info] > git status -z -uall [26ms]
2025-05-24 12:58:37.900 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:58:38.228 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:58:42.937 [info] > git config --get commit.template [10ms]
2025-05-24 12:58:42.977 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:58:42.979 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:58:43.073 [info] > git status -z -uall [51ms]
2025-05-24 12:58:43.082 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [12ms]
2025-05-24 12:58:43.430 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 12:58:53.869 [info] > git config --get commit.template [14ms]
2025-05-24 12:58:53.904 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:58:53.907 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:58:53.965 [info] > git status -z -uall [24ms]
2025-05-24 12:58:53.966 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:58:54.299 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:58:54.543 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 12:58:54.638 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/context/RestaurantStatusContext.tsx [58ms]
2025-05-24 12:58:54.638 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/Checkout.tsx [32ms]
2025-05-24 12:58:54.638 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:server/auth.ts [2ms]
2025-05-24 12:58:54.894 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- server/auth.ts [32ms]
2025-05-24 12:58:54.894 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/context/RestaurantStatusContext.tsx [58ms]
2025-05-24 12:58:54.898 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/Checkout.tsx [5ms]
2025-05-24 12:58:59.024 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:58:59.025 [info] > git config --get commit.template [28ms]
2025-05-24 12:58:59.028 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 12:58:59.105 [info] > git status -z -uall [36ms]
2025-05-24 12:58:59.105 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 12:58:59.434 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 12:59:04.165 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 12:59:04.166 [info] > git config --get commit.template [23ms]
2025-05-24 12:59:04.167 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 12:59:04.236 [info] > git status -z -uall [50ms]
2025-05-24 12:59:04.237 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 12:59:04.564 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:03:10.378 [info] > git fetch [30ms]
2025-05-24 13:03:10.379 [info] > git config --get commit.template [2ms]
2025-05-24 13:03:10.434 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:03:10.435 [info] > git config --get commit.template [30ms]
2025-05-24 13:03:10.463 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:03:10.464 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [30ms]
2025-05-24 13:03:10.465 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:03:10.523 [info] > git status -z -uall [33ms]
2025-05-24 13:03:10.525 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 13:03:10.559 [info] > git ls-files --stage -- shared/schema.ts [2ms]
2025-05-24 13:03:10.586 [info] > git cat-file -s d32f4ffe71fcf6e84526cd8ee980575cb739b036 [1ms]
2025-05-24 13:03:10.871 [info] > git show --textconv :shared/schema.ts [25ms]
2025-05-24 13:03:10.872 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:03:11.093 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:03:11.253 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/context/RestaurantStatusContext.tsx [110ms]
2025-05-24 13:03:11.253 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/Checkout.tsx [83ms]
2025-05-24 13:03:11.253 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:server/admin-api.ts [54ms]
2025-05-24 13:03:11.253 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:server/auth.ts [27ms]
2025-05-24 13:03:11.253 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:server/routes.ts [3ms]
2025-05-24 13:03:11.564 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/Checkout.tsx [5ms]
2025-05-24 13:03:11.565 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- server/auth.ts [37ms]
2025-05-24 13:03:11.565 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- server/routes.ts [65ms]
2025-05-24 13:03:11.565 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- server/admin-api.ts [91ms]
2025-05-24 13:03:11.565 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/context/RestaurantStatusContext.tsx [115ms]
2025-05-24 13:03:11.997 [info] > git ls-files --stage -- shared/schema.ts [1ms]
2025-05-24 13:03:12.025 [info] > git cat-file -s d32f4ffe71fcf6e84526cd8ee980575cb739b036 [2ms]
2025-05-24 13:03:12.233 [info] > git show --textconv :shared/schema.ts [4ms]
2025-05-24 13:03:15.530 [info] > git config --get commit.template [39ms]
2025-05-24 13:03:15.531 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:03:15.533 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 13:03:15.594 [info] > git status -z -uall [28ms]
2025-05-24 13:03:15.596 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:03:15.924 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:03:20.643 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:03:20.646 [info] > git config --get commit.template [24ms]
2025-05-24 13:03:20.646 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 13:03:20.684 [info] > git status -z -uall [16ms]
2025-05-24 13:03:20.685 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 13:03:21.015 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:03:21.237 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:03:21.402 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/context/RestaurantStatusContext.tsx [126ms]
2025-05-24 13:03:21.402 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/Checkout.tsx [102ms]
2025-05-24 13:03:21.402 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:cookies.txt [76ms]
2025-05-24 13:03:21.402 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:server/admin-api.ts [51ms]
2025-05-24 13:03:21.402 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:server/auth.ts [28ms]
2025-05-24 13:03:21.402 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:server/routes.ts [3ms]
2025-05-24 13:03:21.747 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- cookies.txt [26ms]
2025-05-24 13:03:21.747 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- server/auth.ts [53ms]
2025-05-24 13:03:21.748 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- server/admin-api.ts [79ms]
2025-05-24 13:03:21.748 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/Checkout.tsx [111ms]
2025-05-24 13:03:21.748 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/context/RestaurantStatusContext.tsx [134ms]
2025-05-24 13:03:21.751 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- server/routes.ts [6ms]
2025-05-24 13:03:25.720 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:03:25.749 [info] > git config --get commit.template [29ms]
2025-05-24 13:03:25.750 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:03:25.870 [info] > git status -z -uall [97ms]
2025-05-24 13:03:25.871 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [77ms]
2025-05-24 13:03:26.207 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 13:03:30.903 [info] > git config --get commit.template [1ms]
2025-05-24 13:03:30.941 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:03:30.942 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:03:30.997 [info] > git status -z -uall [25ms]
2025-05-24 13:03:30.998 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:03:31.334 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:03:36.047 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:03:36.048 [info] > git config --get commit.template [24ms]
2025-05-24 13:03:36.049 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:03:36.086 [info] > git status -z -uall [20ms]
2025-05-24 13:03:36.087 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:03:36.422 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:03:43.086 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:03:43.087 [info] > git config --get commit.template [21ms]
2025-05-24 13:03:43.089 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 13:03:43.147 [info] > git status -z -uall [28ms]
2025-05-24 13:03:43.148 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:03:43.479 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:03:44.054 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:03:44.262 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/context/RestaurantStatusContext.tsx [169ms]
2025-05-24 13:03:44.263 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/Checkout.tsx [147ms]
2025-05-24 13:03:44.263 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:cookies.txt [112ms]
2025-05-24 13:03:44.263 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:server/admin-api.ts [89ms]
2025-05-24 13:03:44.263 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:server/auth.ts [65ms]
2025-05-24 13:03:44.263 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:server/routes.ts [35ms]
2025-05-24 13:03:44.264 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:cookies2.txt [8ms]
2025-05-24 13:03:44.338 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [6ms]
2025-05-24 13:03:44.996 [info] > git hash-object -w --stdin [3ms]
2025-05-24 13:03:44.997 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- server/routes.ts [33ms]
2025-05-24 13:03:44.997 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- server/auth.ts [60ms]
2025-05-24 13:03:44.997 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- server/admin-api.ts [96ms]
2025-05-24 13:03:44.998 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- cookies.txt [126ms]
2025-05-24 13:03:44.998 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/Checkout.tsx [157ms]
2025-05-24 13:03:44.998 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/context/RestaurantStatusContext.tsx [187ms]
2025-05-24 13:03:45.041 [info] > git config --get commit.template [8ms]
2025-05-24 13:03:45.073 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:03:45.075 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:03:45.134 [info] > git status -z -uall [27ms]
2025-05-24 13:03:45.135 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:03:45.164 [info] > git hash-object -w --stdin [2ms]
2025-05-24 13:03:45.214 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:03:45.214 [info] > git config --get commit.template [24ms]
2025-05-24 13:03:45.217 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 13:03:45.271 [info] > git status -z -uall [26ms]
2025-05-24 13:03:45.271 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 13:03:45.306 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 f45b58453d90a2f7c53517f142627fc254b4a369 [3ms]
2025-05-24 13:03:45.600 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:03:48.201 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:03:48.202 [info] > git config --get commit.template [26ms]
2025-05-24 13:03:48.204 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 13:03:48.258 [info] > git status -z -uall [31ms]
2025-05-24 13:03:48.260 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 13:03:48.592 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:03:53.316 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:03:53.316 [info] > git config --get commit.template [22ms]
2025-05-24 13:03:53.317 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:03:53.380 [info] > git status -z -uall [26ms]
2025-05-24 13:03:53.381 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 13:03:53.712 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:03:58.416 [info] > git config --get commit.template [2ms]
2025-05-24 13:03:58.442 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:03:58.444 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:03:58.482 [info] > git status -z -uall [18ms]
2025-05-24 13:03:58.483 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:03:58.850 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:04:43.459 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:04:43.459 [info] > git config --get commit.template [23ms]
2025-05-24 13:04:43.460 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:04:43.501 [info] > git status -z -uall [21ms]
2025-05-24 13:04:43.502 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 13:04:43.827 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:04:48.558 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:04:48.559 [info] > git config --get commit.template [29ms]
2025-05-24 13:04:48.559 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:04:48.613 [info] > git status -z -uall [29ms]
2025-05-24 13:04:48.615 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 13:04:48.947 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:04:53.663 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:04:53.664 [info] > git config --get commit.template [23ms]
2025-05-24 13:04:53.665 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:04:53.707 [info] > git status -z -uall [20ms]
2025-05-24 13:04:53.709 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:04:54.034 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:04:58.760 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:04:58.761 [info] > git config --get commit.template [26ms]
2025-05-24 13:04:58.762 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:04:58.862 [info] > git status -z -uall [78ms]
2025-05-24 13:04:58.862 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [50ms]
2025-05-24 13:04:59.188 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:05:03.895 [info] > git config --get commit.template [2ms]
2025-05-24 13:05:03.924 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:05:03.925 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:05:03.982 [info] > git status -z -uall [27ms]
2025-05-24 13:05:03.983 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:05:04.311 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:05:09.046 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:05:09.047 [info] > git config --get commit.template [27ms]
2025-05-24 13:05:09.047 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:05:09.091 [info] > git status -z -uall [21ms]
2025-05-24 13:05:09.092 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:05:09.422 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:05:14.143 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:05:14.144 [info] > git config --get commit.template [25ms]
2025-05-24 13:05:14.145 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:05:14.247 [info] > git status -z -uall [79ms]
2025-05-24 13:05:14.301 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [108ms]
2025-05-24 13:05:14.636 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:05:19.361 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:05:19.362 [info] > git config --get commit.template [31ms]
2025-05-24 13:05:19.362 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:05:19.419 [info] > git status -z -uall [25ms]
2025-05-24 13:05:19.420 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:05:19.747 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:05:24.473 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:05:24.473 [info] > git config --get commit.template [26ms]
2025-05-24 13:05:24.474 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:05:24.520 [info] > git status -z -uall [24ms]
2025-05-24 13:05:24.522 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:05:24.848 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:05:35.658 [info] > git config --get commit.template [2ms]
2025-05-24 13:05:35.686 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:05:35.687 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:05:35.737 [info] > git status -z -uall [27ms]
2025-05-24 13:05:35.738 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:05:36.064 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:05:36.285 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:05:36.519 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/context/RestaurantStatusContext.tsx [170ms]
2025-05-24 13:05:36.519 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/Checkout.tsx [152ms]
2025-05-24 13:05:36.519 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/auth/AuthPage.tsx [137ms]
2025-05-24 13:05:36.519 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:cookies.txt [112ms]
2025-05-24 13:05:36.520 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:server/admin-api.ts [78ms]
2025-05-24 13:05:36.520 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:server/auth.ts [55ms]
2025-05-24 13:05:36.520 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:server/routes.ts [35ms]
2025-05-24 13:05:36.520 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:cookies2.txt [5ms]
2025-05-24 13:05:36.556 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [4ms]
2025-05-24 13:05:36.914 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/auth/AuthPage.tsx [168ms]
2025-05-24 13:05:36.914 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/context/RestaurantStatusContext.tsx [194ms]
2025-05-24 13:05:36.916 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/Checkout.tsx [7ms]
2025-05-24 13:05:36.916 [info] > git hash-object -w --stdin [37ms]
2025-05-24 13:05:36.917 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- server/routes.ts [59ms]
2025-05-24 13:05:36.917 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- server/admin-api.ts [83ms]
2025-05-24 13:05:36.917 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- server/auth.ts [114ms]
2025-05-24 13:05:36.917 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- cookies.txt [143ms]
2025-05-24 13:05:36.958 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:05:36.958 [info] > git config --get commit.template [17ms]
2025-05-24 13:05:36.960 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:05:36.997 [info] > git status -z -uall [19ms]
2025-05-24 13:05:36.998 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:05:37.025 [info] > git hash-object -w --stdin [2ms]
2025-05-24 13:05:37.072 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:05:37.073 [info] > git config --get commit.template [22ms]
2025-05-24 13:05:37.074 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:05:37.151 [info] > git status -z -uall [31ms]
2025-05-24 13:05:37.151 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:05:37.176 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 f45b58453d90a2f7c53517f142627fc254b4a369 [2ms]
2025-05-24 13:05:37.476 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:05:40.870 [info] > git config --get commit.template [59ms]
2025-05-24 13:05:40.895 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:05:40.899 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 13:05:40.950 [info] > git status -z -uall [24ms]
2025-05-24 13:05:40.951 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 13:05:41.275 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:05:47.802 [info] > git config --get commit.template [2ms]
2025-05-24 13:05:47.840 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:05:47.841 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:05:47.906 [info] > git status -z -uall [30ms]
2025-05-24 13:05:47.907 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 13:05:48.239 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:05:52.984 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:05:52.985 [info] > git config --get commit.template [39ms]
2025-05-24 13:05:52.988 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 13:05:53.060 [info] > git status -z -uall [36ms]
2025-05-24 13:05:53.063 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 13:05:53.412 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-05-24 13:05:58.094 [info] > git config --get commit.template [3ms]
2025-05-24 13:05:58.133 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:05:58.135 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:05:58.194 [info] > git status -z -uall [31ms]
2025-05-24 13:05:58.196 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:05:58.528 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:06:03.256 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:06:03.256 [info] > git config --get commit.template [28ms]
2025-05-24 13:06:03.257 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:06:03.309 [info] > git status -z -uall [27ms]
2025-05-24 13:06:03.311 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 13:06:03.644 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:06:08.359 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:06:08.360 [info] > git config --get commit.template [23ms]
2025-05-24 13:06:08.361 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:06:08.416 [info] > git status -z -uall [34ms]
2025-05-24 13:06:08.417 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:06:08.746 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 13:06:10.560 [info] > git fetch [5ms]
2025-05-24 13:06:10.621 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:06:10.623 [info] > git config --get commit.template [36ms]
2025-05-24 13:06:10.623 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:06:10.678 [info] > git status -z -uall [27ms]
2025-05-24 13:06:10.680 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:06:11.013 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:06:12.163 [info] > git ls-files --stage -- shared/schema.ts [3ms]
2025-05-24 13:06:12.184 [info] > git cat-file -s d32f4ffe71fcf6e84526cd8ee980575cb739b036 [1ms]
2025-05-24 13:06:12.384 [info] > git show --textconv :shared/schema.ts [2ms]
2025-05-24 13:06:13.477 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:06:13.478 [info] > git config --get commit.template [29ms]
2025-05-24 13:06:13.481 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 13:06:13.559 [info] > git status -z -uall [37ms]
2025-05-24 13:06:13.561 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 13:06:13.889 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 13:06:18.615 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:06:18.615 [info] > git config --get commit.template [29ms]
2025-05-24 13:06:18.617 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 13:06:18.667 [info] > git status -z -uall [25ms]
2025-05-24 13:06:18.669 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 13:06:19.008 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 13:06:23.718 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:06:23.718 [info] > git config --get commit.template [24ms]
2025-05-24 13:06:23.720 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 13:06:23.775 [info] > git status -z -uall [30ms]
2025-05-24 13:06:23.777 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 13:06:24.121 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:06:28.865 [info] > git config --get commit.template [51ms]
2025-05-24 13:06:28.866 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:06:28.894 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:06:28.961 [info] > git status -z -uall [37ms]
2025-05-24 13:06:28.961 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 13:06:29.289 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:06:34.018 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:06:34.018 [info] > git config --get commit.template [28ms]
2025-05-24 13:06:34.019 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:06:34.068 [info] > git status -z -uall [26ms]
2025-05-24 13:06:34.069 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:06:34.393 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:06:39.117 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:06:39.118 [info] > git config --get commit.template [24ms]
2025-05-24 13:06:39.119 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:06:39.175 [info] > git status -z -uall [27ms]
2025-05-24 13:06:39.176 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:06:39.509 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:06:44.239 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:06:44.240 [info] > git config --get commit.template [33ms]
2025-05-24 13:06:44.240 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:06:44.283 [info] > git status -z -uall [19ms]
2025-05-24 13:06:44.284 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:06:44.626 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:06:49.349 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:06:49.350 [info] > git config --get commit.template [26ms]
2025-05-24 13:06:49.351 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:06:49.411 [info] > git status -z -uall [31ms]
2025-05-24 13:06:49.413 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:06:49.743 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:06:54.474 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:06:54.474 [info] > git config --get commit.template [25ms]
2025-05-24 13:06:54.475 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:06:54.525 [info] > git status -z -uall [25ms]
2025-05-24 13:06:54.527 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 13:06:54.866 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:06:59.573 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:06:59.573 [info] > git config --get commit.template [21ms]
2025-05-24 13:06:59.574 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:06:59.637 [info] > git status -z -uall [33ms]
2025-05-24 13:06:59.639 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:06:59.972 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:07:04.664 [info] > git config --get commit.template [1ms]
2025-05-24 13:07:04.687 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:07:04.689 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:07:04.735 [info] > git status -z -uall [24ms]
2025-05-24 13:07:04.737 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:07:05.065 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 13:07:09.799 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:07:09.799 [info] > git config --get commit.template [29ms]
2025-05-24 13:07:09.800 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:07:09.862 [info] > git status -z -uall [37ms]
2025-05-24 13:07:09.863 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-05-24 13:07:10.194 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:07:14.916 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:07:14.917 [info] > git config --get commit.template [26ms]
2025-05-24 13:07:14.918 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:07:14.974 [info] > git status -z -uall [30ms]
2025-05-24 13:07:14.975 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:07:15.305 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:07:20.007 [info] > git config --get commit.template [3ms]
2025-05-24 13:07:20.030 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:07:20.031 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:07:20.136 [info] > git status -z -uall [82ms]
2025-05-24 13:07:20.136 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [61ms]
2025-05-24 13:07:20.463 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:07:25.178 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:07:25.178 [info] > git config --get commit.template [18ms]
2025-05-24 13:07:25.179 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:07:25.237 [info] > git status -z -uall [35ms]
2025-05-24 13:07:25.238 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 13:07:25.569 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:07:30.297 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:07:30.297 [info] > git config --get commit.template [24ms]
2025-05-24 13:07:30.298 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:07:30.366 [info] > git status -z -uall [31ms]
2025-05-24 13:07:30.367 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:07:30.694 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:07:35.423 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:07:35.423 [info] > git config --get commit.template [31ms]
2025-05-24 13:07:35.424 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:07:35.475 [info] > git status -z -uall [26ms]
2025-05-24 13:07:35.477 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:07:35.804 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 13:07:40.533 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:07:40.533 [info] > git config --get commit.template [31ms]
2025-05-24 13:07:40.535 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:07:40.591 [info] > git status -z -uall [27ms]
2025-05-24 13:07:40.592 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:07:40.920 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:07:45.645 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:07:45.645 [info] > git config --get commit.template [24ms]
2025-05-24 13:07:45.647 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 13:07:45.694 [info] > git status -z -uall [23ms]
2025-05-24 13:07:45.696 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:07:46.026 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:07:50.746 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:07:50.746 [info] > git config --get commit.template [24ms]
2025-05-24 13:07:50.747 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:07:50.792 [info] > git status -z -uall [22ms]
2025-05-24 13:07:50.793 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 13:07:51.120 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:07:55.873 [info] > git config --get commit.template [54ms]
2025-05-24 13:07:55.897 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:07:55.899 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 13:07:55.954 [info] > git status -z -uall [32ms]
2025-05-24 13:07:55.954 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 13:07:56.287 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:08:01.003 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:08:01.003 [info] > git config --get commit.template [24ms]
2025-05-24 13:08:01.004 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:08:01.054 [info] > git status -z -uall [23ms]
2025-05-24 13:08:01.055 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:08:01.387 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:08:06.104 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:08:06.105 [info] > git config --get commit.template [21ms]
2025-05-24 13:08:06.106 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:08:06.152 [info] > git status -z -uall [23ms]
2025-05-24 13:08:06.152 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 13:08:06.482 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:08:11.200 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:08:11.201 [info] > git config --get commit.template [25ms]
2025-05-24 13:08:11.202 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:08:11.266 [info] > git status -z -uall [30ms]
2025-05-24 13:08:11.267 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 13:08:11.596 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:08:16.333 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:08:16.333 [info] > git config --get commit.template [36ms]
2025-05-24 13:08:16.334 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:08:16.376 [info] > git status -z -uall [20ms]
2025-05-24 13:08:16.378 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:08:16.704 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 13:08:21.428 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:08:21.429 [info] > git config --get commit.template [25ms]
2025-05-24 13:08:21.430 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:08:21.485 [info] > git status -z -uall [30ms]
2025-05-24 13:08:21.485 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 13:08:21.817 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:08:26.540 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:08:26.540 [info] > git config --get commit.template [23ms]
2025-05-24 13:08:26.541 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:08:26.589 [info] > git status -z -uall [25ms]
2025-05-24 13:08:26.590 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-05-24 13:08:26.928 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-05-24 13:08:31.642 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:08:31.643 [info] > git config --get commit.template [23ms]
2025-05-24 13:08:31.644 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:08:31.702 [info] > git status -z -uall [34ms]
2025-05-24 13:08:31.705 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 13:08:32.041 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:08:36.765 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:08:36.765 [info] > git config --get commit.template [30ms]
2025-05-24 13:08:36.766 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:08:36.833 [info] > git status -z -uall [38ms]
2025-05-24 13:08:36.835 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:08:37.162 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:08:41.860 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:08:41.885 [info] > git config --get commit.template [24ms]
2025-05-24 13:08:41.885 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:08:41.940 [info] > git status -z -uall [31ms]
2025-05-24 13:08:41.944 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 13:08:42.273 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:08:46.971 [info] > git config --get commit.template [2ms]
2025-05-24 13:08:46.997 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:08:46.998 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:08:47.056 [info] > git status -z -uall [26ms]
2025-05-24 13:08:47.057 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:08:47.383 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:08:52.167 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:08:52.168 [info] > git config --get commit.template [54ms]
2025-05-24 13:08:52.170 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 13:08:52.269 [info] > git status -z -uall [49ms]
2025-05-24 13:08:52.270 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 13:08:52.615 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:08:57.345 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:08:57.345 [info] > git config --get commit.template [40ms]
2025-05-24 13:08:57.346 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:08:57.426 [info] > git status -z -uall [38ms]
2025-05-24 13:08:57.428 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:08:57.762 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 13:09:02.502 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:09:02.503 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:09:02.503 [info] > git config --get commit.template [32ms]
2025-05-24 13:09:02.582 [info] > git status -z -uall [39ms]
2025-05-24 13:09:02.583 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:09:02.922 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [9ms]
2025-05-24 13:09:07.638 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:09:07.639 [info] > git config --get commit.template [25ms]
2025-05-24 13:09:07.640 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:09:07.688 [info] > git status -z -uall [23ms]
2025-05-24 13:09:07.690 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:09:08.018 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:09:10.717 [info] > git fetch [5ms]
2025-05-24 13:09:10.775 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:09:10.775 [info] > git config --get commit.template [32ms]
2025-05-24 13:09:10.776 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:09:10.845 [info] > git status -z -uall [32ms]
2025-05-24 13:09:10.845 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 13:09:11.174 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:09:12.323 [info] > git ls-files --stage -- shared/schema.ts [2ms]
2025-05-24 13:09:12.352 [info] > git cat-file -s d32f4ffe71fcf6e84526cd8ee980575cb739b036 [2ms]
2025-05-24 13:09:12.558 [info] > git show --textconv :shared/schema.ts [2ms]
2025-05-24 13:09:12.738 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:09:12.739 [info] > git config --get commit.template [22ms]
2025-05-24 13:09:12.739 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:09:12.789 [info] > git status -z -uall [25ms]
2025-05-24 13:09:12.791 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:09:13.128 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:09:17.879 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:09:17.879 [info] > git config --get commit.template [36ms]
2025-05-24 13:09:17.880 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:09:17.941 [info] > git status -z -uall [26ms]
2025-05-24 13:09:17.942 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 13:09:18.273 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:09:22.981 [info] > git config --get commit.template [3ms]
2025-05-24 13:09:23.017 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:09:23.167 [info] > git check-ignore -v -z --stdin [77ms]
2025-05-24 13:09:23.168 [info] > git show --textconv :client/src/pages/auth/AuthPage.tsx [38ms]
2025-05-24 13:09:23.168 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [151ms]
2025-05-24 13:09:23.306 [info] > git status -z -uall [110ms]
2025-05-24 13:09:23.307 [info] > git ls-files --stage -- client/src/pages/auth/AuthPage.tsx [140ms]
2025-05-24 13:09:23.338 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [106ms]
2025-05-24 13:09:23.344 [info] > git cat-file -s 2ea9cd441f1a6e2650059ab37f864aad43747cb2 [9ms]
2025-05-24 13:09:23.669 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:09:31.260 [info] > git config --get commit.template [1ms]
2025-05-24 13:09:31.261 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:09:31.285 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:09:31.339 [info] > git status -z -uall [28ms]
2025-05-24 13:09:31.339 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:09:31.667 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:09:36.366 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:09:36.386 [info] > git config --get commit.template [20ms]
2025-05-24 13:09:36.387 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:09:36.455 [info] > git status -z -uall [39ms]
2025-05-24 13:09:36.460 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-05-24 13:09:36.786 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:09:55.850 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:09:55.851 [info] > git config --get commit.template [33ms]
2025-05-24 13:09:55.851 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:09:55.907 [info] > git status -z -uall [24ms]
2025-05-24 13:09:55.908 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:09:56.264 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [11ms]
2025-05-24 13:10:00.972 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:10:00.973 [info] > git config --get commit.template [26ms]
2025-05-24 13:10:00.975 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 13:10:01.028 [info] > git status -z -uall [27ms]
2025-05-24 13:10:01.029 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:10:01.358 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 13:10:06.085 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:10:06.085 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-05-24 13:10:06.086 [info] > git config --get commit.template [25ms]
2025-05-24 13:10:06.141 [info] > git status -z -uall [25ms]
2025-05-24 13:10:06.142 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:10:06.470 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:10:35.631 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:10:35.632 [info] > git config --get commit.template [40ms]
2025-05-24 13:10:35.633 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:10:35.758 [info] > git status -z -uall [98ms]
2025-05-24 13:10:35.759 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [70ms]
2025-05-24 13:10:36.099 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:10:36.334 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:10:36.770 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/context/RestaurantStatusContext.tsx [366ms]
2025-05-24 13:10:36.770 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/Checkout.tsx [332ms]
2025-05-24 13:10:36.770 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/auth/AuthPage.tsx [294ms]
2025-05-24 13:10:36.770 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/auth/LogoutPage.tsx [258ms]
2025-05-24 13:10:36.770 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:cookies.txt [232ms]
2025-05-24 13:10:36.770 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:server/admin-api.ts [202ms]
2025-05-24 13:10:36.770 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:server/auth.ts [164ms]
2025-05-24 13:10:36.770 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:server/routes.ts [129ms]
2025-05-24 13:10:36.770 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:cookies2.txt [93ms]
2025-05-24 13:10:36.868 [info] > git check-ignore -v -z --stdin [2ms]
2025-05-24 13:10:36.869 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [37ms]
2025-05-24 13:10:37.507 [info] > git show --textconv :client/src/pages/auth/LogoutPage.tsx [40ms]
2025-05-24 13:10:37.507 [info] > git hash-object -w --stdin [79ms]
2025-05-24 13:10:37.514 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- server/routes.ts [136ms]
2025-05-24 13:10:37.514 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- server/auth.ts [194ms]
2025-05-24 13:10:37.515 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- server/admin-api.ts [261ms]
2025-05-24 13:10:37.515 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- cookies.txt [303ms]
2025-05-24 13:10:37.515 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/auth/LogoutPage.tsx [336ms]
2025-05-24 13:10:37.516 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/auth/AuthPage.tsx [367ms]
2025-05-24 13:10:37.516 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/Checkout.tsx [442ms]
2025-05-24 13:10:37.516 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/context/RestaurantStatusContext.tsx [516ms]
2025-05-24 13:10:37.559 [info] > git ls-files --stage -- client/src/pages/auth/LogoutPage.tsx [58ms]
2025-05-24 13:10:37.595 [info] > git config --get commit.template [37ms]
2025-05-24 13:10:37.598 [info] > git cat-file -s d36ad335f7e5bcacb98e533de5ae0cc630b6b82d [5ms]
2025-05-24 13:10:37.659 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:10:37.662 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 13:10:37.787 [info] > git status -z -uall [79ms]
2025-05-24 13:10:37.799 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [16ms]
2025-05-24 13:10:37.969 [info] > git hash-object -w --stdin [58ms]
2025-05-24 13:10:38.057 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:10:38.058 [info] > git config --get commit.template [32ms]
2025-05-24 13:10:38.063 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-05-24 13:10:38.374 [info] > git status -z -uall [244ms]
2025-05-24 13:10:38.374 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [138ms]
2025-05-24 13:10:38.414 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [128ms]
2025-05-24 13:10:38.415 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 f45b58453d90a2f7c53517f142627fc254b4a369 [5ms]
2025-05-24 13:10:38.706 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:10:40.855 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:10:40.855 [info] > git config --get commit.template [34ms]
2025-05-24 13:10:40.856 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:10:40.920 [info] > git status -z -uall [33ms]
2025-05-24 13:10:40.922 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 13:10:41.318 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [15ms]
2025-05-24 13:11:04.477 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:11:04.478 [info] > git config --get commit.template [32ms]
2025-05-24 13:11:04.479 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:11:04.537 [info] > git status -z -uall [33ms]
2025-05-24 13:11:04.538 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:11:04.871 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 13:11:09.592 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:11:09.592 [info] > git config --get commit.template [29ms]
2025-05-24 13:11:09.594 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:11:09.658 [info] > git status -z -uall [34ms]
2025-05-24 13:11:09.659 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:11:09.987 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:11:14.714 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:11:14.716 [info] > git config --get commit.template [27ms]
2025-05-24 13:11:14.716 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:11:14.774 [info] > git status -z -uall [26ms]
2025-05-24 13:11:14.775 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:11:15.103 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:11:19.835 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:11:19.835 [info] > git config --get commit.template [29ms]
2025-05-24 13:11:19.836 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:11:19.895 [info] > git status -z -uall [30ms]
2025-05-24 13:11:19.896 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 13:11:20.304 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [55ms]
2025-05-24 13:11:24.975 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:11:24.975 [info] > git config --get commit.template [40ms]
2025-05-24 13:11:24.978 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 13:11:25.070 [info] > git status -z -uall [40ms]
2025-05-24 13:11:25.073 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 13:11:25.404 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 13:11:30.183 [info] > git config --get commit.template [72ms]
2025-05-24 13:11:30.215 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:11:30.217 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:11:30.283 [info] > git status -z -uall [34ms]
2025-05-24 13:11:30.283 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 13:11:30.616 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:11:36.456 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:11:36.457 [info] > git config --get commit.template [25ms]
2025-05-24 13:11:36.459 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 13:11:36.512 [info] > git status -z -uall [30ms]
2025-05-24 13:11:36.513 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 13:11:36.848 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:11:41.566 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:11:41.566 [info] > git config --get commit.template [25ms]
2025-05-24 13:11:41.567 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:11:41.619 [info] > git status -z -uall [27ms]
2025-05-24 13:11:41.620 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:11:41.946 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:11:46.668 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:11:46.669 [info] > git config --get commit.template [24ms]
2025-05-24 13:11:46.670 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:11:46.721 [info] > git status -z -uall [26ms]
2025-05-24 13:11:46.722 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 13:11:47.047 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:11:51.791 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:11:51.792 [info] > git config --get commit.template [35ms]
2025-05-24 13:11:51.793 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:11:51.851 [info] > git status -z -uall [29ms]
2025-05-24 13:11:51.852 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:11:52.184 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:11:56.882 [info] > git config --get commit.template [0ms]
2025-05-24 13:11:56.913 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:11:56.915 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 13:11:56.961 [info] > git status -z -uall [21ms]
2025-05-24 13:11:56.965 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-05-24 13:11:57.297 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 13:12:02.738 [info] > git config --get commit.template [2ms]
2025-05-24 13:12:02.770 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:12:02.772 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:12:02.837 [info] > git status -z -uall [40ms]
2025-05-24 13:12:02.838 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 13:12:03.162 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:12:10.872 [info] > git fetch [3ms]
2025-05-24 13:12:10.921 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:12:10.922 [info] > git config --get commit.template [27ms]
2025-05-24 13:12:10.923 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:12:11.041 [info] > git status -z -uall [91ms]
2025-05-24 13:12:11.041 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [72ms]
2025-05-24 13:12:11.366 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:12:12.479 [info] > git ls-files --stage -- client/src/pages/auth/LogoutPage.tsx [1ms]
2025-05-24 13:12:12.509 [info] > git cat-file -s d36ad335f7e5bcacb98e533de5ae0cc630b6b82d [1ms]
2025-05-24 13:12:12.715 [info] > git show --textconv :client/src/pages/auth/LogoutPage.tsx [2ms]
2025-05-24 13:12:13.768 [info] > git config --get commit.template [4ms]
2025-05-24 13:12:13.811 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:12:13.813 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:12:13.870 [info] > git status -z -uall [28ms]
2025-05-24 13:12:13.872 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:12:14.200 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:12:18.925 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:12:18.926 [info] > git config --get commit.template [25ms]
2025-05-24 13:12:18.926 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:12:18.976 [info] > git status -z -uall [25ms]
2025-05-24 13:12:18.977 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:12:19.310 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:14:15.743 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:14:15.744 [info] > git config --get commit.template [29ms]
2025-05-24 13:14:15.744 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:14:15.796 [info] > git status -z -uall [25ms]
2025-05-24 13:14:15.803 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-05-24 13:14:16.130 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:14:16.604 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:14:16.991 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/context/RestaurantStatusContext.tsx [337ms]
2025-05-24 13:14:16.991 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/Checkout.tsx [313ms]
2025-05-24 13:14:16.991 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/auth/AuthPage.tsx [283ms]
2025-05-24 13:14:16.991 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/auth/LogoutPage.tsx [238ms]
2025-05-24 13:14:16.991 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:cookies.txt [210ms]
2025-05-24 13:14:16.991 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:server/admin-api.ts [183ms]
2025-05-24 13:14:16.991 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:server/auth.ts [157ms]
2025-05-24 13:14:16.991 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:server/routes.ts [132ms]
2025-05-24 13:14:16.991 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/components/NotificationDropdown.tsx [107ms]
2025-05-24 13:14:16.991 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/context/NotificationContext.tsx [84ms]
2025-05-24 13:14:16.991 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/hooks/useAudioNotification.ts [61ms]
2025-05-24 13:14:16.991 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:cookies2.txt [35ms]
2025-05-24 13:14:16.991 [info] > git check-ignore -v -z --stdin [11ms]
2025-05-24 13:14:17.170 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [57ms]
2025-05-24 13:14:17.215 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [143ms]
2025-05-24 13:14:17.263 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [231ms]
2025-05-24 13:14:17.316 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [155ms]
2025-05-24 13:14:18.054 [info] > git hash-object -w --stdin [73ms]
2025-05-24 13:14:18.055 [info] > git hash-object -w --stdin [99ms]
2025-05-24 13:14:18.056 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- server/routes.ts [129ms]
2025-05-24 13:14:18.056 [info] > git hash-object -w --stdin [154ms]
2025-05-24 13:14:18.056 [info] > git hash-object -w --stdin [178ms]
2025-05-24 13:14:18.057 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- server/admin-api.ts [207ms]
2025-05-24 13:14:18.057 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- server/auth.ts [234ms]
2025-05-24 13:14:18.057 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- cookies.txt [263ms]
2025-05-24 13:14:18.057 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/auth/LogoutPage.tsx [300ms]
2025-05-24 13:14:18.057 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/Checkout.tsx [331ms]
2025-05-24 13:14:18.057 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/auth/AuthPage.tsx [363ms]
2025-05-24 13:14:18.058 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/context/RestaurantStatusContext.tsx [664ms]
2025-05-24 13:14:18.165 [info] > git config --get commit.template [28ms]
2025-05-24 13:14:18.165 [info] > git config --get commit.template [51ms]
2025-05-24 13:14:18.166 [info] > git config --get commit.template [76ms]
2025-05-24 13:14:18.166 [info] > git config --get commit.template [4ms]
2025-05-24 13:14:18.205 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:14:18.234 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:14:18.261 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:14:18.291 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:14:18.298 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-05-24 13:14:18.353 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [92ms]
2025-05-24 13:14:18.386 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [151ms]
2025-05-24 13:14:18.411 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [206ms]
2025-05-24 13:14:18.523 [info] > git status -z -uall [201ms]
2025-05-24 13:14:18.523 [info] > git hash-object -w --stdin [112ms]
2025-05-24 13:14:18.525 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [172ms] (cancelled)
2025-05-24 13:14:18.564 [info] > git hash-object -w --stdin [179ms]
2025-05-24 13:14:18.659 [info] > git hash-object -w --stdin [223ms]
2025-05-24 13:14:18.740 [info] > git config --get commit.template [119ms]
2025-05-24 13:14:18.740 [info] > git hash-object -w --stdin [176ms]
2025-05-24 13:14:18.790 [info] > git config --get commit.template [132ms]
2025-05-24 13:14:18.824 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:14:18.865 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:14:18.867 [info] > git config --get commit.template [82ms]
2025-05-24 13:14:18.867 [info] > git config --get commit.template [131ms]
2025-05-24 13:14:18.917 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:14:18.927 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [103ms]
2025-05-24 13:14:18.977 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:14:19.012 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [96ms]
2025-05-24 13:14:19.049 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [183ms]
2025-05-24 13:14:19.088 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 266a2ace2b033bb01999d0049505c3d16557cc40 [40ms]
2025-05-24 13:14:19.089 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [82ms]
2025-05-24 13:14:19.165 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 f45b58453d90a2f7c53517f142627fc254b4a369 [189ms]
2025-05-24 13:14:19.170 [info] > git status -z -uall [44ms]
2025-05-24 13:14:19.170 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 c229f1549b6c43cc25ca236283e8433e774dbad9 [93ms]
2025-05-24 13:14:19.181 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [16ms]
2025-05-24 13:14:19.236 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 25eda2e8a0a86065a3e4a1d5edc663e67f7b8c39 [11ms]
2025-05-24 13:14:19.541 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [9ms]
2025-05-24 13:14:20.854 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:14:20.854 [info] > git config --get commit.template [26ms]
2025-05-24 13:14:20.855 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:14:20.905 [info] > git status -z -uall [25ms]
2025-05-24 13:14:20.906 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:14:21.249 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:14:25.958 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:14:25.959 [info] > git config --get commit.template [24ms]
2025-05-24 13:14:25.960 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:14:26.006 [info] > git status -z -uall [27ms]
2025-05-24 13:14:26.006 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 13:14:26.336 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:14:31.078 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:14:31.079 [info] > git config --get commit.template [38ms]
2025-05-24 13:14:31.080 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:14:31.132 [info] > git status -z -uall [26ms]
2025-05-24 13:14:31.134 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 13:14:31.460 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:14:31.859 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:14:32.326 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/context/RestaurantStatusContext.tsx [413ms]
2025-05-24 13:14:32.326 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/Checkout.tsx [385ms]
2025-05-24 13:14:32.326 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/auth/AuthPage.tsx [354ms]
2025-05-24 13:14:32.326 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/auth/LogoutPage.tsx [324ms]
2025-05-24 13:14:32.326 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/manager/ManagerLayout.tsx [297ms]
2025-05-24 13:14:32.326 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:cookies.txt [268ms]
2025-05-24 13:14:32.326 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:server/admin-api.ts [239ms]
2025-05-24 13:14:32.326 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:server/auth.ts [215ms]
2025-05-24 13:14:32.326 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:server/routes.ts [179ms]
2025-05-24 13:14:32.327 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/components/NotificationDropdown.tsx [149ms]
2025-05-24 13:14:32.327 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/context/NotificationContext.tsx [119ms]
2025-05-24 13:14:32.327 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/hooks/useAudioNotification.ts [93ms]
2025-05-24 13:14:32.327 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:cookies2.txt [60ms]
2025-05-24 13:14:32.446 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [26ms]
2025-05-24 13:14:32.473 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [82ms]
2025-05-24 13:14:32.502 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [137ms]
2025-05-24 13:14:32.538 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [96ms]
2025-05-24 13:14:32.972 [info] > git hash-object -w --stdin [56ms]
2025-05-24 13:14:32.973 [info] > git hash-object -w --stdin [82ms]
2025-05-24 13:14:32.973 [info] > git hash-object -w --stdin [104ms]
2025-05-24 13:14:32.973 [info] > git hash-object -w --stdin [129ms]
2025-05-24 13:14:32.974 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- server/routes.ts [152ms]
2025-05-24 13:14:32.974 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- server/auth.ts [180ms]
2025-05-24 13:14:32.974 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- cookies.txt [211ms]
2025-05-24 13:14:32.974 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- server/admin-api.ts [236ms]
2025-05-24 13:14:32.974 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/manager/ManagerLayout.tsx [267ms]
2025-05-24 13:14:32.975 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/auth/LogoutPage.tsx [291ms]
2025-05-24 13:14:32.975 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/auth/AuthPage.tsx [317ms]
2025-05-24 13:14:32.975 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/Checkout.tsx [342ms]
2025-05-24 13:14:32.975 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/context/RestaurantStatusContext.tsx [371ms]
2025-05-24 13:14:33.086 [info] > git config --get commit.template [13ms]
2025-05-24 13:14:33.086 [info] > git config --get commit.template [35ms]
2025-05-24 13:14:33.086 [info] > git config --get commit.template [61ms]
2025-05-24 13:14:33.086 [info] > git config --get commit.template [86ms]
2025-05-24 13:14:33.111 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:14:33.137 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:14:33.170 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:14:33.204 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:14:33.208 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [38ms]
2025-05-24 13:14:33.241 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [104ms]
2025-05-24 13:14:33.274 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [163ms]
2025-05-24 13:14:33.311 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [108ms]
2025-05-24 13:14:33.379 [info] > git status -z -uall [37ms]
2025-05-24 13:14:33.379 [info] > git hash-object -w --stdin [72ms]
2025-05-24 13:14:33.380 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms] (cancelled)
2025-05-24 13:14:33.415 [info] > git hash-object -w --stdin [174ms]
2025-05-24 13:14:33.417 [info] > git hash-object -w --stdin [143ms]
2025-05-24 13:14:33.521 [info] > git config --get commit.template [6ms]
2025-05-24 13:14:33.521 [info] > git config --get commit.template [34ms]
2025-05-24 13:14:33.521 [info] > git config --get commit.template [65ms]
2025-05-24 13:14:33.521 [info] > git hash-object -w --stdin [106ms]
2025-05-24 13:14:33.573 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:14:33.573 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:14:33.633 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:14:33.636 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [36ms]
2025-05-24 13:14:33.667 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [94ms]
2025-05-24 13:14:33.700 [info] > git config --get commit.template [153ms]
2025-05-24 13:14:33.740 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:14:33.743 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 c229f1549b6c43cc25ca236283e8433e774dbad9 [77ms]
2025-05-24 13:14:33.743 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [110ms]
2025-05-24 13:14:33.788 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [49ms]
2025-05-24 13:14:33.847 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 f45b58453d90a2f7c53517f142627fc254b4a369 [147ms]
2025-05-24 13:14:33.850 [info] > git status -z -uall [35ms]
2025-05-24 13:14:33.851 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 266a2ace2b033bb01999d0049505c3d16557cc40 [66ms]
2025-05-24 13:14:33.853 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-05-24 13:14:33.950 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 25eda2e8a0a86065a3e4a1d5edc663e67f7b8c39 [72ms]
2025-05-24 13:14:34.231 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [50ms]
2025-05-24 13:14:36.189 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:14:36.191 [info] > git config --get commit.template [32ms]
2025-05-24 13:14:36.192 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 13:14:36.243 [info] > git status -z -uall [24ms]
2025-05-24 13:14:36.244 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:14:36.572 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:14:41.299 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:14:41.300 [info] > git config --get commit.template [27ms]
2025-05-24 13:14:41.300 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:14:41.369 [info] > git status -z -uall [39ms]
2025-05-24 13:14:41.371 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:14:41.697 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:14:46.758 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:14:46.759 [info] > git config --get commit.template [77ms]
2025-05-24 13:14:46.764 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-05-24 13:14:46.924 [info] > git status -z -uall [119ms]
2025-05-24 13:14:46.927 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [94ms]
2025-05-24 13:14:47.292 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:14:47.757 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:14:48.316 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/context/RestaurantStatusContext.tsx [495ms]
2025-05-24 13:14:48.316 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/Checkout.tsx [457ms]
2025-05-24 13:14:48.316 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/auth/AuthPage.tsx [423ms]
2025-05-24 13:14:48.316 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/auth/LogoutPage.tsx [385ms]
2025-05-24 13:14:48.316 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/manager/ManagerLayout.tsx [356ms]
2025-05-24 13:14:48.316 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:cookies.txt [324ms]
2025-05-24 13:14:48.316 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:server/admin-api.ts [289ms]
2025-05-24 13:14:48.316 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:server/auth.ts [252ms]
2025-05-24 13:14:48.316 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:server/routes.ts [213ms]
2025-05-24 13:14:48.316 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/components/NotificationDropdown.tsx [183ms]
2025-05-24 13:14:48.316 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/context/NotificationContext.tsx [144ms]
2025-05-24 13:14:48.316 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/hooks/useAudioNotification.ts [107ms]
2025-05-24 13:14:48.317 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:cookies2.txt [72ms]
2025-05-24 13:14:48.410 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [34ms]
2025-05-24 13:14:48.460 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [113ms]
2025-05-24 13:14:48.545 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [138ms]
2025-05-24 13:14:48.577 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [43ms]
2025-05-24 13:14:49.013 [info] > git hash-object -w --stdin [29ms]
2025-05-24 13:14:49.014 [info] > git hash-object -w --stdin [54ms]
2025-05-24 13:14:49.014 [info] > git hash-object -w --stdin [88ms]
2025-05-24 13:14:49.015 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- server/routes.ts [121ms]
2025-05-24 13:14:49.015 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- server/auth.ts [154ms]
2025-05-24 13:14:49.015 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- server/admin-api.ts [186ms]
2025-05-24 13:14:49.015 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/auth/AuthPage.tsx [216ms]
2025-05-24 13:14:49.015 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/manager/ManagerLayout.tsx [248ms]
2025-05-24 13:14:49.015 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- cookies.txt [277ms]
2025-05-24 13:14:49.016 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/auth/LogoutPage.tsx [308ms]
2025-05-24 13:14:49.016 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/Checkout.tsx [346ms]
2025-05-24 13:14:49.016 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/context/RestaurantStatusContext.tsx [375ms]
2025-05-24 13:14:49.087 [info] > git hash-object -w --stdin [78ms]
2025-05-24 13:14:49.116 [info] > git config --get commit.template [50ms]
2025-05-24 13:14:49.116 [info] > git config --get commit.template [75ms]
2025-05-24 13:14:49.118 [info] > git config --get commit.template [31ms]
2025-05-24 13:14:49.152 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:14:49.181 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:14:49.207 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:14:49.235 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:14:49.239 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [58ms]
2025-05-24 13:14:49.268 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [116ms]
2025-05-24 13:14:49.294 [info] > git config --get commit.template [179ms]
2025-05-24 13:14:49.296 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [61ms]
2025-05-24 13:14:49.386 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [179ms]
2025-05-24 13:14:49.443 [info] > git status -z -uall [122ms]
2025-05-24 13:14:49.443 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [57ms]
2025-05-24 13:14:49.472 [info] > git hash-object -w --stdin [178ms]
2025-05-24 13:14:49.473 [info] > git hash-object -w --stdin [205ms]
2025-05-24 13:14:49.529 [info] > git hash-object -w --stdin [89ms]
2025-05-24 13:14:49.561 [info] > git config --get commit.template [33ms]
2025-05-24 13:14:49.562 [info] > git config --get commit.template [59ms]
2025-05-24 13:14:49.562 [info] > git hash-object -w --stdin [90ms]
2025-05-24 13:14:49.598 [info] > git config --get commit.template [37ms]
2025-05-24 13:14:49.599 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:14:49.656 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:14:49.657 [info] > git config --get commit.template [60ms]
2025-05-24 13:14:49.700 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:14:49.703 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [48ms]
2025-05-24 13:14:49.730 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [102ms]
2025-05-24 13:14:49.826 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:14:49.828 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 266a2ace2b033bb01999d0049505c3d16557cc40 [61ms]
2025-05-24 13:14:49.829 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 c229f1549b6c43cc25ca236283e8433e774dbad9 [99ms]
2025-05-24 13:14:49.829 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [129ms]
2025-05-24 13:14:49.870 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [44ms]
2025-05-24 13:14:49.956 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [160ms]
2025-05-24 13:14:49.959 [info] > git status -z -uall [49ms]
2025-05-24 13:14:49.959 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 25eda2e8a0a86065a3e4a1d5edc663e67f7b8c39 [89ms]
2025-05-24 13:14:49.960 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 13:14:49.989 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 f45b58453d90a2f7c53517f142627fc254b4a369 [3ms]
2025-05-24 13:14:50.328 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 13:14:51.992 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:14:51.993 [info] > git config --get commit.template [27ms]
2025-05-24 13:14:51.995 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 13:14:52.058 [info] > git status -z -uall [27ms]
2025-05-24 13:14:52.060 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 13:14:52.418 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:14:57.089 [info] > git config --get commit.template [0ms]
2025-05-24 13:14:57.113 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:14:57.114 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:14:57.172 [info] > git status -z -uall [29ms]
2025-05-24 13:14:57.174 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:14:57.510 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:15:02.258 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:15:02.259 [info] > git config --get commit.template [40ms]
2025-05-24 13:15:02.260 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:15:02.361 [info] > git status -z -uall [47ms]
2025-05-24 13:15:02.365 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-05-24 13:15:02.713 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 13:15:03.171 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:15:03.667 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/context/RestaurantStatusContext.tsx [446ms]
2025-05-24 13:15:03.667 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/Checkout.tsx [414ms]
2025-05-24 13:15:03.667 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/auth/AuthPage.tsx [380ms]
2025-05-24 13:15:03.667 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/auth/LogoutPage.tsx [354ms]
2025-05-24 13:15:03.667 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/manager/ManagerLayout.tsx [316ms]
2025-05-24 13:15:03.667 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:cookies.txt [285ms]
2025-05-24 13:15:03.667 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:server/admin-api.ts [257ms]
2025-05-24 13:15:03.667 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:server/auth.ts [219ms]
2025-05-24 13:15:03.667 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:server/routes.ts [188ms]
2025-05-24 13:15:03.667 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/components/NotificationDropdown.tsx [158ms]
2025-05-24 13:15:03.667 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/context/NotificationContext.tsx [100ms]
2025-05-24 13:15:03.668 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/hooks/useAudioNotification.ts [59ms]
2025-05-24 13:15:03.668 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:cookies2.txt [10ms]
2025-05-24 13:15:03.786 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [29ms]
2025-05-24 13:15:03.817 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [88ms]
2025-05-24 13:15:03.847 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [145ms]
2025-05-24 13:15:03.886 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [102ms]
2025-05-24 13:15:04.333 [info] > git hash-object -w --stdin [92ms]
2025-05-24 13:15:04.333 [info] > git hash-object -w --stdin [117ms]
2025-05-24 13:15:04.334 [info] > git hash-object -w --stdin [147ms]
2025-05-24 13:15:04.334 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- server/admin-api.ts [174ms]
2025-05-24 13:15:04.334 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- server/routes.ts [200ms]
2025-05-24 13:15:04.334 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- server/auth.ts [228ms]
2025-05-24 13:15:04.335 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/manager/ManagerLayout.tsx [254ms]
2025-05-24 13:15:04.335 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/Checkout.tsx [278ms]
2025-05-24 13:15:04.336 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- cookies.txt [305ms]
2025-05-24 13:15:04.336 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/auth/LogoutPage.tsx [339ms]
2025-05-24 13:15:04.336 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/auth/AuthPage.tsx [364ms]
2025-05-24 13:15:04.336 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/context/RestaurantStatusContext.tsx [388ms]
2025-05-24 13:15:04.433 [info] > git hash-object -w --stdin [167ms]
2025-05-24 13:15:04.467 [info] > git config --get commit.template [35ms]
2025-05-24 13:15:04.467 [info] > git config --get commit.template [62ms]
2025-05-24 13:15:04.467 [info] > git config --get commit.template [92ms]
2025-05-24 13:15:04.470 [info] > git config --get commit.template [4ms]
2025-05-24 13:15:04.495 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:15:04.522 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:15:04.547 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:15:04.579 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:15:04.581 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [34ms]
2025-05-24 13:15:04.613 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [92ms]
2025-05-24 13:15:04.639 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [144ms]
2025-05-24 13:15:04.678 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [100ms]
2025-05-24 13:15:04.731 [info] > git status -z -uall [28ms]
2025-05-24 13:15:04.731 [info] > git hash-object -w --stdin [63ms]
2025-05-24 13:15:04.732 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms] (cancelled)
2025-05-24 13:15:04.758 [info] > git hash-object -w --stdin [118ms]
2025-05-24 13:15:04.758 [info] > git hash-object -w --stdin [145ms]
2025-05-24 13:15:04.848 [info] > git config --get commit.template [27ms]
2025-05-24 13:15:04.849 [info] > git config --get commit.template [62ms]
2025-05-24 13:15:04.849 [info] > git hash-object -w --stdin [92ms]
2025-05-24 13:15:04.879 [info] > git config --get commit.template [33ms]
2025-05-24 13:15:04.912 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:15:04.946 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:15:04.979 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:15:04.984 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [10ms]
2025-05-24 13:15:05.010 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [64ms]
2025-05-24 13:15:05.036 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [124ms]
2025-05-24 13:15:05.067 [info] > git config --get commit.template [189ms]
2025-05-24 13:15:05.090 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:15:05.092 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 c229f1549b6c43cc25ca236283e8433e774dbad9 [56ms]
2025-05-24 13:15:05.093 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 25eda2e8a0a86065a3e4a1d5edc663e67f7b8c39 [83ms]
2025-05-24 13:15:05.093 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 13:15:05.143 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 266a2ace2b033bb01999d0049505c3d16557cc40 [77ms]
2025-05-24 13:15:05.144 [info] > git status -z -uall [26ms]
2025-05-24 13:15:05.145 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:15:05.173 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 f45b58453d90a2f7c53517f142627fc254b4a369 [2ms]
2025-05-24 13:15:05.478 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-05-24 13:15:07.436 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:15:07.436 [info] > git config --get commit.template [41ms]
2025-05-24 13:15:07.439 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 13:15:07.495 [info] > git status -z -uall [24ms]
2025-05-24 13:15:07.497 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:15:07.827 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:15:11.072 [info] > git fetch [4ms]
2025-05-24 13:15:11.121 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:15:11.122 [info] > git config --get commit.template [24ms]
2025-05-24 13:15:11.123 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:15:11.183 [info] > git status -z -uall [34ms]
2025-05-24 13:15:11.185 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:15:11.516 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:15:12.547 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:15:12.547 [info] > git config --get commit.template [24ms]
2025-05-24 13:15:12.548 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:15:12.617 [info] > git status -z -uall [42ms]
2025-05-24 13:15:12.618 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:15:12.958 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 13:15:17.669 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:15:17.669 [info] > git config --get commit.template [22ms]
2025-05-24 13:15:17.670 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:15:17.732 [info] > git status -z -uall [34ms]
2025-05-24 13:15:17.733 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 13:15:18.124 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 13:15:18.513 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:15:19.013 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/context/RestaurantStatusContext.tsx [447ms]
2025-05-24 13:15:19.013 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/Checkout.tsx [422ms]
2025-05-24 13:15:19.013 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/auth/AuthPage.tsx [393ms]
2025-05-24 13:15:19.013 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/auth/LogoutPage.tsx [367ms]
2025-05-24 13:15:19.013 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/manager/ManagerLayout.tsx [343ms]
2025-05-24 13:15:19.014 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/manager/ManagerPage.tsx [315ms]
2025-05-24 13:15:19.014 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:cookies.txt [291ms]
2025-05-24 13:15:19.014 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:server/admin-api.ts [261ms]
2025-05-24 13:15:19.015 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:server/auth.ts [237ms]
2025-05-24 13:15:19.015 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:server/routes.ts [184ms]
2025-05-24 13:15:19.015 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/components/NotificationDropdown.tsx [133ms]
2025-05-24 13:15:19.015 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/context/NotificationContext.tsx [97ms]
2025-05-24 13:15:19.015 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/hooks/useAudioNotification.ts [64ms]
2025-05-24 13:15:19.162 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:cookies2.txt [155ms]
2025-05-24 13:15:19.163 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [52ms]
2025-05-24 13:15:19.206 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [141ms]
2025-05-24 13:15:19.316 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [159ms]
2025-05-24 13:15:19.358 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [47ms]
2025-05-24 13:15:19.939 [info] > git hash-object -w --stdin [7ms]
2025-05-24 13:15:19.940 [info] > git hash-object -w --stdin [38ms]
2025-05-24 13:15:19.943 [info] > git hash-object -w --stdin [82ms]
2025-05-24 13:15:19.944 [info] > git hash-object -w --stdin [115ms]
2025-05-24 13:15:19.944 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- server/routes.ts [145ms]
2025-05-24 13:15:19.945 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- server/auth.ts [178ms]
2025-05-24 13:15:19.945 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- server/admin-api.ts [204ms]
2025-05-24 13:15:19.945 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- cookies.txt [236ms]
2025-05-24 13:15:19.945 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/manager/ManagerPage.tsx [274ms]
2025-05-24 13:15:19.945 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/manager/ManagerLayout.tsx [313ms]
2025-05-24 13:15:19.946 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/auth/LogoutPage.tsx [360ms]
2025-05-24 13:15:19.946 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/Checkout.tsx [402ms]
2025-05-24 13:15:19.946 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/auth/AuthPage.tsx [448ms]
2025-05-24 13:15:19.946 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/context/RestaurantStatusContext.tsx [493ms]
2025-05-24 13:15:20.101 [info] > git config --get commit.template [3ms]
2025-05-24 13:15:20.101 [info] > git config --get commit.template [35ms]
2025-05-24 13:15:20.101 [info] > git config --get commit.template [67ms]
2025-05-24 13:15:20.101 [info] > git config --get commit.template [115ms]
2025-05-24 13:15:20.133 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:15:20.164 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:15:20.205 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:15:20.235 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:15:20.241 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [37ms]
2025-05-24 13:15:20.278 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [114ms]
2025-05-24 13:15:20.308 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [175ms]
2025-05-24 13:15:20.478 [info] > git hash-object -w --stdin [200ms]
2025-05-24 13:15:20.479 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [244ms]
2025-05-24 13:15:20.541 [info] > git hash-object -w --stdin [206ms]
2025-05-24 13:15:20.541 [info] > git hash-object -w --stdin [233ms]
2025-05-24 13:15:20.603 [info] > git config --get commit.template [66ms]
2025-05-24 13:15:20.603 [info] > git hash-object -w --stdin [99ms]
2025-05-24 13:15:20.642 [info] > git config --get commit.template [40ms]
2025-05-24 13:15:20.642 [info] > git config --get commit.template [72ms]
2025-05-24 13:15:20.674 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:15:20.683 [info] > git config --get commit.template [47ms]
2025-05-24 13:15:20.710 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:15:20.742 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:15:20.799 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [125ms]
2025-05-24 13:15:20.854 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:15:20.915 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [173ms]
2025-05-24 13:15:20.943 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [233ms]
2025-05-24 13:15:20.973 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 266a2ace2b033bb01999d0049505c3d16557cc40 [30ms]
2025-05-24 13:15:20.973 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [119ms]
2025-05-24 13:15:21.033 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 c229f1549b6c43cc25ca236283e8433e774dbad9 [209ms]
2025-05-24 13:15:21.052 [info] > git status -z -uall [52ms]
2025-05-24 13:15:21.052 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 f45b58453d90a2f7c53517f142627fc254b4a369 [83ms]
2025-05-24 13:15:21.061 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [28ms]
2025-05-24 13:15:21.099 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 25eda2e8a0a86065a3e4a1d5edc663e67f7b8c39 [4ms]
2025-05-24 13:15:21.435 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 13:15:22.787 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:15:22.788 [info] > git config --get commit.template [27ms]
2025-05-24 13:15:22.789 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:15:22.845 [info] > git status -z -uall [27ms]
2025-05-24 13:15:22.846 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 13:15:23.174 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:15:27.907 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:15:27.907 [info] > git config --get commit.template [33ms]
2025-05-24 13:15:27.909 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:15:27.966 [info] > git status -z -uall [30ms]
2025-05-24 13:15:27.967 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 13:15:28.297 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 13:15:33.021 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:15:33.023 [info] > git config --get commit.template [28ms]
2025-05-24 13:15:33.024 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 13:15:33.081 [info] > git status -z -uall [28ms]
2025-05-24 13:15:33.081 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 13:15:33.417 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 13:15:33.831 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:15:34.246 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/context/RestaurantStatusContext.tsx [368ms]
2025-05-24 13:15:34.246 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/Checkout.tsx [331ms]
2025-05-24 13:15:34.246 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/auth/AuthPage.tsx [302ms]
2025-05-24 13:15:34.246 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/auth/LogoutPage.tsx [277ms]
2025-05-24 13:15:34.246 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/manager/ManagerLayout.tsx [258ms]
2025-05-24 13:15:34.246 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/manager/ManagerPage.tsx [230ms]
2025-05-24 13:15:34.246 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:cookies.txt [202ms]
2025-05-24 13:15:34.246 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:server/admin-api.ts [167ms]
2025-05-24 13:15:34.246 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:server/auth.ts [135ms]
2025-05-24 13:15:34.246 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:server/routes.ts [110ms]
2025-05-24 13:15:34.246 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/components/NotificationDropdown.tsx [84ms]
2025-05-24 13:15:34.246 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/context/NotificationContext.tsx [57ms]
2025-05-24 13:15:34.246 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/hooks/useAudioNotification.ts [32ms]
2025-05-24 13:15:34.246 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:cookies2.txt [5ms]
2025-05-24 13:15:34.371 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [55ms]
2025-05-24 13:15:34.427 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [143ms]
2025-05-24 13:15:34.500 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [132ms]
2025-05-24 13:15:34.546 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [53ms]
2025-05-24 13:15:35.166 [info] > git hash-object -w --stdin [48ms]
2025-05-24 13:15:35.166 [info] > git hash-object -w --stdin [89ms]
2025-05-24 13:15:35.166 [info] > git hash-object -w --stdin [127ms]
2025-05-24 13:15:35.167 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- server/routes.ts [166ms]
2025-05-24 13:15:35.167 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- server/auth.ts [195ms]
2025-05-24 13:15:35.167 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- server/admin-api.ts [236ms]
2025-05-24 13:15:35.167 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- cookies.txt [270ms]
2025-05-24 13:15:35.168 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/manager/ManagerPage.tsx [329ms]
2025-05-24 13:15:35.168 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/context/RestaurantStatusContext.tsx [382ms]
2025-05-24 13:15:35.168 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/auth/LogoutPage.tsx [428ms]
2025-05-24 13:15:35.168 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/manager/ManagerLayout.tsx [475ms]
2025-05-24 13:15:35.168 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/Checkout.tsx [505ms]
2025-05-24 13:15:35.168 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/auth/AuthPage.tsx [542ms]
2025-05-24 13:15:35.321 [info] > git hash-object -w --stdin [161ms]
2025-05-24 13:15:35.356 [info] > git config --get commit.template [35ms]
2025-05-24 13:15:35.357 [info] > git config --get commit.template [87ms]
2025-05-24 13:15:35.357 [info] > git config --get commit.template [130ms]
2025-05-24 13:15:35.358 [info] > git config --get commit.template [3ms]
2025-05-24 13:15:35.386 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:15:35.417 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:15:35.451 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:15:35.488 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:15:35.490 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [74ms]
2025-05-24 13:15:35.523 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [137ms]
2025-05-24 13:15:35.564 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [76ms]
2025-05-24 13:15:35.640 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [189ms]
2025-05-24 13:15:35.676 [info] > git status -z -uall [71ms]
2025-05-24 13:15:35.676 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [36ms]
2025-05-24 13:15:35.735 [info] > git hash-object -w --stdin [212ms]
2025-05-24 13:15:35.738 [info] > git hash-object -w --stdin [176ms]
2025-05-24 13:15:35.797 [info] > git hash-object -w --stdin [124ms]
2025-05-24 13:15:35.838 [info] > git config --get commit.template [42ms]
2025-05-24 13:15:35.838 [info] > git config --get commit.template [69ms]
2025-05-24 13:15:35.838 [info] > git hash-object -w --stdin [104ms]
2025-05-24 13:15:35.866 [info] > git config --get commit.template [29ms]
2025-05-24 13:15:35.868 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:15:35.921 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:15:36.045 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:15:36.047 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [32ms]
2025-05-24 13:15:36.048 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [127ms]
2025-05-24 13:15:36.074 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [179ms]
2025-05-24 13:15:36.100 [info] > git config --get commit.template [234ms]
2025-05-24 13:15:36.131 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:15:36.132 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 c229f1549b6c43cc25ca236283e8433e774dbad9 [32ms]
2025-05-24 13:15:36.132 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 266a2ace2b033bb01999d0049505c3d16557cc40 [58ms]
2025-05-24 13:15:36.132 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [88ms]
2025-05-24 13:15:36.168 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [38ms]
2025-05-24 13:15:36.237 [info] > git status -z -uall [33ms]
2025-05-24 13:15:36.238 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 25eda2e8a0a86065a3e4a1d5edc663e67f7b8c39 [76ms]
2025-05-24 13:15:36.239 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 13:15:36.282 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 f45b58453d90a2f7c53517f142627fc254b4a369 [7ms]
2025-05-24 13:15:36.628 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 13:15:38.140 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:15:38.140 [info] > git config --get commit.template [33ms]
2025-05-24 13:15:38.141 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:15:38.188 [info] > git status -z -uall [21ms]
2025-05-24 13:15:38.189 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 13:15:38.517 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:18:08.827 [info] > git config --get commit.template [2ms]
2025-05-24 13:18:08.850 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:18:08.855 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-05-24 13:18:08.907 [info] > git status -z -uall [29ms]
2025-05-24 13:18:08.908 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:18:09.292 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [57ms]
2025-05-24 13:18:09.704 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:18:10.332 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/App.tsx [572ms]
2025-05-24 13:18:10.332 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/context/RestaurantStatusContext.tsx [524ms]
2025-05-24 13:18:10.332 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/Checkout.tsx [497ms]
2025-05-24 13:18:10.332 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/auth/AuthPage.tsx [469ms]
2025-05-24 13:18:10.332 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/auth/LogoutPage.tsx [438ms]
2025-05-24 13:18:10.332 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/manager/ManagerLayout.tsx [408ms]
2025-05-24 13:18:10.332 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/manager/ManagerPage.tsx [381ms]
2025-05-24 13:18:10.332 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:cookies.txt [342ms]
2025-05-24 13:18:10.332 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:server/admin-api.ts [294ms]
2025-05-24 13:18:10.332 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:server/auth.ts [267ms]
2025-05-24 13:18:10.332 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:server/routes.ts [231ms]
2025-05-24 13:18:10.332 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/components/NotificationDropdown.tsx [200ms]
2025-05-24 13:18:10.332 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/context/NotificationContext.tsx [167ms]
2025-05-24 13:18:10.332 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/hooks/useAudioNotification.ts [130ms]
2025-05-24 13:18:10.332 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:cookies2.txt [85ms]
2025-05-24 13:18:10.332 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:public/sounds/README.md [45ms]
2025-05-24 13:18:10.333 [info] > git check-ignore -v -z --stdin [10ms]
2025-05-24 13:18:10.556 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [38ms]
2025-05-24 13:18:10.590 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [117ms]
2025-05-24 13:18:10.648 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [215ms]
2025-05-24 13:18:10.691 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [320ms]
2025-05-24 13:18:10.727 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [176ms]
2025-05-24 13:18:11.481 [info] > git check-ignore -v -z --stdin [584ms]
2025-05-24 13:18:11.481 [info] > git hash-object -w --stdin [27ms]
2025-05-24 13:18:11.482 [info] > git hash-object -w --stdin [56ms]
2025-05-24 13:18:11.482 [info] > git hash-object -w --stdin [92ms]
2025-05-24 13:18:11.482 [info] > git hash-object -w --stdin [120ms]
2025-05-24 13:18:11.483 [info] > git hash-object -w --stdin [155ms]
2025-05-24 13:18:11.483 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- server/routes.ts [203ms]
2025-05-24 13:18:11.483 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- server/auth.ts [238ms]
2025-05-24 13:18:11.483 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- server/admin-api.ts [280ms]
2025-05-24 13:18:11.484 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- cookies.txt [326ms]
2025-05-24 13:18:11.484 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/manager/ManagerLayout.tsx [376ms]
2025-05-24 13:18:11.484 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/auth/LogoutPage.tsx [419ms]
2025-05-24 13:18:11.484 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/auth/AuthPage.tsx [454ms]
2025-05-24 13:18:11.484 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/context/RestaurantStatusContext.tsx [498ms]
2025-05-24 13:18:11.484 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/Checkout.tsx [533ms]
2025-05-24 13:18:11.485 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/App.tsx [560ms]
2025-05-24 13:18:11.610 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/manager/ManagerPage.tsx [133ms]
2025-05-24 13:18:11.613 [info] > git config --get commit.template [9ms]
2025-05-24 13:18:11.613 [info] > git config --get commit.template [30ms]
2025-05-24 13:18:11.613 [info] > git config --get commit.template [54ms]
2025-05-24 13:18:11.613 [info] > git config --get commit.template [80ms]
2025-05-24 13:18:11.613 [info] > git config --get commit.template [102ms]
2025-05-24 13:18:11.638 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:18:11.661 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:18:11.682 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:18:11.682 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:18:11.728 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:18:11.732 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [50ms]
2025-05-24 13:18:11.786 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [124ms]
2025-05-24 13:18:11.816 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [178ms]
2025-05-24 13:18:11.896 [info] > git fetch [137ms]
2025-05-24 13:18:11.923 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [195ms]
2025-05-24 13:18:11.952 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [250ms]
2025-05-24 13:18:12.000 [info] > git hash-object -w --stdin [158ms]
2025-05-24 13:18:12.001 [info] > git hash-object -w --stdin [215ms]
2025-05-24 13:18:12.001 [info] > git hash-object -w --stdin [185ms]
2025-05-24 13:18:12.071 [info] > git config --get commit.template [72ms]
2025-05-24 13:18:12.071 [info] > git hash-object -w --stdin [94ms]
2025-05-24 13:18:12.072 [info] > git hash-object -w --stdin [120ms]
2025-05-24 13:18:12.117 [info] > git config --get commit.template [47ms]
2025-05-24 13:18:12.117 [info] > git config --get commit.template [68ms]
2025-05-24 13:18:12.118 [info] > git config --get commit.template [92ms]
2025-05-24 13:18:12.138 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:18:12.146 [info] > git config --get commit.template [49ms]
2025-05-24 13:18:12.168 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:18:12.190 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:18:12.219 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:18:12.219 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [81ms]
2025-05-24 13:18:12.221 [info] > git config --get commit.template [104ms]
2025-05-24 13:18:12.245 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:18:12.269 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:18:12.270 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [51ms]
2025-05-24 13:18:12.293 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [103ms]
2025-05-24 13:18:12.316 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [148ms]
2025-05-24 13:18:12.342 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 6aca8d3409f146f2c60bae80f8eb363e418fc3f5 [26ms]
2025-05-24 13:18:12.342 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 f45b58453d90a2f7c53517f142627fc254b4a369 [49ms]
2025-05-24 13:18:12.342 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [73ms]
2025-05-24 13:18:12.386 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [141ms]
2025-05-24 13:18:12.414 [info] > git status -z -uall [49ms]
2025-05-24 13:18:12.414 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [29ms]
2025-05-24 13:18:12.442 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 fc4d0b0f596583cd89f41cffcb34defdcc13c733 [104ms]
2025-05-24 13:18:12.443 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 25eda2e8a0a86065a3e4a1d5edc663e67f7b8c39 [1ms]
2025-05-24 13:18:12.443 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 c229f1549b6c43cc25ca236283e8433e774dbad9 [32ms]
2025-05-24 13:18:12.740 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:18:13.967 [info] > git config --get commit.template [30ms]
2025-05-24 13:18:13.967 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:18:13.969 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 13:18:14.025 [info] > git status -z -uall [26ms]
2025-05-24 13:18:14.026 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 13:18:14.354 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:18:16.043 [info] > git check-ignore -v -z --stdin [2ms]
2025-05-24 13:18:24.215 [info] > git config --get commit.template [4ms]
2025-05-24 13:18:24.249 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:18:24.252 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 13:18:24.392 [info] > git status -z -uall [109ms]
2025-05-24 13:18:24.393 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [70ms]
2025-05-24 13:18:24.745 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:18:25.129 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:18:25.524 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/App.tsx [352ms]
2025-05-24 13:18:25.524 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/context/RestaurantStatusContext.tsx [333ms]
2025-05-24 13:18:25.524 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/Checkout.tsx [300ms]
2025-05-24 13:18:25.524 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/auth/AuthPage.tsx [278ms]
2025-05-24 13:18:25.524 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/auth/LogoutPage.tsx [257ms]
2025-05-24 13:18:25.524 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/manager/ManagerLayout.tsx [240ms]
2025-05-24 13:18:25.524 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/manager/ManagerPage.tsx [211ms]
2025-05-24 13:18:25.524 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:cookies.txt [184ms]
2025-05-24 13:18:25.524 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:server/admin-api.ts [165ms]
2025-05-24 13:18:25.524 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:server/auth.ts [150ms]
2025-05-24 13:18:25.524 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:server/routes.ts [129ms]
2025-05-24 13:18:25.524 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/components/NotificationDropdown.tsx [97ms]
2025-05-24 13:18:25.524 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/context/NotificationContext.tsx [77ms]
2025-05-24 13:18:25.524 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/hooks/useAudioNotification.ts [58ms]
2025-05-24 13:18:25.524 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:cookies2.txt [41ms]
2025-05-24 13:18:25.524 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:public/sounds/README.md [12ms]
2025-05-24 13:18:25.615 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [29ms]
2025-05-24 13:18:25.641 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [73ms]
2025-05-24 13:18:25.663 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [114ms]
2025-05-24 13:18:25.728 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [117ms]
2025-05-24 13:18:25.753 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [32ms]
2025-05-24 13:18:26.170 [info] > git hash-object -w --stdin [21ms]
2025-05-24 13:18:26.171 [info] > git hash-object -w --stdin [42ms]
2025-05-24 13:18:26.172 [info] > git hash-object -w --stdin [79ms]
2025-05-24 13:18:26.172 [info] > git hash-object -w --stdin [99ms]
2025-05-24 13:18:26.172 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- server/routes.ts [113ms]
2025-05-24 13:18:26.173 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- server/auth.ts [131ms]
2025-05-24 13:18:26.173 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- server/admin-api.ts [151ms]
2025-05-24 13:18:26.173 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/Checkout.tsx [185ms]
2025-05-24 13:18:26.173 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- cookies.txt [203ms]
2025-05-24 13:18:26.173 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/manager/ManagerPage.tsx [220ms]
2025-05-24 13:18:26.173 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/auth/LogoutPage.tsx [240ms]
2025-05-24 13:18:26.173 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/manager/ManagerLayout.tsx [267ms]
2025-05-24 13:18:26.173 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/auth/AuthPage.tsx [300ms]
2025-05-24 13:18:26.174 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/context/RestaurantStatusContext.tsx [320ms]
2025-05-24 13:18:26.174 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/App.tsx [340ms]
2025-05-24 13:18:26.277 [info] > git hash-object -w --stdin [111ms]
2025-05-24 13:18:26.309 [info] > git config --get commit.template [35ms]
2025-05-24 13:18:26.309 [info] > git config --get commit.template [56ms]
2025-05-24 13:18:26.309 [info] > git config --get commit.template [77ms]
2025-05-24 13:18:26.309 [info] > git config --get commit.template [105ms]
2025-05-24 13:18:26.334 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:18:26.351 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:18:26.368 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:18:26.386 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:18:26.386 [info] > git config --get commit.template [78ms]
2025-05-24 13:18:26.423 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:18:26.424 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [56ms]
2025-05-24 13:18:26.447 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [96ms]
2025-05-24 13:18:26.465 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [131ms]
2025-05-24 13:18:26.495 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [73ms]
2025-05-24 13:18:26.546 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [161ms]
2025-05-24 13:18:26.570 [info] > git status -z -uall [45ms]
2025-05-24 13:18:26.570 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [24ms]
2025-05-24 13:18:26.595 [info] > git hash-object -w --stdin [113ms]
2025-05-24 13:18:26.596 [info] > git hash-object -w --stdin [131ms]
2025-05-24 13:18:26.598 [info] > git hash-object -w --stdin [151ms]
2025-05-24 13:18:26.671 [info] > git hash-object -w --stdin [104ms]
2025-05-24 13:18:26.700 [info] > git config --get commit.template [30ms]
2025-05-24 13:18:26.700 [info] > git config --get commit.template [50ms]
2025-05-24 13:18:26.700 [info] > git config --get commit.template [71ms]
2025-05-24 13:18:26.700 [info] > git hash-object -w --stdin [105ms]
2025-05-24 13:18:26.735 [info] > git config --get commit.template [36ms]
2025-05-24 13:18:26.760 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:18:26.783 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:18:26.820 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:18:26.847 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:18:26.849 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [34ms]
2025-05-24 13:18:26.872 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [89ms]
2025-05-24 13:18:26.901 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [141ms]
2025-05-24 13:18:26.928 [info] > git config --get commit.template [194ms]
2025-05-24 13:18:26.973 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:18:27.032 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 f45b58453d90a2f7c53517f142627fc254b4a369 [104ms]
2025-05-24 13:18:27.032 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 635b2abac91212da2bae3ca8263bd5ae06baba45 [131ms]
2025-05-24 13:18:27.032 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 c229f1549b6c43cc25ca236283e8433e774dbad9 [160ms]
2025-05-24 13:18:27.033 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [187ms]
2025-05-24 13:18:27.058 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [86ms]
2025-05-24 13:18:27.103 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [154ms]
2025-05-24 13:18:27.105 [info] > git status -z -uall [28ms]
2025-05-24 13:18:27.106 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 25eda2e8a0a86065a3e4a1d5edc663e67f7b8c39 [49ms]
2025-05-24 13:18:27.106 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 13:18:27.149 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 fc4d0b0f596583cd89f41cffcb34defdcc13c733 [8ms]
2025-05-24 13:18:27.441 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 13:18:29.442 [info] > git config --get commit.template [2ms]
2025-05-24 13:18:29.474 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:18:29.476 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:18:29.552 [info] > git status -z -uall [33ms]
2025-05-24 13:18:29.553 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 13:18:29.968 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [82ms]
2025-05-24 13:18:31.634 [info] > git show --textconv :cookies.txt [47ms]
2025-05-24 13:18:31.636 [info] > git ls-files --stage -- cookies.txt [2ms]
2025-05-24 13:18:31.659 [info] > git cat-file -s e38d924c57afac8bd8cf8ec5ddc5b3a3fd398d7a [2ms]
2025-05-24 13:18:34.609 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:18:34.610 [info] > git config --get commit.template [33ms]
2025-05-24 13:18:34.613 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 13:18:34.661 [info] > git status -z -uall [23ms]
2025-05-24 13:18:34.663 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:18:34.991 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 13:18:39.721 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:18:39.722 [info] > git config --get commit.template [33ms]
2025-05-24 13:18:39.724 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 13:18:39.772 [info] > git status -z -uall [25ms]
2025-05-24 13:18:39.774 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:18:40.099 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:18:44.825 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:18:44.826 [info] > git config --get commit.template [26ms]
2025-05-24 13:18:44.827 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:18:44.879 [info] > git status -z -uall [28ms]
2025-05-24 13:18:44.881 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 13:18:45.213 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:19:47.867 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:19:47.868 [info] > git config --get commit.template [25ms]
2025-05-24 13:19:47.869 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:19:47.969 [info] > git status -z -uall [77ms]
2025-05-24 13:19:47.969 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [54ms]
2025-05-24 13:19:48.295 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:19:53.027 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:19:53.027 [info] > git config --get commit.template [24ms]
2025-05-24 13:19:53.028 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:19:53.079 [info] > git status -z -uall [26ms]
2025-05-24 13:19:53.080 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 13:19:53.410 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:22:45.220 [info] > git fetch [26ms]
2025-05-24 13:22:45.224 [info] > git config --get commit.template [5ms]
2025-05-24 13:22:45.269 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:22:45.270 [info] > git config --get commit.template [22ms]
2025-05-24 13:22:45.289 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:22:45.290 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [21ms]
2025-05-24 13:22:45.291 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:22:45.333 [info] > git status -z -uall [22ms]
2025-05-24 13:22:45.333 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:22:45.664 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:22:46.750 [info] > git ls-files --stage -- cookies.txt [2ms]
2025-05-24 13:22:46.774 [info] > git cat-file -s e38d924c57afac8bd8cf8ec5ddc5b3a3fd398d7a [2ms]
2025-05-24 13:22:46.982 [info] > git show --textconv :cookies.txt [1ms]
2025-05-24 13:22:50.344 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:22:50.345 [info] > git config --get commit.template [27ms]
2025-05-24 13:22:50.345 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:22:50.406 [info] > git status -z -uall [31ms]
2025-05-24 13:22:50.407 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:22:50.733 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:23:53.538 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:23:53.538 [info] > git config --get commit.template [47ms]
2025-05-24 13:23:53.540 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:23:53.667 [info] > git status -z -uall [69ms]
2025-05-24 13:23:53.669 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 13:23:54.049 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:25:29.847 [info] > git config --get commit.template [2ms]
2025-05-24 13:25:29.884 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:25:29.886 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:25:30.015 [info] > git status -z -uall [92ms]
2025-05-24 13:25:30.016 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [68ms]
2025-05-24 13:25:30.343 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:25:35.080 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:25:35.080 [info] > git config --get commit.template [33ms]
2025-05-24 13:25:35.081 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:25:35.134 [info] > git status -z -uall [26ms]
2025-05-24 13:25:35.135 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:25:35.462 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:25:40.192 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:25:40.193 [info] > git config --get commit.template [30ms]
2025-05-24 13:25:40.194 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:25:40.254 [info] > git status -z -uall [31ms]
2025-05-24 13:25:40.255 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 13:25:40.582 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 13:34:38.595 [info] > git fetch [33ms]
2025-05-24 13:34:38.640 [info] > git config --get commit.template [45ms]
2025-05-24 13:34:38.641 [info] > git config --get commit.template [3ms]
2025-05-24 13:34:38.679 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:34:38.721 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:34:38.722 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [43ms]
2025-05-24 13:34:38.723 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:34:38.800 [info] > git status -z -uall [38ms]
2025-05-24 13:34:38.802 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 13:34:39.256 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [110ms]
2025-05-24 13:34:39.739 [info] > git check-ignore -v -z --stdin [2ms]
2025-05-24 13:34:39.853 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 13:34:40.979 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/App.tsx [977ms]
2025-05-24 13:34:40.979 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/context/RestaurantStatusContext.tsx [932ms]
2025-05-24 13:34:40.979 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/Checkout.tsx [892ms]
2025-05-24 13:34:40.979 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/auth/AuthPage.tsx [857ms]
2025-05-24 13:34:40.979 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/auth/LogoutPage.tsx [792ms]
2025-05-24 13:34:40.979 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/driver/DriverLayout.tsx [731ms]
2025-05-24 13:34:40.980 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/driver/DriverPage.tsx [670ms]
2025-05-24 13:34:40.980 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/manager/ManagerLayout.tsx [641ms]
2025-05-24 13:34:40.980 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/manager/ManagerPage.tsx [578ms]
2025-05-24 13:34:40.980 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:cookies.txt [538ms]
2025-05-24 13:34:40.980 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:server/admin-api.ts [469ms]
2025-05-24 13:34:40.980 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:server/auth.ts [372ms]
2025-05-24 13:34:40.980 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:server/routes.ts [332ms]
2025-05-24 13:34:40.980 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/components/NotificationDropdown.tsx [263ms]
2025-05-24 13:34:40.980 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/context/NotificationContext.tsx [205ms]
2025-05-24 13:34:40.980 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/hooks/useAudioNotification.ts [158ms]
2025-05-24 13:34:40.980 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/RealOrderTracker.tsx [120ms]
2025-05-24 13:34:40.980 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/utils/orderStatusWorkflow.ts [84ms]
2025-05-24 13:34:40.980 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:cookies2.txt [56ms]
2025-05-24 13:34:40.980 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:public/sounds/README.md [22ms]
2025-05-24 13:34:41.260 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [53ms]
2025-05-24 13:34:41.290 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [119ms]
2025-05-24 13:34:41.322 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [207ms]
2025-05-24 13:34:41.368 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [296ms]
2025-05-24 13:34:41.407 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [363ms]
2025-05-24 13:34:41.437 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [423ms]
2025-05-24 13:34:41.643 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [398ms]
2025-05-24 13:34:42.502 [info] > git hash-object -w --stdin [104ms]
2025-05-24 13:34:42.503 [info] > git hash-object -w --stdin [139ms]
2025-05-24 13:34:42.505 [info] > git hash-object -w --stdin [170ms]
2025-05-24 13:34:42.505 [info] > git hash-object -w --stdin [199ms]
2025-05-24 13:34:42.505 [info] > git hash-object -w --stdin [233ms]
2025-05-24 13:34:42.506 [info] > git hash-object -w --stdin [272ms]
2025-05-24 13:34:42.506 [info] > git hash-object -w --stdin [305ms]
2025-05-24 13:34:42.507 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- server/routes.ts [335ms]
2025-05-24 13:34:42.507 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- server/auth.ts [359ms]
2025-05-24 13:34:42.507 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- server/admin-api.ts [398ms]
2025-05-24 13:34:42.508 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/manager/ManagerPage.tsx [435ms]
2025-05-24 13:34:42.508 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/manager/ManagerLayout.tsx [469ms]
2025-05-24 13:34:42.508 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- cookies.txt [524ms]
2025-05-24 13:34:42.508 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/driver/DriverPage.tsx [554ms]
2025-05-24 13:34:42.508 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/driver/DriverLayout.tsx [584ms]
2025-05-24 13:34:42.508 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/auth/LogoutPage.tsx [631ms]
2025-05-24 13:34:42.508 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/auth/AuthPage.tsx [659ms]
2025-05-24 13:34:42.509 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/Checkout.tsx [711ms]
2025-05-24 13:34:42.509 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/context/RestaurantStatusContext.tsx [746ms]
2025-05-24 13:34:42.509 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/App.tsx [785ms]
2025-05-24 13:34:42.807 [info] > git config --get commit.template [72ms]
2025-05-24 13:34:42.807 [info] > git config --get commit.template [100ms]
2025-05-24 13:34:42.808 [info] > git config --get commit.template [128ms]
2025-05-24 13:34:42.808 [info] > git config --get commit.template [163ms]
2025-05-24 13:34:42.808 [info] > git config --get commit.template [190ms]
2025-05-24 13:34:42.808 [info] > git config --get commit.template [222ms]
2025-05-24 13:34:42.808 [info] > git config --get commit.template [249ms]
2025-05-24 13:34:42.811 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:34:42.894 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:34:42.894 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:34:42.932 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:34:42.959 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:34:42.986 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:34:43.017 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:34:43.026 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [9ms]
2025-05-24 13:34:43.114 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [129ms]
2025-05-24 13:34:43.146 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [187ms]
2025-05-24 13:34:43.176 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [244ms]
2025-05-24 13:34:43.203 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [309ms]
2025-05-24 13:34:43.249 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [382ms]
2025-05-24 13:34:43.275 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [435ms]
2025-05-24 13:34:43.313 [info] > git status -z -uall [244ms]
2025-05-24 13:34:43.314 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [201ms]
2025-05-24 13:34:43.355 [info] > git hash-object -w --stdin [81ms]
2025-05-24 13:34:43.355 [info] > git hash-object -w --stdin [153ms]
2025-05-24 13:34:43.356 [info] > git hash-object -w --stdin [107ms]
2025-05-24 13:34:43.356 [info] > git hash-object -w --stdin [56ms]
2025-05-24 13:34:43.357 [info] > git hash-object -w --stdin [211ms]
2025-05-24 13:34:43.357 [info] > git hash-object -w --stdin [182ms]
2025-05-24 13:34:43.549 [info] > git hash-object -w --stdin [207ms]
2025-05-24 13:34:43.596 [info] > git config --get commit.template [48ms]
2025-05-24 13:34:43.597 [info] > git config --get commit.template [78ms]
2025-05-24 13:34:43.597 [info] > git config --get commit.template [109ms]
2025-05-24 13:34:43.597 [info] > git config --get commit.template [145ms]
2025-05-24 13:34:43.597 [info] > git config --get commit.template [179ms]
2025-05-24 13:34:43.597 [info] > git config --get commit.template [210ms]
2025-05-24 13:34:43.601 [info] > git config --get commit.template [9ms]
2025-05-24 13:34:43.632 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:34:43.663 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:34:43.691 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:34:43.716 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:34:43.757 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:34:43.782 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:34:43.842 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:34:43.846 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [64ms]
2025-05-24 13:34:43.888 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [131ms]
2025-05-24 13:34:43.921 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [206ms]
2025-05-24 13:34:43.951 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [261ms]
2025-05-24 13:34:43.984 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [321ms]
2025-05-24 13:34:44.021 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [389ms]
2025-05-24 13:34:44.092 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 4ee0dfd88d34fcf8174d114a98258be24124d5ee [72ms]
2025-05-24 13:34:44.092 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 7799d8a51313aaf146b2aa1409e102c36691c508 [109ms]
2025-05-24 13:34:44.092 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 fc4d0b0f596583cd89f41cffcb34defdcc13c733 [141ms]
2025-05-24 13:34:44.092 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 c229f1549b6c43cc25ca236283e8433e774dbad9 [171ms]
2025-05-24 13:34:44.093 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 635b2abac91212da2bae3ca8263bd5ae06baba45 [208ms]
2025-05-24 13:34:44.093 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [252ms]
2025-05-24 13:34:44.121 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [310ms]
2025-05-24 13:34:44.127 [info] > git config --get commit.template [37ms]
2025-05-24 13:34:44.127 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 f45b58453d90a2f7c53517f142627fc254b4a369 [69ms]
2025-05-24 13:34:44.128 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 25eda2e8a0a86065a3e4a1d5edc663e67f7b8c39 [7ms]
2025-05-24 13:34:44.158 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:34:44.164 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-05-24 13:34:44.223 [info] > git status -z -uall [31ms]
2025-05-24 13:34:44.224 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 13:34:44.554 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:34:49.258 [info] > git config --get commit.template [7ms]
2025-05-24 13:34:49.288 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:34:49.290 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:34:49.357 [info] > git status -z -uall [29ms]
2025-05-24 13:34:49.359 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:34:49.692 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:34:54.391 [info] > git config --get commit.template [1ms]
2025-05-24 13:34:54.419 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:34:54.420 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:34:54.473 [info] > git status -z -uall [26ms]
2025-05-24 13:34:54.474 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:34:54.816 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-05-24 13:34:59.527 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:34:59.528 [info] > git config --get commit.template [28ms]
2025-05-24 13:34:59.529 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:34:59.579 [info] > git status -z -uall [25ms]
2025-05-24 13:34:59.580 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:34:59.912 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:35:04.637 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:35:04.637 [info] > git config --get commit.template [30ms]
2025-05-24 13:35:04.638 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:35:04.694 [info] > git status -z -uall [25ms]
2025-05-24 13:35:04.695 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 13:35:05.027 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:35:09.768 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:35:09.826 [info] > git config --get commit.template [90ms]
2025-05-24 13:35:09.831 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [63ms]
2025-05-24 13:35:09.907 [info] > git status -z -uall [46ms]
2025-05-24 13:35:09.910 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-05-24 13:35:10.322 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:35:10.728 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:35:11.426 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/App.tsx [633ms]
2025-05-24 13:35:11.426 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/context/RestaurantStatusContext.tsx [608ms]
2025-05-24 13:35:11.426 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/Checkout.tsx [577ms]
2025-05-24 13:35:11.426 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/auth/AuthPage.tsx [551ms]
2025-05-24 13:35:11.426 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/auth/LogoutPage.tsx [525ms]
2025-05-24 13:35:11.426 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/driver/DriverLayout.tsx [500ms]
2025-05-24 13:35:11.426 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/driver/DriverPage.tsx [476ms]
2025-05-24 13:35:11.426 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/manager/ManagerLayout.tsx [430ms]
2025-05-24 13:35:11.426 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/manager/ManagerPage.tsx [398ms]
2025-05-24 13:35:11.426 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:cookies.txt [365ms]
2025-05-24 13:35:11.427 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:server/admin-api.ts [328ms]
2025-05-24 13:35:11.427 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:server/auth.ts [291ms]
2025-05-24 13:35:11.427 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:server/routes.ts [263ms]
2025-05-24 13:35:11.427 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/components/NotificationDropdown.tsx [234ms]
2025-05-24 13:35:11.427 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/context/NotificationContext.tsx [206ms]
2025-05-24 13:35:11.427 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/hooks/useAudioNotification.ts [172ms]
2025-05-24 13:35:11.427 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/RealOrderTracker.tsx [126ms]
2025-05-24 13:35:11.427 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/utils/orderStatusWorkflow.ts [92ms]
2025-05-24 13:35:11.427 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:cookies2.txt [50ms]
2025-05-24 13:35:11.427 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:public/sounds/README.md [13ms]
2025-05-24 13:35:11.634 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [48ms]
2025-05-24 13:35:11.682 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [128ms]
2025-05-24 13:35:11.719 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [199ms]
2025-05-24 13:35:11.768 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [284ms]
2025-05-24 13:35:11.801 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [350ms]
2025-05-24 13:35:11.977 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [347ms]
2025-05-24 13:35:12.020 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [130ms]
2025-05-24 13:35:12.740 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/manager/ManagerLayout.tsx [12ms]
2025-05-24 13:35:12.740 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- cookies.txt [49ms]
2025-05-24 13:35:12.741 [info] > git hash-object -w --stdin [84ms]
2025-05-24 13:35:12.741 [info] > git hash-object -w --stdin [114ms]
2025-05-24 13:35:12.742 [info] > git hash-object -w --stdin [169ms]
2025-05-24 13:35:12.742 [info] > git hash-object -w --stdin [196ms]
2025-05-24 13:35:12.742 [info] > git hash-object -w --stdin [223ms]
2025-05-24 13:35:12.743 [info] > git hash-object -w --stdin [257ms]
2025-05-24 13:35:12.743 [info] > git hash-object -w --stdin [284ms]
2025-05-24 13:35:12.743 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- server/routes.ts [320ms]
2025-05-24 13:35:12.743 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- server/auth.ts [350ms]
2025-05-24 13:35:12.744 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- server/admin-api.ts [383ms]
2025-05-24 13:35:12.744 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/Checkout.tsx [408ms]
2025-05-24 13:35:12.745 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/manager/ManagerPage.tsx [441ms]
2025-05-24 13:35:12.745 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/driver/DriverPage.tsx [470ms]
2025-05-24 13:35:12.745 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/driver/DriverLayout.tsx [506ms]
2025-05-24 13:35:12.745 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/auth/LogoutPage.tsx [538ms]
2025-05-24 13:35:12.745 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/auth/AuthPage.tsx [570ms]
2025-05-24 13:35:12.745 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/context/RestaurantStatusContext.tsx [595ms]
2025-05-24 13:35:12.745 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/App.tsx [632ms]
2025-05-24 13:35:13.076 [info] > git config --get commit.template [156ms]
2025-05-24 13:35:13.076 [info] > git config --get commit.template [186ms]
2025-05-24 13:35:13.076 [info] > git config --get commit.template [215ms]
2025-05-24 13:35:13.076 [info] > git config --get commit.template [246ms]
2025-05-24 13:35:13.076 [info] > git config --get commit.template [276ms]
2025-05-24 13:35:13.076 [info] > git config --get commit.template [306ms]
2025-05-24 13:35:13.100 [info] > git config --get commit.template [116ms]
2025-05-24 13:35:13.177 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:35:13.258 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:35:13.302 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:35:13.346 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:35:13.419 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:35:13.470 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:35:13.516 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:35:13.641 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [125ms]
2025-05-24 13:35:13.728 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [258ms]
2025-05-24 13:35:13.769 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [351ms]
2025-05-24 13:35:13.834 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [488ms]
2025-05-24 13:35:13.891 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [588ms]
2025-05-24 13:35:13.943 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [686ms]
2025-05-24 13:35:13.979 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [802ms]
2025-05-24 13:35:14.034 [info] > git status -z -uall [357ms]
2025-05-24 13:35:14.036 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [308ms]
2025-05-24 13:35:14.070 [info] > git hash-object -w --stdin [48ms]
2025-05-24 13:35:14.071 [info] > git hash-object -w --stdin [92ms]
2025-05-24 13:35:14.072 [info] > git hash-object -w --stdin [129ms]
2025-05-24 13:35:14.072 [info] > git hash-object -w --stdin [238ms]
2025-05-24 13:35:14.072 [info] > git hash-object -w --stdin [182ms]
2025-05-24 13:35:14.073 [info] > git hash-object -w --stdin [304ms]
2025-05-24 13:35:14.278 [info] > git hash-object -w --stdin [211ms]
2025-05-24 13:35:14.321 [info] > git config --get commit.template [43ms]
2025-05-24 13:35:14.322 [info] > git config --get commit.template [80ms]
2025-05-24 13:35:14.322 [info] > git config --get commit.template [117ms]
2025-05-24 13:35:14.322 [info] > git config --get commit.template [154ms]
2025-05-24 13:35:14.322 [info] > git config --get commit.template [186ms]
2025-05-24 13:35:14.322 [info] > git config --get commit.template [222ms]
2025-05-24 13:35:14.325 [info] > git config --get commit.template [17ms]
2025-05-24 13:35:14.360 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:35:14.392 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:35:14.429 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:35:14.465 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:35:14.501 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:35:14.534 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:35:14.597 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:35:14.603 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [70ms]
2025-05-24 13:35:14.635 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [133ms]
2025-05-24 13:35:14.660 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [196ms]
2025-05-24 13:35:14.685 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [256ms]
2025-05-24 13:35:14.709 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [317ms]
2025-05-24 13:35:14.739 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [380ms]
2025-05-24 13:35:14.775 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 4ee0dfd88d34fcf8174d114a98258be24124d5ee [36ms]
2025-05-24 13:35:14.775 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 f45b58453d90a2f7c53517f142627fc254b4a369 [67ms]
2025-05-24 13:35:14.775 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 635b2abac91212da2bae3ca8263bd5ae06baba45 [91ms]
2025-05-24 13:35:14.775 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 7799d8a51313aaf146b2aa1409e102c36691c508 [115ms]
2025-05-24 13:35:14.776 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 25eda2e8a0a86065a3e4a1d5edc663e67f7b8c39 [142ms]
2025-05-24 13:35:14.776 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [179ms]
2025-05-24 13:35:14.855 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [291ms]
2025-05-24 13:35:14.882 [info] > git status -z -uall [69ms]
2025-05-24 13:35:14.883 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 fc4d0b0f596583cd89f41cffcb34defdcc13c733 [113ms]
2025-05-24 13:35:14.884 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [30ms]
2025-05-24 13:35:14.977 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 c229f1549b6c43cc25ca236283e8433e774dbad9 [51ms]
2025-05-24 13:35:14.982 [info] > git config --get commit.template [6ms]
2025-05-24 13:35:15.024 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:35:15.030 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-05-24 13:35:15.089 [info] > git status -z -uall [29ms]
2025-05-24 13:35:15.092 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-05-24 13:35:15.515 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 13:35:20.146 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:35:20.147 [info] > git config --get commit.template [25ms]
2025-05-24 13:35:20.147 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:35:20.258 [info] > git status -z -uall [86ms]
2025-05-24 13:35:20.261 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [62ms]
2025-05-24 13:35:20.591 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:35:25.317 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:35:25.319 [info] > git config --get commit.template [27ms]
2025-05-24 13:35:25.319 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:35:25.371 [info] > git status -z -uall [29ms]
2025-05-24 13:35:25.373 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 13:35:25.700 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:35:30.479 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:35:30.483 [info] > git config --get commit.template [59ms]
2025-05-24 13:35:30.484 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-05-24 13:35:30.563 [info] > git status -z -uall [39ms]
2025-05-24 13:35:30.567 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-05-24 13:35:31.048 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [117ms]
2025-05-24 13:35:31.589 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [68ms]
2025-05-24 13:35:32.476 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/App.tsx [796ms]
2025-05-24 13:35:32.476 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/context/RestaurantStatusContext.tsx [737ms]
2025-05-24 13:35:32.476 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/Checkout.tsx [699ms]
2025-05-24 13:35:32.476 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/auth/AuthPage.tsx [641ms]
2025-05-24 13:35:32.476 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/auth/LogoutPage.tsx [585ms]
2025-05-24 13:35:32.477 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/driver/DriverLayout.tsx [518ms]
2025-05-24 13:35:32.477 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/driver/DriverPage.tsx [486ms]
2025-05-24 13:35:32.477 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/manager/ManagerLayout.tsx [447ms]
2025-05-24 13:35:32.477 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/manager/ManagerPage.tsx [415ms]
2025-05-24 13:35:32.477 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:cookies.txt [377ms]
2025-05-24 13:35:32.477 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:server/admin-api.ts [341ms]
2025-05-24 13:35:32.477 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:server/auth.ts [303ms]
2025-05-24 13:35:32.477 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:server/routes.ts [267ms]
2025-05-24 13:35:32.477 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/components/NotificationDropdown.tsx [235ms]
2025-05-24 13:35:32.477 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/context/NotificationContext.tsx [205ms]
2025-05-24 13:35:32.477 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/hooks/useAudioNotification.ts [175ms]
2025-05-24 13:35:32.477 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/RealOrderTracker.tsx [145ms]
2025-05-24 13:35:32.477 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/utils/orderStatusWorkflow.ts [119ms]
2025-05-24 13:35:32.477 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:cookies2.txt [91ms]
2025-05-24 13:35:32.477 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:public/sounds/README.md [61ms]
2025-05-24 13:35:32.746 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [100ms]
2025-05-24 13:35:32.785 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [166ms]
2025-05-24 13:35:32.823 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [234ms]
2025-05-24 13:35:32.849 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [286ms]
2025-05-24 13:35:32.876 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [339ms]
2025-05-24 13:35:32.906 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [399ms]
2025-05-24 13:35:32.947 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [276ms]
2025-05-24 13:35:33.613 [info] > git hash-object -w --stdin [54ms]
2025-05-24 13:35:33.614 [info] > git hash-object -w --stdin [82ms]
2025-05-24 13:35:33.620 [info] > git hash-object -w --stdin [112ms]
2025-05-24 13:35:33.621 [info] > git hash-object -w --stdin [144ms]
2025-05-24 13:35:33.621 [info] > git hash-object -w --stdin [165ms]
2025-05-24 13:35:33.621 [info] > git hash-object -w --stdin [193ms]
2025-05-24 13:35:33.622 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- server/routes.ts [259ms]
2025-05-24 13:35:33.622 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- server/auth.ts [280ms]
2025-05-24 13:35:33.622 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- server/admin-api.ts [305ms]
2025-05-24 13:35:33.622 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- cookies.txt [342ms]
2025-05-24 13:35:33.623 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/manager/ManagerPage.tsx [369ms]
2025-05-24 13:35:33.623 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/manager/ManagerLayout.tsx [402ms]
2025-05-24 13:35:33.623 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/driver/DriverPage.tsx [441ms]
2025-05-24 13:35:33.623 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/driver/DriverLayout.tsx [462ms]
2025-05-24 13:35:33.623 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/auth/LogoutPage.tsx [483ms]
2025-05-24 13:35:33.623 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/Checkout.tsx [510ms]
2025-05-24 13:35:33.623 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/auth/AuthPage.tsx [545ms]
2025-05-24 13:35:33.624 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/context/RestaurantStatusContext.tsx [569ms]
2025-05-24 13:35:33.624 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/App.tsx [592ms]
2025-05-24 13:35:33.776 [info] > git hash-object -w --stdin [183ms]
2025-05-24 13:35:33.808 [info] > git config --get commit.template [34ms]
2025-05-24 13:35:33.808 [info] > git config --get commit.template [59ms]
2025-05-24 13:35:33.808 [info] > git config --get commit.template [82ms]
2025-05-24 13:35:33.808 [info] > git config --get commit.template [106ms]
2025-05-24 13:35:33.808 [info] > git config --get commit.template [133ms]
2025-05-24 13:35:33.809 [info] > git config --get commit.template [160ms]
2025-05-24 13:35:33.823 [info] > git config --get commit.template [17ms]
2025-05-24 13:35:33.823 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:35:33.875 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:35:33.902 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:35:33.931 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:35:33.957 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:35:33.984 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:35:34.015 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:35:34.068 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [85ms]
2025-05-24 13:35:34.096 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [139ms]
2025-05-24 13:35:34.124 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [193ms]
2025-05-24 13:35:34.153 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [251ms]
2025-05-24 13:35:34.177 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [302ms]
2025-05-24 13:35:34.202 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [349ms]
2025-05-24 13:35:34.227 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [213ms]
2025-05-24 13:35:34.288 [info] > git status -z -uall [29ms]
2025-05-24 13:35:34.289 [info] > git hash-object -w --stdin [65ms]
2025-05-24 13:35:34.289 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms] (cancelled)
2025-05-24 13:35:34.316 [info] > git hash-object -w --stdin [140ms]
2025-05-24 13:35:34.316 [info] > git hash-object -w --stdin [114ms]
2025-05-24 13:35:34.317 [info] > git hash-object -w --stdin [193ms]
2025-05-24 13:35:34.317 [info] > git hash-object -w --stdin [165ms]
2025-05-24 13:35:34.317 [info] > git hash-object -w --stdin [222ms]
2025-05-24 13:35:34.481 [info] > git config --get commit.template [6ms]
2025-05-24 13:35:34.481 [info] > git config --get commit.template [30ms]
2025-05-24 13:35:34.481 [info] > git config --get commit.template [54ms]
2025-05-24 13:35:34.481 [info] > git config --get commit.template [79ms]
2025-05-24 13:35:34.481 [info] > git config --get commit.template [102ms]
2025-05-24 13:35:34.482 [info] > git config --get commit.template [126ms]
2025-05-24 13:35:34.482 [info] > git hash-object -w --stdin [167ms]
2025-05-24 13:35:34.535 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:35:34.560 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:35:34.584 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:35:34.584 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:35:34.645 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:35:34.645 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:35:34.677 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-05-24 13:35:34.705 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [60ms]
2025-05-24 13:35:34.729 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [112ms]
2025-05-24 13:35:34.755 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [171ms]
2025-05-24 13:35:34.779 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [220ms]
2025-05-24 13:35:34.806 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [271ms]
2025-05-24 13:35:34.853 [info] > git config --get commit.template [345ms]
2025-05-24 13:35:34.885 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:35:34.952 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 4ee0dfd88d34fcf8174d114a98258be24124d5ee [146ms]
2025-05-24 13:35:34.952 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 7799d8a51313aaf146b2aa1409e102c36691c508 [173ms]
2025-05-24 13:35:34.952 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 c229f1549b6c43cc25ca236283e8433e774dbad9 [197ms]
2025-05-24 13:35:34.952 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 635b2abac91212da2bae3ca8263bd5ae06baba45 [223ms]
2025-05-24 13:35:34.952 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 25eda2e8a0a86065a3e4a1d5edc663e67f7b8c39 [247ms]
2025-05-24 13:35:34.954 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [70ms]
2025-05-24 13:35:35.010 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 f45b58453d90a2f7c53517f142627fc254b4a369 [157ms]
2025-05-24 13:35:35.012 [info] > git status -z -uall [33ms]
2025-05-24 13:35:35.015 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-05-24 13:35:35.058 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 fc4d0b0f596583cd89f41cffcb34defdcc13c733 [7ms]
2025-05-24 13:35:35.370 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:35:35.602 [info] > git config --get commit.template [4ms]
2025-05-24 13:35:35.627 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:35:35.629 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:35:35.687 [info] > git status -z -uall [32ms]
2025-05-24 13:35:35.687 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 13:35:36.018 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:35:40.737 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:35:40.737 [info] > git config --get commit.template [23ms]
2025-05-24 13:35:40.738 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:35:40.788 [info] > git status -z -uall [26ms]
2025-05-24 13:35:40.789 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 13:35:41.133 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 13:35:45.854 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:35:45.855 [info] > git config --get commit.template [31ms]
2025-05-24 13:35:45.857 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 13:35:45.910 [info] > git status -z -uall [28ms]
2025-05-24 13:35:45.912 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:35:46.246 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:35:50.978 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:35:50.982 [info] > git config --get commit.template [29ms]
2025-05-24 13:35:50.982 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 13:35:51.041 [info] > git status -z -uall [29ms]
2025-05-24 13:35:51.041 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:35:51.370 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:35:56.093 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:35:56.094 [info] > git config --get commit.template [26ms]
2025-05-24 13:35:56.095 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:35:56.146 [info] > git status -z -uall [27ms]
2025-05-24 13:35:56.148 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:35:56.476 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:36:01.181 [info] > git config --get commit.template [1ms]
2025-05-24 13:36:01.206 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:36:01.208 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:36:01.257 [info] > git status -z -uall [26ms]
2025-05-24 13:36:01.258 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 13:36:01.585 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:36:06.321 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:36:06.375 [info] > git config --get commit.template [55ms]
2025-05-24 13:36:06.378 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:36:06.520 [info] > git status -z -uall [74ms]
2025-05-24 13:36:06.523 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 13:36:06.850 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:36:11.593 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:36:11.594 [info] > git config --get commit.template [43ms]
2025-05-24 13:36:11.595 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:36:11.659 [info] > git status -z -uall [24ms]
2025-05-24 13:36:11.660 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:36:11.993 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:36:16.700 [info] > git config --get commit.template [9ms]
2025-05-24 13:36:16.724 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:36:16.734 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [10ms]
2025-05-24 13:36:16.793 [info] > git status -z -uall [35ms]
2025-05-24 13:36:16.794 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:36:17.123 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:36:21.861 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:36:21.862 [info] > git config --get commit.template [36ms]
2025-05-24 13:36:21.863 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 13:36:21.928 [info] > git status -z -uall [32ms]
2025-05-24 13:36:21.928 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 13:36:22.259 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 13:36:22.715 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:36:23.271 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/App.tsx [502ms]
2025-05-24 13:36:23.271 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/context/RestaurantStatusContext.tsx [475ms]
2025-05-24 13:36:23.271 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/Checkout.tsx [441ms]
2025-05-24 13:36:23.271 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/auth/AuthPage.tsx [413ms]
2025-05-24 13:36:23.271 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/auth/LogoutPage.tsx [386ms]
2025-05-24 13:36:23.272 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/driver/DriverLayout.tsx [360ms]
2025-05-24 13:36:23.272 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/driver/DriverPage.tsx [334ms]
2025-05-24 13:36:23.272 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/manager/ManagerLayout.tsx [304ms]
2025-05-24 13:36:23.272 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/manager/ManagerPage.tsx [281ms]
2025-05-24 13:36:23.272 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:cookies.txt [254ms]
2025-05-24 13:36:23.272 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:server/admin-api.ts [231ms]
2025-05-24 13:36:23.272 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:server/auth.ts [209ms]
2025-05-24 13:36:23.272 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:server/routes.ts [182ms]
2025-05-24 13:36:23.272 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/components/NotificationDropdown.tsx [159ms]
2025-05-24 13:36:23.272 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/context/NotificationContext.tsx [136ms]
2025-05-24 13:36:23.272 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/hooks/useAudioNotification.ts [107ms]
2025-05-24 13:36:23.272 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/RealOrderTracker.tsx [84ms]
2025-05-24 13:36:23.272 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/utils/orderStatusWorkflow.ts [59ms]
2025-05-24 13:36:23.272 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:cookies2.txt [36ms]
2025-05-24 13:36:23.272 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:public/sounds/README.md [6ms]
2025-05-24 13:36:23.445 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [26ms]
2025-05-24 13:36:23.471 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [79ms]
2025-05-24 13:36:23.500 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [130ms]
2025-05-24 13:36:23.531 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [185ms]
2025-05-24 13:36:23.558 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [235ms]
2025-05-24 13:36:23.585 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [286ms]
2025-05-24 13:36:23.622 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [180ms]
2025-05-24 13:36:24.226 [info] > git hash-object -w --stdin [35ms]
2025-05-24 13:36:24.228 [info] > git hash-object -w --stdin [68ms]
2025-05-24 13:36:24.228 [info] > git hash-object -w --stdin [91ms]
2025-05-24 13:36:24.229 [info] > git hash-object -w --stdin [115ms]
2025-05-24 13:36:24.230 [info] > git hash-object -w --stdin [144ms]
2025-05-24 13:36:24.230 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/manager/ManagerPage.tsx [171ms]
2025-05-24 13:36:24.230 [info] > git hash-object -w --stdin [197ms]
2025-05-24 13:36:24.231 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- server/routes.ts [229ms]
2025-05-24 13:36:24.231 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- server/auth.ts [258ms]
2025-05-24 13:36:24.232 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- server/admin-api.ts [285ms]
2025-05-24 13:36:24.232 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- cookies.txt [315ms]
2025-05-24 13:36:24.232 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/manager/ManagerLayout.tsx [342ms]
2025-05-24 13:36:24.232 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/driver/DriverLayout.tsx [368ms]
2025-05-24 13:36:24.232 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/driver/DriverPage.tsx [398ms]
2025-05-24 13:36:24.233 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/auth/LogoutPage.tsx [428ms]
2025-05-24 13:36:24.233 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/Checkout.tsx [467ms]
2025-05-24 13:36:24.233 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/auth/AuthPage.tsx [501ms]
2025-05-24 13:36:24.233 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/context/RestaurantStatusContext.tsx [525ms]
2025-05-24 13:36:24.233 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/App.tsx [550ms]
2025-05-24 13:36:24.407 [info] > git hash-object -w --stdin [184ms]
2025-05-24 13:36:24.438 [info] > git config --get commit.template [32ms]
2025-05-24 13:36:24.438 [info] > git config --get commit.template [63ms]
2025-05-24 13:36:24.438 [info] > git config --get commit.template [85ms]
2025-05-24 13:36:24.439 [info] > git config --get commit.template [110ms]
2025-05-24 13:36:24.439 [info] > git config --get commit.template [146ms]
2025-05-24 13:36:24.439 [info] > git config --get commit.template [174ms]
2025-05-24 13:36:24.450 [info] > git config --get commit.template [13ms]
2025-05-24 13:36:24.479 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:36:24.513 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:36:24.544 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:36:24.569 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:36:24.596 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:36:24.626 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:36:24.656 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:36:24.661 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [36ms]
2025-05-24 13:36:24.695 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [99ms]
2025-05-24 13:36:24.727 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [158ms]
2025-05-24 13:36:24.752 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [208ms]
2025-05-24 13:36:24.775 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [262ms]
2025-05-24 13:36:24.801 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [322ms]
2025-05-24 13:36:24.828 [info] > git hash-object -w --stdin [4ms]
2025-05-24 13:36:24.828 [info] > git hash-object -w --stdin [27ms]
2025-05-24 13:36:24.829 [info] > git hash-object -w --stdin [54ms]
2025-05-24 13:36:24.830 [info] > git hash-object -w --stdin [103ms]
2025-05-24 13:36:24.830 [info] > git hash-object -w --stdin [135ms]
2025-05-24 13:36:24.832 [info] > git hash-object -w --stdin [80ms]
2025-05-24 13:36:24.833 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [177ms]
2025-05-24 13:36:25.029 [info] > git config --get commit.template [33ms]
2025-05-24 13:36:25.029 [info] > git config --get commit.template [60ms]
2025-05-24 13:36:25.029 [info] > git config --get commit.template [86ms]
2025-05-24 13:36:25.030 [info] > git config --get commit.template [113ms]
2025-05-24 13:36:25.030 [info] > git config --get commit.template [143ms]
2025-05-24 13:36:25.030 [info] > git hash-object -w --stdin [171ms]
2025-05-24 13:36:25.063 [info] > git config --get commit.template [41ms]
2025-05-24 13:36:25.090 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:36:25.116 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:36:25.140 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:36:25.162 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:36:25.189 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:36:25.218 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:36:25.290 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [72ms]
2025-05-24 13:36:25.316 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [127ms]
2025-05-24 13:36:25.339 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [177ms]
2025-05-24 13:36:25.362 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [222ms]
2025-05-24 13:36:25.388 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [272ms]
2025-05-24 13:36:25.419 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [329ms]
2025-05-24 13:36:25.447 [info] > git config --get commit.template [385ms]
2025-05-24 13:36:25.478 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:36:25.479 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 7799d8a51313aaf146b2aa1409e102c36691c508 [60ms]
2025-05-24 13:36:25.480 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 4ee0dfd88d34fcf8174d114a98258be24124d5ee [92ms]
2025-05-24 13:36:25.480 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 c229f1549b6c43cc25ca236283e8433e774dbad9 [118ms]
2025-05-24 13:36:25.480 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 25eda2e8a0a86065a3e4a1d5edc663e67f7b8c39 [141ms]
2025-05-24 13:36:25.480 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 635b2abac91212da2bae3ca8263bd5ae06baba45 [164ms]
2025-05-24 13:36:25.539 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [61ms]
2025-05-24 13:36:25.586 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 f45b58453d90a2f7c53517f142627fc254b4a369 [139ms]
2025-05-24 13:36:25.594 [info] > git status -z -uall [31ms]
2025-05-24 13:36:25.597 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [11ms]
2025-05-24 13:36:25.631 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 fc4d0b0f596583cd89f41cffcb34defdcc13c733 [9ms]
2025-05-24 13:36:25.984 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [61ms]
2025-05-24 13:36:26.965 [info] > git config --get commit.template [2ms]
2025-05-24 13:36:27.012 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:36:27.015 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 13:36:27.088 [info] > git status -z -uall [24ms]
2025-05-24 13:36:27.089 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:36:27.420 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:36:32.126 [info] > git config --get commit.template [3ms]
2025-05-24 13:36:32.153 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:36:32.155 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:36:32.203 [info] > git status -z -uall [26ms]
2025-05-24 13:36:32.205 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:36:32.552 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 13:36:37.235 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:36:37.262 [info] > git config --get commit.template [28ms]
2025-05-24 13:36:37.263 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:36:37.312 [info] > git status -z -uall [26ms]
2025-05-24 13:36:37.313 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:36:37.642 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:53:23.372 [info] > git fetch [31ms]
2025-05-24 13:53:23.398 [info] > git config --get commit.template [28ms]
2025-05-24 13:53:23.428 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:53:23.429 [info] > git config --get commit.template [34ms]
2025-05-24 13:53:23.453 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:53:23.454 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [26ms]
2025-05-24 13:53:23.455 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:53:23.515 [info] > git status -z -uall [34ms]
2025-05-24 13:53:23.518 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 13:53:23.850 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:53:28.495 [info] > git config --get commit.template [4ms]
2025-05-24 13:53:28.527 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:53:28.528 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:53:28.591 [info] > git status -z -uall [36ms]
2025-05-24 13:53:28.593 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:53:28.923 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 13:54:22.368 [info] > git config --get commit.template [2ms]
2025-05-24 13:54:22.369 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:54:22.403 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 13:54:22.462 [info] > git status -z -uall [29ms]
2025-05-24 13:54:22.465 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 13:54:22.800 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:56:00.626 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:56:00.656 [info] > git config --get commit.template [30ms]
2025-05-24 13:56:00.657 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:56:00.706 [info] > git status -z -uall [24ms]
2025-05-24 13:56:00.708 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:56:01.038 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 13:56:05.762 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:56:05.763 [info] > git config --get commit.template [28ms]
2025-05-24 13:56:05.764 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:56:05.819 [info] > git status -z -uall [26ms]
2025-05-24 13:56:05.820 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:56:06.146 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:56:12.569 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:56:12.570 [info] > git config --get commit.template [22ms]
2025-05-24 13:56:12.571 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:56:12.625 [info] > git status -z -uall [30ms]
2025-05-24 13:56:12.626 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 13:56:12.989 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [10ms]
2025-05-24 13:56:17.683 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:56:17.684 [info] > git config --get commit.template [24ms]
2025-05-24 13:56:17.684 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:56:17.749 [info] > git status -z -uall [27ms]
2025-05-24 13:56:17.750 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:56:18.084 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:56:22.919 [info] > git config --get commit.template [2ms]
2025-05-24 13:56:22.953 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:56:22.959 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-05-24 13:56:23.040 [info] > git status -z -uall [45ms]
2025-05-24 13:56:23.044 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-05-24 13:56:23.381 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:56:23.578 [info] > git fetch [14ms]
2025-05-24 13:56:23.668 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:56:23.668 [info] > git config --get commit.template [43ms]
2025-05-24 13:56:23.672 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-05-24 13:56:23.752 [info] > git status -z -uall [48ms]
2025-05-24 13:56:23.754 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [16ms]
2025-05-24 13:56:24.099 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:56:28.098 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:56:28.099 [info] > git config --get commit.template [25ms]
2025-05-24 13:56:28.100 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:56:28.152 [info] > git status -z -uall [24ms]
2025-05-24 13:56:28.153 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 13:56:28.479 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:56:33.214 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:56:33.214 [info] > git config --get commit.template [25ms]
2025-05-24 13:56:33.215 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:56:33.277 [info] > git status -z -uall [27ms]
2025-05-24 13:56:33.278 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:56:33.617 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-05-24 13:56:38.341 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:56:38.343 [info] > git config --get commit.template [38ms]
2025-05-24 13:56:38.344 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 13:56:38.413 [info] > git status -z -uall [37ms]
2025-05-24 13:56:38.415 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:56:38.747 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:56:43.450 [info] > git config --get commit.template [2ms]
2025-05-24 13:56:43.489 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:56:43.492 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 13:56:43.588 [info] > git status -z -uall [58ms]
2025-05-24 13:56:43.588 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 13:56:43.935 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [11ms]
2025-05-24 13:56:44.321 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:56:44.976 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/App.tsx [590ms]
2025-05-24 13:56:44.976 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/context/RestaurantStatusContext.tsx [563ms]
2025-05-24 13:56:44.976 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/Checkout.tsx [536ms]
2025-05-24 13:56:44.976 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/auth/AuthPage.tsx [509ms]
2025-05-24 13:56:44.976 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/auth/LogoutPage.tsx [482ms]
2025-05-24 13:56:44.976 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/driver/DriverLayout.tsx [455ms]
2025-05-24 13:56:44.976 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/driver/DriverPage.tsx [423ms]
2025-05-24 13:56:44.976 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/manager/ManagerLayout.tsx [397ms]
2025-05-24 13:56:44.976 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/manager/ManagerPage.tsx [373ms]
2025-05-24 13:56:44.976 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:cookies.txt [349ms]
2025-05-24 13:56:44.977 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:server/admin-api.ts [319ms]
2025-05-24 13:56:44.977 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:server/auth.ts [287ms]
2025-05-24 13:56:44.977 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:server/routes.ts [258ms]
2025-05-24 13:56:44.977 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/components/NotificationDropdown.tsx [219ms]
2025-05-24 13:56:44.977 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/context/NotificationContext.tsx [182ms]
2025-05-24 13:56:44.977 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/hooks/useAudioNotification.ts [157ms]
2025-05-24 13:56:44.977 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/RealOrderTracker.tsx [128ms]
2025-05-24 13:56:44.977 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/utils/orderStatusWorkflow.ts [81ms]
2025-05-24 13:56:44.977 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:cookies2.txt [44ms]
2025-05-24 13:56:44.977 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:public/sounds/README.md [8ms]
2025-05-24 13:56:45.199 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [42ms]
2025-05-24 13:56:45.242 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [117ms]
2025-05-24 13:56:45.277 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [183ms]
2025-05-24 13:56:45.323 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [264ms]
2025-05-24 13:56:45.383 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [359ms]
2025-05-24 13:56:45.486 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [291ms]
2025-05-24 13:56:45.541 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [68ms]
2025-05-24 13:56:46.306 [info] > git hash-object -w --stdin [7ms]
2025-05-24 13:56:46.307 [info] > git hash-object -w --stdin [43ms]
2025-05-24 13:56:46.307 [info] > git hash-object -w --stdin [88ms]
2025-05-24 13:56:46.308 [info] > git hash-object -w --stdin [127ms]
2025-05-24 13:56:46.308 [info] > git hash-object -w --stdin [161ms]
2025-05-24 13:56:46.318 [info] > git hash-object -w --stdin [208ms]
2025-05-24 13:56:46.319 [info] > git hash-object -w --stdin [245ms]
2025-05-24 13:56:46.320 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- server/routes.ts [274ms]
2025-05-24 13:56:46.321 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- server/auth.ts [319ms]
2025-05-24 13:56:46.321 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- server/admin-api.ts [354ms]
2025-05-24 13:56:46.321 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- cookies.txt [383ms]
2025-05-24 13:56:46.321 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/manager/ManagerLayout.tsx [424ms]
2025-05-24 13:56:46.321 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/manager/ManagerPage.tsx [456ms]
2025-05-24 13:56:46.321 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/driver/DriverPage.tsx [497ms]
2025-05-24 13:56:46.322 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/driver/DriverLayout.tsx [540ms]
2025-05-24 13:56:46.322 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/auth/LogoutPage.tsx [573ms]
2025-05-24 13:56:46.322 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/auth/AuthPage.tsx [609ms]
2025-05-24 13:56:46.322 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/context/RestaurantStatusContext.tsx [634ms]
2025-05-24 13:56:46.322 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/Checkout.tsx [667ms]
2025-05-24 13:56:46.322 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/App.tsx [702ms]
2025-05-24 13:56:46.585 [info] > git config --get commit.template [88ms]
2025-05-24 13:56:46.586 [info] > git config --get commit.template [116ms]
2025-05-24 13:56:46.586 [info] > git config --get commit.template [142ms]
2025-05-24 13:56:46.586 [info] > git config --get commit.template [167ms]
2025-05-24 13:56:46.586 [info] > git config --get commit.template [198ms]
2025-05-24 13:56:46.586 [info] > git config --get commit.template [228ms]
2025-05-24 13:56:46.588 [info] > git config --get commit.template [64ms]
2025-05-24 13:56:46.615 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:56:46.646 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:56:46.672 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:56:46.696 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:56:46.725 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:56:46.758 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:56:46.783 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:56:46.784 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [26ms]
2025-05-24 13:56:46.830 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [105ms]
2025-05-24 13:56:46.859 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [164ms]
2025-05-24 13:56:46.890 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [218ms]
2025-05-24 13:56:46.919 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [273ms]
2025-05-24 13:56:46.951 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [336ms]
2025-05-24 13:56:46.994 [info] > git hash-object -w --stdin [104ms]
2025-05-24 13:56:46.995 [info] > git hash-object -w --stdin [136ms]
2025-05-24 13:56:46.995 [info] > git hash-object -w --stdin [165ms]
2025-05-24 13:56:46.996 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [214ms]
2025-05-24 13:56:47.105 [info] > git hash-object -w --stdin [186ms]
2025-05-24 13:56:47.105 [info] > git hash-object -w --stdin [124ms]
2025-05-24 13:56:47.106 [info] > git hash-object -w --stdin [155ms]
2025-05-24 13:56:47.189 [info] > git config --get commit.template [85ms]
2025-05-24 13:56:47.189 [info] > git config --get commit.template [115ms]
2025-05-24 13:56:47.190 [info] > git config --get commit.template [139ms]
2025-05-24 13:56:47.190 [info] > git hash-object -w --stdin [166ms]
2025-05-24 13:56:47.283 [info] > git config --get commit.template [95ms]
2025-05-24 13:56:47.284 [info] > git config --get commit.template [123ms]
2025-05-24 13:56:47.284 [info] > git config --get commit.template [150ms]
2025-05-24 13:56:47.306 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:56:47.327 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:56:47.349 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:56:47.355 [info] > git config --get commit.template [135ms]
2025-05-24 13:56:47.377 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:56:47.399 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:56:47.400 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:56:47.419 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [70ms]
2025-05-24 13:56:47.444 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [117ms]
2025-05-24 13:56:47.466 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [160ms]
2025-05-24 13:56:47.513 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:56:47.519 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 635b2abac91212da2bae3ca8263bd5ae06baba45 [53ms]
2025-05-24 13:56:47.519 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 c229f1549b6c43cc25ca236283e8433e774dbad9 [75ms]
2025-05-24 13:56:47.519 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [101ms]
2025-05-24 13:56:47.545 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [146ms]
2025-05-24 13:56:47.579 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [202ms]
2025-05-24 13:56:47.615 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 fc4d0b0f596583cd89f41cffcb34defdcc13c733 [36ms]
2025-05-24 13:56:47.616 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 f45b58453d90a2f7c53517f142627fc254b4a369 [71ms]
2025-05-24 13:56:47.616 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [103ms]
2025-05-24 13:56:47.661 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 4ee0dfd88d34fcf8174d114a98258be24124d5ee [171ms]
2025-05-24 13:56:47.665 [info] > git status -z -uall [25ms]
2025-05-24 13:56:47.665 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 7799d8a51313aaf146b2aa1409e102c36691c508 [54ms]
2025-05-24 13:56:47.666 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-05-24 13:56:47.694 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 25eda2e8a0a86065a3e4a1d5edc663e67f7b8c39 [5ms]
2025-05-24 13:56:48.055 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 13:56:48.623 [info] > git config --get commit.template [0ms]
2025-05-24 13:56:48.649 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:56:48.651 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:56:48.699 [info] > git status -z -uall [23ms]
2025-05-24 13:56:48.700 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 13:56:49.025 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:56:53.812 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:56:53.813 [info] > git config --get commit.template [48ms]
2025-05-24 13:56:53.816 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 13:56:53.987 [info] > git status -z -uall [105ms]
2025-05-24 13:56:53.988 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 13:56:54.363 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:56:59.081 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:56:59.081 [info] > git config --get commit.template [38ms]
2025-05-24 13:56:59.083 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 13:56:59.152 [info] > git status -z -uall [38ms]
2025-05-24 13:56:59.155 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 13:56:59.482 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:57:04.208 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:57:04.208 [info] > git config --get commit.template [25ms]
2025-05-24 13:57:04.209 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:57:04.245 [info] > git status -z -uall [18ms]
2025-05-24 13:57:04.246 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:57:04.614 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:57:09.334 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:57:09.340 [info] > git config --get commit.template [48ms]
2025-05-24 13:57:09.342 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-05-24 13:57:09.415 [info] > git status -z -uall [35ms]
2025-05-24 13:57:09.417 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 13:57:09.754 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 13:57:14.477 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:57:14.477 [info] > git config --get commit.template [28ms]
2025-05-24 13:57:14.479 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:57:14.532 [info] > git status -z -uall [25ms]
2025-05-24 13:57:14.533 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 13:57:14.862 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:57:19.609 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:57:19.610 [info] > git config --get commit.template [47ms]
2025-05-24 13:57:19.611 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:57:19.664 [info] > git status -z -uall [29ms]
2025-05-24 13:57:19.665 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-05-24 13:57:19.998 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:57:54.919 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:57:54.919 [info] > git config --get commit.template [31ms]
2025-05-24 13:57:54.920 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:57:54.970 [info] > git status -z -uall [24ms]
2025-05-24 13:57:54.975 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-05-24 13:57:55.303 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:58:00.028 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:58:00.029 [info] > git config --get commit.template [27ms]
2025-05-24 13:58:00.030 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:58:00.083 [info] > git status -z -uall [25ms]
2025-05-24 13:58:00.085 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:58:00.414 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:58:05.141 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:58:05.141 [info] > git config --get commit.template [23ms]
2025-05-24 13:58:05.142 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:58:05.204 [info] > git status -z -uall [33ms]
2025-05-24 13:58:05.205 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:58:05.534 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:58:10.267 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:58:10.267 [info] > git config --get commit.template [29ms]
2025-05-24 13:58:10.268 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:58:10.328 [info] > git status -z -uall [31ms]
2025-05-24 13:58:10.329 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 13:58:10.657 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:58:15.377 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:58:15.377 [info] > git config --get commit.template [23ms]
2025-05-24 13:58:15.378 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:58:15.423 [info] > git status -z -uall [22ms]
2025-05-24 13:58:15.425 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 13:58:15.757 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:58:20.479 [info] > git config --get commit.template [3ms]
2025-05-24 13:58:20.518 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:58:20.521 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 13:58:20.594 [info] > git status -z -uall [36ms]
2025-05-24 13:58:20.594 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-05-24 13:58:20.936 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [9ms]
2025-05-24 13:58:25.653 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:58:25.654 [info] > git config --get commit.template [26ms]
2025-05-24 13:58:25.654 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 13:58:25.726 [info] > git status -z -uall [26ms]
2025-05-24 13:58:25.727 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 13:58:26.053 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 13:58:30.792 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 13:58:30.793 [info] > git config --get commit.template [28ms]
2025-05-24 13:58:30.797 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-05-24 13:58:30.868 [info] > git status -z -uall [43ms]
2025-05-24 13:58:30.870 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 13:58:31.206 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 14:00:08.467 [info] > git fetch [31ms]
2025-05-24 14:00:08.496 [info] > git config --get commit.template [29ms]
2025-05-24 14:00:08.522 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 14:00:08.523 [info] > git config --get commit.template [27ms]
2025-05-24 14:00:08.550 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 14:00:08.551 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [29ms]
2025-05-24 14:00:08.553 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 14:00:08.610 [info] > git status -z -uall [32ms]
2025-05-24 14:00:08.611 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 14:00:08.942 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 14:00:09.346 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 14:00:09.987 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/App.tsx [552ms]
2025-05-24 14:00:09.987 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/context/RestaurantStatusContext.tsx [515ms]
2025-05-24 14:00:09.987 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/Checkout.tsx [484ms]
2025-05-24 14:00:09.987 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/auth/AuthPage.tsx [454ms]
2025-05-24 14:00:09.987 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/auth/LogoutPage.tsx [425ms]
2025-05-24 14:00:09.987 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/driver/DriverLayout.tsx [404ms]
2025-05-24 14:00:09.987 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/driver/DriverPage.tsx [367ms]
2025-05-24 14:00:09.987 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/manager/ManagerLayout.tsx [340ms]
2025-05-24 14:00:09.987 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/manager/ManagerPage.tsx [313ms]
2025-05-24 14:00:09.987 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:cookies.txt [281ms]
2025-05-24 14:00:09.987 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:server/admin-api.ts [252ms]
2025-05-24 14:00:09.987 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:server/auth.ts [227ms]
2025-05-24 14:00:09.987 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:server/routes.ts [203ms]
2025-05-24 14:00:09.987 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/components/NotificationDropdown.tsx [171ms]
2025-05-24 14:00:09.987 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/context/NotificationContext.tsx [145ms]
2025-05-24 14:00:09.987 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/hooks/useAudioNotification.ts [123ms]
2025-05-24 14:00:09.987 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/pages/RealOrderTracker.tsx [96ms]
2025-05-24 14:00:09.987 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:client/src/utils/orderStatusWorkflow.ts [60ms]
2025-05-24 14:00:09.987 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:cookies2.txt [34ms]
2025-05-24 14:00:09.987 [info] > git show --textconv 8d1a5f0b0b487f0e778b9c59a76faedc1543648a:public/sounds/README.md [10ms]
2025-05-24 14:00:10.173 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [34ms]
2025-05-24 14:00:10.201 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [98ms]
2025-05-24 14:00:10.240 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [160ms]
2025-05-24 14:00:10.268 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [216ms]
2025-05-24 14:00:10.293 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [269ms]
2025-05-24 14:00:10.375 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [205ms]
2025-05-24 14:00:10.414 [info] > git ls-tree -l -r 8d1a5f0b0b487f0e778b9c59a76faedc1543648a [65ms]
2025-05-24 14:00:11.060 [info] > git hash-object -w --stdin [84ms]
2025-05-24 14:00:11.061 [info] > git hash-object -w --stdin [108ms]
2025-05-24 14:00:11.061 [info] > git hash-object -w --stdin [134ms]
2025-05-24 14:00:11.061 [info] > git hash-object -w --stdin [159ms]
2025-05-24 14:00:11.062 [info] > git hash-object -w --stdin [208ms]
2025-05-24 14:00:11.062 [info] > git hash-object -w --stdin [235ms]
2025-05-24 14:00:11.062 [info] > git hash-object -w --stdin [261ms]
2025-05-24 14:00:11.063 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- server/routes.ts [288ms]
2025-05-24 14:00:11.063 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- server/auth.ts [307ms]
2025-05-24 14:00:11.063 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- server/admin-api.ts [330ms]
2025-05-24 14:00:11.063 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- cookies.txt [360ms]
2025-05-24 14:00:11.064 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/manager/ManagerPage.tsx [386ms]
2025-05-24 14:00:11.064 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/manager/ManagerLayout.tsx [414ms]
2025-05-24 14:00:11.064 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/driver/DriverPage.tsx [443ms]
2025-05-24 14:00:11.064 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/driver/DriverLayout.tsx [467ms]
2025-05-24 14:00:11.064 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/auth/LogoutPage.tsx [492ms]
2025-05-24 14:00:11.065 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/auth/AuthPage.tsx [516ms]
2025-05-24 14:00:11.065 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/pages/Checkout.tsx [543ms]
2025-05-24 14:00:11.065 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/context/RestaurantStatusContext.tsx [578ms]
2025-05-24 14:00:11.065 [info] > git diff 8d1a5f0b0b487f0e778b9c59a76faedc1543648a -- client/src/App.tsx [599ms]
2025-05-24 14:00:11.275 [info] > git config --get commit.template [31ms]
2025-05-24 14:00:11.275 [info] > git config --get commit.template [64ms]
2025-05-24 14:00:11.276 [info] > git config --get commit.template [91ms]
2025-05-24 14:00:11.276 [info] > git config --get commit.template [116ms]
2025-05-24 14:00:11.276 [info] > git config --get commit.template [156ms]
2025-05-24 14:00:11.276 [info] > git config --get commit.template [180ms]
2025-05-24 14:00:11.277 [info] > git config --get commit.template [8ms]
2025-05-24 14:00:11.309 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 14:00:11.309 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 14:00:11.365 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 14:00:11.423 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 14:00:11.472 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 14:00:11.532 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 14:00:11.563 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 14:00:11.570 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [38ms]
2025-05-24 14:00:11.598 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [126ms]
2025-05-24 14:00:11.632 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [209ms]
2025-05-24 14:00:11.657 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [292ms]
2025-05-24 14:00:11.680 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [339ms]
2025-05-24 14:00:11.707 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [398ms]
2025-05-24 14:00:11.746 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [183ms]
2025-05-24 14:00:11.799 [info] > git status -z -uall [26ms]
2025-05-24 14:00:11.799 [info] > git hash-object -w --stdin [93ms]
2025-05-24 14:00:11.800 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms] (cancelled)
2025-05-24 14:00:11.833 [info] > git hash-object -w --stdin [153ms]
2025-05-24 14:00:11.834 [info] > git hash-object -w --stdin [96ms]
2025-05-24 14:00:11.835 [info] > git hash-object -w --stdin [178ms]
2025-05-24 14:00:11.835 [info] > git hash-object -w --stdin [203ms]
2025-05-24 14:00:11.836 [info] > git hash-object -w --stdin [238ms]
2025-05-24 14:00:12.069 [info] > git config --get commit.template [90ms]
2025-05-24 14:00:12.069 [info] > git config --get commit.template [114ms]
2025-05-24 14:00:12.070 [info] > git config --get commit.template [148ms]
2025-05-24 14:00:12.070 [info] > git config --get commit.template [187ms]
2025-05-24 14:00:12.070 [info] > git config --get commit.template [208ms]
2025-05-24 14:00:12.070 [info] > git hash-object -w --stdin [238ms]
2025-05-24 14:00:12.126 [info] > git config --get commit.template [120ms]
2025-05-24 14:00:12.173 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 14:00:12.233 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 14:00:12.283 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 14:00:12.317 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 14:00:12.363 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 14:00:12.392 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 14:00:12.393 [info] > git config --get commit.template [269ms]
2025-05-24 14:00:12.454 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [62ms]
2025-05-24 14:00:12.481 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [118ms]
2025-05-24 14:00:12.512 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [195ms]
2025-05-24 14:00:12.540 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [257ms]
2025-05-24 14:00:12.569 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [336ms]
2025-05-24 14:00:12.592 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [419ms]
2025-05-24 14:00:12.640 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 14:00:12.646 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 7799d8a51313aaf146b2aa1409e102c36691c508 [54ms]
2025-05-24 14:00:12.647 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 fc4d0b0f596583cd89f41cffcb34defdcc13c733 [77ms]
2025-05-24 14:00:12.647 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 4ee0dfd88d34fcf8174d114a98258be24124d5ee [107ms]
2025-05-24 14:00:12.647 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 635b2abac91212da2bae3ca8263bd5ae06baba45 [136ms]
2025-05-24 14:00:12.647 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 c229f1549b6c43cc25ca236283e8433e774dbad9 [167ms]
2025-05-24 14:00:12.648 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [8ms]
2025-05-24 14:00:12.694 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 f45b58453d90a2f7c53517f142627fc254b4a369 [76ms]
2025-05-24 14:00:12.699 [info] > git status -z -uall [28ms]
2025-05-24 14:00:12.700 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-05-24 14:00:12.731 [info] > git diff e69de29bb2d1d6434b8b29ae775ad8c2e48c5391 25eda2e8a0a86065a3e4a1d5edc663e67f7b8c39 [5ms]
2025-05-24 14:00:13.045 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [11ms]
2025-05-24 14:00:13.667 [info] > git config --get commit.template [73ms]
2025-05-24 14:00:13.705 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 14:00:13.708 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 14:00:13.778 [info] > git status -z -uall [33ms]
2025-05-24 14:00:13.779 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-05-24 14:00:14.111 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 14:00:34.765 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 14:00:34.766 [info] > git config --get commit.template [25ms]
2025-05-24 14:00:34.767 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 14:00:34.831 [info] > git status -z -uall [42ms]
2025-05-24 14:00:34.832 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 14:00:35.158 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 14:00:39.885 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 14:00:39.885 [info] > git config --get commit.template [25ms]
2025-05-24 14:00:39.886 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 14:00:39.945 [info] > git status -z -uall [28ms]
2025-05-24 14:00:39.946 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 14:00:40.281 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
