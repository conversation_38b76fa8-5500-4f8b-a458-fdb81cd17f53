import { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  ChefHat,
  Truck,
  CheckCircle,
  Clock,
  Flame,
  ShoppingBag,
  Package,
  Bell,
  AlertTriangle
} from 'lucide-react';
import ManagerLayout from './ManagerLayout';
import { format, parseISO } from 'date-fns';
import { useNotifications } from '@/context/NotificationContext';
import { useAudioNotification } from '@/hooks/useAudioNotification';
import {
  ORDER_STATUSES,
  ORDER_TYPES,
  getNextStatus,
  getStatusLabel,
  getStatusColor,
  isManagerOrder,
  requiresDriverAssignment
} from '@/utils/orderStatusWorkflow';

// Use centralized status and type constants
const STATUS = ORDER_STATUSES;
const ORDER_TYPE = ORDER_TYPES;

// Format currency
const formatCurrency = (amount: number) => {
  return `$${(amount / 100).toFixed(2)}`;
};

// Format date for display
const formatDate = (dateString: string) => {
  try {
    const date = parseISO(dateString);
    return format(date, 'MMM d, h:mm a');
  } catch (e) {
    return dateString;
  }
};

// Format time elapsed since order was placed
const formatTimeElapsed = (dateString: string) => {
  try {
    const orderDate = parseISO(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - orderDate.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes === 1) return '1 minute ago';
    if (diffInMinutes < 60) return `${diffInMinutes} minutes ago`;

    const hours = Math.floor(diffInMinutes / 60);
    if (hours === 1) return '1 hour ago';
    return `${hours} hours ago`;
  } catch (e) {
    return 'Unknown time';
  }
};

interface OrderItem {
  id: number;
  name: string;
  price: number;
  quantity: number;
}

interface OrderCustomer {
  firstName: string;
  lastName: string;
  phone: string;
  email: string;
  address?: string;
  postalCode?: string;
  city?: string;
}

interface OrderDetails {
  type?: 'delivery' | 'takeaway';
  time?: 'asap' | 'scheduled';
  scheduledTime?: string | null;
}

interface Order {
  id: number;
  createdAt: string;
  status: string;
  items: OrderItem[];
  customer: OrderCustomer;
  orderDetails?: OrderDetails;
  subtotal: number;
  deliveryFee: number;
  total: number;
  paymentMethod: string;
  notes: string | null;
}

// Status Badge Component
const StatusBadge = ({ status }: { status: string }) => {
  let color = '';
  let icon = <AlertTriangle className="w-4 h-4" />;
  let label = status.replace(/_/g, ' ');

  switch (status) {
    case STATUS.CONFIRMED:
      color = 'bg-blue-900/40 border-blue-700/30 text-blue-400';
      icon = <Bell className="w-4 h-4" />;
      label = 'Confirmed';
      break;
    case STATUS.PROCESSING:
    case STATUS.PREPARING:
      color = 'bg-orange-900/40 border-orange-700/30 text-orange-400';
      icon = <Flame className="w-4 h-4" />;
      label = 'Preparing';
      break;
    case STATUS.READY_FOR_PICKUP:
      color = 'bg-green-900/40 border-green-700/30 text-green-400';
      icon = <Package className="w-4 h-4" />;
      label = 'Ready for Pickup';
      break;
    case STATUS.READY_FOR_DELIVERY:
      color = 'bg-green-900/40 border-green-700/30 text-green-400';
      icon = <Truck className="w-4 h-4" />;
      label = 'Ready for Delivery';
      break;
    case STATUS.COMPLETED:
      color = 'bg-teal-900/40 border-teal-700/30 text-teal-400';
      icon = <CheckCircle className="w-4 h-4" />;
      label = 'Completed';
      break;
  }

  return (
    <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${color}`}>
      <span className="mr-1.5">{icon}</span>
      {label}
    </div>
  );
};

// Action Button Component
const ActionButton = ({
  order,
  onStatusUpdate,
  onDispatchToDriver,
  isLoading,
  isDispatched = false
}: {
  order: Order;
  onStatusUpdate: (orderId: number, newStatus: string) => void;
  onDispatchToDriver: (orderId: number) => void;
  isLoading: boolean;
  isDispatched?: boolean;
}) => {
  // Get next action based on current status and order type using workflow system
  const getNextAction = () => {
    const { status, orderDetails } = order;
    const orderType = orderDetails?.type || ORDER_TYPE.TAKEAWAY;
    const nextStatus = getNextStatus(status, orderType);

    if (!nextStatus) return null;

    // Special handling for dispatch to driver
    if (requiresDriverAssignment(status)) {
      if (isDispatched) {
        // Order has been dispatched but driver hasn't accepted yet
        return {
          nextStatus: "waiting",
          label: 'Awaiting Driver',
          color: 'bg-yellow-900/50 border-yellow-700 text-yellow-400 cursor-not-allowed',
          icon: <Clock className="w-5 h-5 mr-2" />,
          disabled: true
        };
      } else {
        return {
          nextStatus: "dispatch",
          label: 'Dispatch to Driver',
          color: 'bg-cyan-900/50 hover:bg-cyan-800 border-cyan-700 text-cyan-400 hover:text-white shadow-[0_0_15px_rgba(6,182,212,0.3)]',
          icon: <Truck className="w-5 h-5 mr-2" />
        };
      }
    }

    // Get action details based on next status
    const getActionDetails = (nextStatus: string) => {
      switch (nextStatus) {
        case STATUS.PREPARING:
          return {
            label: 'Start Preparing',
            color: 'bg-orange-900/50 hover:bg-orange-800 border-orange-700 text-orange-400 hover:text-white shadow-[0_0_15px_rgba(249,115,22,0.3)]',
            icon: <Flame className="w-5 h-5 mr-2" />
          };
        case STATUS.READY_FOR_PICKUP:
          return {
            label: 'Ready for Pickup',
            color: 'bg-green-900/50 hover:bg-green-800 border-green-700 text-green-400 hover:text-white shadow-[0_0_15px_rgba(34,197,94,0.3)]',
            icon: <Package className="w-5 h-5 mr-2" />
          };
        case STATUS.READY_FOR_DELIVERY:
          return {
            label: 'Ready for Delivery',
            color: 'bg-green-900/50 hover:bg-green-800 border-green-700 text-green-400 hover:text-white shadow-[0_0_15px_rgba(34,197,94,0.3)]',
            icon: <Truck className="w-5 h-5 mr-2" />
          };
        case STATUS.COMPLETED:
          return {
            label: 'Mark Completed',
            color: 'bg-teal-900/50 hover:bg-teal-800 border-teal-700 text-teal-400 hover:text-white shadow-[0_0_15px_rgba(20,184,166,0.3)]',
            icon: <CheckCircle className="w-5 h-5 mr-2" />
          };
        default:
          return {
            label: getStatusLabel(nextStatus),
            color: 'bg-gray-900/50 hover:bg-gray-800 border-gray-700 text-gray-400 hover:text-white',
            icon: <Clock className="w-5 h-5 mr-2" />
          };
      }
    };

    return {
      nextStatus,
      ...getActionDetails(nextStatus)
    };
  };

  const action = getNextAction();

  if (!action) return null;

  // Handle action click
  const handleActionClick = () => {
    const { nextStatus } = action;

    if (nextStatus === "dispatch") {
      onDispatchToDriver(order.id);
    } else if (nextStatus !== "waiting") {
      onStatusUpdate(order.id, nextStatus);
    }
  };

  const isDisabled = isLoading || action.disabled;

  return (
    <motion.button
      className={`flex items-center justify-center px-4 py-2.5 rounded-lg font-medium
                  border transition-all ${action.color} disabled:opacity-50 disabled:cursor-not-allowed
                  w-full md:w-auto`}
      onClick={handleActionClick}
      disabled={isDisabled}
      whileHover={!isDisabled ? { scale: 1.05 } : {}}
      whileTap={!isDisabled ? { scale: 0.95 } : {}}
    >
      {isLoading ? (
        <Clock className="w-5 h-5 animate-spin mr-2" />
      ) : (
        action.icon
      )}
      {action.label}
    </motion.button>
  );
};

// Order Card Component
const OrderCard = ({
  order,
  onStatusUpdate,
  onDispatchToDriver,
  isUpdating
}: {
  order: Order;
  onStatusUpdate: (orderId: number, newStatus: string) => void;
  onDispatchToDriver: (orderId: number) => void;
  isUpdating: boolean;
}) => {
  return (
    <motion.div
      className="bg-gray-900/70 rounded-xl border border-gray-800 overflow-hidden backdrop-blur-md shadow-lg"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, scale: 0.95 }}
      transition={{ duration: 0.3 }}
      layout
    >
      {/* Header */}
      <div className="bg-gray-900 p-4 border-b border-gray-800 flex justify-between items-center">
        <div className="flex items-center">
          <span className="text-lg font-medium text-white mr-3">Order #{order.id}</span>
          <StatusBadge status={order.status} />
        </div>

        <div className="flex items-center space-x-3">
          <span
            className={`px-3 py-1 rounded-full text-xs font-medium
                      ${order.orderDetails?.type === ORDER_TYPE.DELIVERY
                        ? 'bg-cyan-900/30 text-cyan-400 border border-cyan-800/30'
                        : 'bg-amber-900/30 text-amber-400 border border-amber-800/30'
                      }`}
          >
            {order.orderDetails?.type === ORDER_TYPE.DELIVERY ? 'Delivery' : 'Takeaway'}
          </span>

          <span className="text-sm text-gray-400">
            {formatTimeElapsed(order.createdAt)}
          </span>
        </div>
      </div>

      {/* Body */}
      <div className="p-4 space-y-4">
        {/* Customer Info */}
        <div className="flex justify-between items-start">
          <div>
            <h3 className="text-white font-medium mb-1">
              {order.customer.firstName} {order.customer.lastName}
            </h3>
            <p className="text-sm text-gray-400">{order.customer.phone}</p>
          </div>

          <div className="text-right">
            <p className="text-sm text-gray-400">
              {order.orderDetails?.time === 'asap'
                ? 'ASAP'
                : (order.orderDetails?.scheduledTime
                    ? `For: ${formatDate(order.orderDetails.scheduledTime)}`
                    : ''
                  )
              }
            </p>
            <p className="text-sm font-medium text-white">{formatCurrency(order.total)}</p>
          </div>
        </div>

        {/* Order Items */}
        <div className="bg-gray-800/50 rounded-lg p-3">
          <h3 className="text-white text-sm font-medium mb-2">Order Items:</h3>
          <ul className="space-y-1.5">
            {order.items.map(item => (
              <li key={item.id} className="text-sm flex justify-between">
                <div>
                  <span className="text-orange-400 font-medium">{item.quantity}x</span>
                  <span className="text-white ml-2">{item.name}</span>
                </div>
                <span className="text-gray-400">{formatCurrency(item.price * item.quantity)}</span>
              </li>
            ))}
          </ul>
        </div>

        {/* Notes */}
        {order.notes && (
          <div className="bg-gray-800/50 rounded-lg p-3">
            <h3 className="text-white text-sm font-medium mb-1">Special Instructions:</h3>
            <p className="text-sm text-gray-300">{order.notes}</p>
          </div>
        )}

        {/* Actions */}
        <div className="flex justify-end">
          <ActionButton
            order={order}
            onStatusUpdate={onStatusUpdate}
            onDispatchToDriver={onDispatchToDriver}
            isLoading={isUpdating}
          />
        </div>
      </div>
    </motion.div>
  );
};

// Main Manager Page Component
const ManagerPage = () => {
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isUpdating, setIsUpdating] = useState(false);
  const [statusFilter, setStatusFilter] = useState('active');
  const [dispatchedOrders, setDispatchedOrders] = useState<Set<number>>(new Set());
  const previousOrdersRef = useRef<Order[]>([]);

  const { addNotification } = useNotifications();
  const { playNotificationSound, preloadSounds } = useAudioNotification();

  // Preload notification sounds on component mount
  useEffect(() => {
    preloadSounds();
  }, [preloadSounds]);

  // Detect new orders and play notification sound
  useEffect(() => {
    if (previousOrdersRef.current.length === 0) {
      // First load, just store the orders
      previousOrdersRef.current = orders;
      return;
    }

    // Check for new orders
    const newOrders = orders.filter(order =>
      !previousOrdersRef.current.some(prevOrder => prevOrder.id === order.id)
    );

    // Check for status updates
    const updatedOrders = orders.filter(order => {
      const prevOrder = previousOrdersRef.current.find(prev => prev.id === order.id);
      return prevOrder && prevOrder.status !== order.status;
    });

    // Handle new orders
    newOrders.forEach(order => {
      playNotificationSound('new_order');
      addNotification({
        type: 'new_order',
        title: 'New Order Received',
        message: `Order #${order.id} from ${order.customerDetails?.name || 'Customer'}`,
        orderId: order.id,
        customerName: order.customerDetails?.name,
        priority: 'high'
      });
    });

    // Handle status updates
    updatedOrders.forEach(order => {
      const prevOrder = previousOrdersRef.current.find(prev => prev.id === order.id);
      if (prevOrder) {
        playNotificationSound('status_update');
        addNotification({
          type: 'status_update',
          title: 'Order Status Updated',
          message: `Order #${order.id} is now ${order.status.replace('_', ' ')}`,
          orderId: order.id,
          customerName: order.customerDetails?.name,
          priority: 'medium'
        });
      }
    });

    // Update the reference
    previousOrdersRef.current = orders;
  }, [orders, playNotificationSound, addNotification]);

  // Fetch orders
  useEffect(() => {
    const fetchOrders = async () => {
      setLoading(true);
      try {
        const response = await fetch('/api/admin/orders');
        if (!response.ok) {
          throw new Error(`Failed to fetch orders: ${response.status}`);
        }

        const allOrders = await response.json();

        // Filter orders based on selected status
        let filteredOrders = allOrders;

        if (statusFilter === 'active') {
          // Active orders are those managed by the manager (not yet dispatched or completed)
          filteredOrders = allOrders.filter((order: Order) =>
            isManagerOrder(order.status)
          );
        } else if (statusFilter !== 'all') {
          filteredOrders = allOrders.filter((order: Order) =>
            order.status === statusFilter
          );
        }

        setOrders(filteredOrders);
        setError(null);
      } catch (err) {
        console.error('Error fetching orders:', err);
        setError('Failed to load orders. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchOrders();

    // Periodically refresh orders (every 30 seconds)
    const interval = setInterval(fetchOrders, 30000);
    return () => clearInterval(interval);
  }, [statusFilter]);

  // Update order status
  const handleStatusUpdate = async (orderId: number, newStatus: string) => {
    setIsUpdating(true);
    try {
      const response = await fetch(`/api/admin/orders/${orderId}/status`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ newStatus })
      });

      if (!response.ok) {
        throw new Error(`Failed to update order status: ${response.status}`);
      }

      // Update state
      setOrders(prevOrders =>
        prevOrders.map(order =>
          order.id === orderId
            ? { ...order, status: newStatus }
            : order
        )
      );

    } catch (err) {
      console.error('Error updating order status:', err);
      setError('Failed to update order status. Please try again.');
    } finally {
      setIsUpdating(false);
    }
  };

  // Dispatch order to driver
  const handleDispatchToDriver = async (orderId: number) => {
    setIsUpdating(true);
    try {
      const order = orders.find(o => o.id === orderId);
      const response = await fetch('/api/admin/dispatch/to-driver', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ orderId })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to dispatch order: ${response.status}`);
      }

      const result = await response.json();
      console.log('Order dispatched successfully:', result);

      // Keep the order in manager view since driver hasn't accepted it yet
      // The order will be removed when driver changes status to 'with_driver'

      // Add notification for successful dispatch
      addNotification({
        type: 'status_update',
        title: 'Order Dispatched',
        message: `Order #${orderId} has been dispatched to a driver and is awaiting acceptance`,
        orderId: orderId,
        customerName: order?.customerDetails?.name,
        priority: 'medium'
      });

    } catch (err) {
      console.error('Error dispatching order to driver:', err);
      const order = orders.find(o => o.id === orderId);
      addNotification({
        type: 'system',
        title: 'Dispatch Failed',
        message: `Failed to dispatch order #${orderId}: ${err.message}`,
        orderId: orderId,
        priority: 'high'
      });
      setError('Failed to dispatch order to driver. Please try again.');
    } finally {
      setIsUpdating(false);
    }
  };

  // Group orders by status for Kanban-style display
  const groupOrdersByStatus = () => {
    const groups = {
      [STATUS.CONFIRMED]: [] as Order[],
      [STATUS.PREPARING]: [] as Order[],
      [STATUS.READY_FOR_PICKUP]: [] as Order[],
      [STATUS.READY_FOR_DELIVERY]: [] as Order[]
    };

    orders.forEach(order => {
      if (groups[order.status as keyof typeof groups]) {
        groups[order.status as keyof typeof groups].push(order);
      }
    });

    return groups;
  };

  const ordersGrouped = groupOrdersByStatus();

  // Count orders by type
  const countByType = (type: string) => {
    return orders.filter(order => order.orderDetails?.type === type).length;
  };

  // Count orders by status
  const countByStatus = (status: string) => {
    return orders.filter(order => order.status === status).length;
  };

  return (
    <ManagerLayout>
      <div className="space-y-6">
        <div className="flex flex-col md:flex-row md:justify-between md:items-center gap-4">
          <div>
            <h1 className="text-2xl font-bold bg-gradient-to-r from-orange-400 via-yellow-400 to-orange-400 text-transparent bg-clip-text mb-2">
              Kitchen Order Management
            </h1>
            <p className="text-gray-400">Manage all incoming orders and update their status</p>
          </div>

          <div className="flex flex-wrap gap-2">
            <div className="bg-orange-900/30 border border-orange-700/30 rounded-lg p-3 text-center min-w-[100px]">
              <div className="text-orange-400 font-medium">Preparing</div>
              <div className="text-2xl font-bold text-white">{countByStatus(STATUS.PREPARING)}</div>
            </div>

            <div className="bg-amber-900/30 border border-amber-700/30 rounded-lg p-3 text-center min-w-[100px]">
              <div className="text-amber-400 font-medium">Takeaway</div>
              <div className="text-2xl font-bold text-white">{countByType(ORDER_TYPE.TAKEAWAY)}</div>
            </div>

            <div className="bg-cyan-900/30 border border-cyan-700/30 rounded-lg p-3 text-center min-w-[100px]">
              <div className="text-cyan-400 font-medium">Delivery</div>
              <div className="text-2xl font-bold text-white">{countByType(ORDER_TYPE.DELIVERY)}</div>
            </div>
          </div>
        </div>

        {/* Filter Controls */}
        <div className="flex justify-between items-center">
          <div className="flex space-x-2">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="bg-gray-900 border border-gray-700 rounded-lg px-3 py-2 text-sm text-white focus:ring-orange-500 focus:border-orange-500"
            >
              <option value="active">Active Orders</option>
              <option value={STATUS.CONFIRMED}>Confirmed Only</option>
              <option value={STATUS.PREPARING}>Preparing Only</option>
              <option value={STATUS.READY_FOR_PICKUP}>Ready for Pickup Only</option>
              <option value={STATUS.READY_FOR_DELIVERY}>Ready for Delivery Only</option>
              <option value="all">All Orders</option>
            </select>
          </div>

          <div className="text-sm text-gray-400">
            Total: {orders.length} order{orders.length !== 1 ? 's' : ''}
          </div>
        </div>

        {error && (
          <div className="bg-red-900/40 border border-red-700 text-red-400 p-4 rounded-lg">
            {error}
          </div>
        )}

        {loading ? (
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500"></div>
          </div>
        ) : orders.length === 0 ? (
          <div className="bg-gray-900/50 border border-gray-800 rounded-xl p-8 text-center">
            <ChefHat className="w-16 h-16 text-gray-700 mx-auto mb-4" />
            <h2 className="text-xl font-medium text-white mb-2">No Orders Found</h2>
            <p className="text-gray-400">
              There are currently no orders matching your filter.
              New orders will appear here automatically.
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 xl:grid-cols-4 gap-6">
            {/* Column 1: Confirmed Orders */}
            <div className="bg-gray-900/50 rounded-xl border border-blue-900/30 p-4">
              <div className="flex items-center mb-4 border-b border-gray-800 pb-3">
                <Bell className="w-5 h-5 text-blue-400 mr-2" />
                <h3 className="text-lg font-medium text-blue-400">New Orders</h3>
                <span className="ml-auto bg-blue-900/50 text-blue-400 text-sm px-2 py-0.5 rounded-full">
                  {ordersGrouped[STATUS.CONFIRMED]?.length || 0}
                </span>
              </div>

              <div className="space-y-4">
                <AnimatePresence>
                  {ordersGrouped[STATUS.CONFIRMED]?.map(order => (
                    <OrderCard
                      key={order.id}
                      order={order}
                      onStatusUpdate={handleStatusUpdate}
                      onDispatchToDriver={handleDispatchToDriver}
                      isUpdating={isUpdating}
                    />
                  ))}
                </AnimatePresence>

                {ordersGrouped[STATUS.CONFIRMED]?.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    <Bell className="w-8 h-8 text-gray-700 mx-auto mb-2" />
                    <p>No new orders</p>
                  </div>
                )}
              </div>
            </div>

            {/* Column 2: Preparing Orders */}
            <div className="bg-gray-900/50 rounded-xl border border-orange-900/30 p-4">
              <div className="flex items-center mb-4 border-b border-gray-800 pb-3">
                <Flame className="w-5 h-5 text-orange-400 mr-2" />
                <h3 className="text-lg font-medium text-orange-400">Preparing</h3>
                <span className="ml-auto bg-orange-900/50 text-orange-400 text-sm px-2 py-0.5 rounded-full">
                  {ordersGrouped[STATUS.PREPARING]?.length || 0}
                </span>
              </div>

              <div className="space-y-4">
                <AnimatePresence>
                  {ordersGrouped[STATUS.PREPARING]?.map(order => (
                    <OrderCard
                      key={order.id}
                      order={order}
                      onStatusUpdate={handleStatusUpdate}
                      onDispatchToDriver={handleDispatchToDriver}
                      isUpdating={isUpdating}
                    />
                  ))}
                </AnimatePresence>

                {ordersGrouped[STATUS.PREPARING]?.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    <Flame className="w-8 h-8 text-gray-700 mx-auto mb-2" />
                    <p>No orders in preparation</p>
                  </div>
                )}
              </div>
            </div>

            {/* Column 3: Ready Orders (Pickup or Delivery) */}
            <div className="bg-gray-900/50 rounded-xl border border-green-900/30 p-4">
              <div className="flex items-center mb-4 border-b border-gray-800 pb-3">
                <Package className="w-5 h-5 text-green-400 mr-2" />
                <h3 className="text-lg font-medium text-green-400">Ready</h3>
                <span className="ml-auto bg-green-900/50 text-green-400 text-sm px-2 py-0.5 rounded-full">
                  {(ordersGrouped[STATUS.READY_FOR_PICKUP]?.length || 0) +
                   (ordersGrouped[STATUS.READY_FOR_DELIVERY]?.length || 0)}
                </span>
              </div>

              <div className="space-y-4">
                <AnimatePresence>
                  {/* Combine both types of ready orders */}
                  {[
                    ...(ordersGrouped[STATUS.READY_FOR_PICKUP] || []),
                    ...(ordersGrouped[STATUS.READY_FOR_DELIVERY] || [])
                  ].map(order => (
                    <OrderCard
                      key={order.id}
                      order={order}
                      onStatusUpdate={handleStatusUpdate}
                      onDispatchToDriver={handleDispatchToDriver}
                      isUpdating={isUpdating}
                    />
                  ))}
                </AnimatePresence>

                {(ordersGrouped[STATUS.READY_FOR_PICKUP]?.length || 0) +
                 (ordersGrouped[STATUS.READY_FOR_DELIVERY]?.length || 0) === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    <Package className="w-8 h-8 text-gray-700 mx-auto mb-2" />
                    <p>No orders ready yet</p>
                  </div>
                )}
              </div>
            </div>

            {/* Column 4: Completed/Archived Orders */}
            <div className="bg-gray-900/50 rounded-xl border border-teal-900/30 p-4">
              <div className="flex items-center mb-4 border-b border-gray-800 pb-3">
                <CheckCircle className="w-5 h-5 text-teal-400 mr-2" />
                <h3 className="text-lg font-medium text-teal-400">Completed</h3>
                <span className="ml-auto bg-teal-900/50 text-teal-400 text-sm px-2 py-0.5 rounded-full">
                  {ordersGrouped[STATUS.COMPLETED]?.length || 0}
                </span>
              </div>

              <div className="space-y-4">
                <AnimatePresence>
                  {ordersGrouped[STATUS.COMPLETED]?.map(order => (
                    <OrderCard
                      key={order.id}
                      order={order}
                      onStatusUpdate={handleStatusUpdate}
                      onDispatchToDriver={handleDispatchToDriver}
                      isUpdating={isUpdating}
                    />
                  ))}
                </AnimatePresence>

                {ordersGrouped[STATUS.COMPLETED]?.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    <CheckCircle className="w-8 h-8 text-gray-700 mx-auto mb-2" />
                    <p>No completed orders</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </ManagerLayout>
  );
};

export default ManagerPage;