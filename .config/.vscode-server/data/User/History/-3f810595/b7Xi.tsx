import { createContext, useContext, useState, ReactNode, useEffect } from "react";
import { AdminSettings } from "@/api/adminApi";

interface RestaurantStatusContextType {
  isOpen: boolean;
  isLoading: boolean;
  businessHours: Record<string, { open: string; close: string; delivery: boolean }>;
  deliveryFee: number;
  estimatedTime: string;
  refreshStatus: () => Promise<void>;
}

const RestaurantStatusContext = createContext<RestaurantStatusContextType | undefined>(undefined);

export const useRestaurantStatus = () => {
  const context = useContext(RestaurantStatusContext);
  if (!context) {
    throw new Error("useRestaurantStatus must be used within a RestaurantStatusProvider");
  }
  return context;
};

interface RestaurantStatusProviderProps {
  children: ReactNode;
}

export const RestaurantStatusProvider = ({ children }: RestaurantStatusProviderProps) => {
  const [isOpen, setIsOpen] = useState<boolean>(true);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [businessHours, setBusinessHours] = useState<Record<string, { open: string; close: string; delivery: boolean }>>({});
  const [deliveryFee, setDeliveryFee] = useState<number>(49);
  const [estimatedTime, setEstimatedTime] = useState<string>("25-35 min");

  const fetchRestaurantStatus = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/settings');
      if (!response.ok) {
        throw new Error('Failed to fetch restaurant status');
      }

      const data: AdminSettings = await response.json();
      setIsOpen(data.restaurant_open);
      setBusinessHours(data.business_hours);
      setDeliveryFee(data.delivery_fee);
      setEstimatedTime(data.estimated_time);
    } catch (error) {
      console.error('Error fetching restaurant status:', error);
      // Use default values if API call fails
      setIsOpen(true);
      setBusinessHours({
        monday: { open: "10:00", close: "22:00", delivery: true },
        tuesday: { open: "10:00", close: "22:00", delivery: true },
        wednesday: { open: "10:00", close: "22:00", delivery: true },
        thursday: { open: "10:00", close: "22:00", delivery: true },
        friday: { open: "10:00", close: "23:00", delivery: true },
        saturday: { open: "10:00", close: "23:00", delivery: true },
        sunday: { open: "12:00", close: "21:00", delivery: true }
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch restaurant status on component mount
  useEffect(() => {
    fetchRestaurantStatus();
  }, []);

  const value = {
    isOpen,
    isLoading,
    businessHours,
    deliveryFee,
    estimatedTime,
    refreshStatus: fetchRestaurantStatus
  };

  return (
    <RestaurantStatusContext.Provider value={value}>
      {children}
    </RestaurantStatusContext.Provider>
  );
};
