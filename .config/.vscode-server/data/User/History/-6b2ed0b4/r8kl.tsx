import { useEffect } from "react";
import { Route, Switch, useLocation } from "wouter";
import { QueryClientProvider } from "@tanstack/react-query";
import { queryClient } from "./lib/queryClient";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";

import Header from "@/components/Header";
import Footer from "@/components/Footer";
import Home from "@/pages/Home";
import Menu from "@/pages/Menu";
import Cart from "@/pages/Cart";
import Checkout from "@/pages/Checkout";
import Contact from "@/pages/Contact";
import OrderConfirmation from "@/pages/OrderConfirmation";
import OrderTracker from "@/pages/OrderTracker";
import RealOrderTracker from "@/pages/RealOrderTracker";
import OrderLookup from "@/pages/OrderLookup";
import AdminSettings from "@/pages/admin/Settings";
import AdminMenu from "@/pages/admin/Menu";
import AdminAnalytics from "@/pages/admin/Analytics";
import OrderManager from "@/pages/admin/OrderManager";
import CustomizationManager from "@/pages/admin/CustomizationManager";
import DriverPage from "./pages/driver/DriverPage";
import ManagerPage from "./pages/manager/ManagerPage";
import AuthPage from "./pages/auth/AuthPage";
import LogoutPage from "./pages/auth/LogoutPage";
import NotFound from "@/pages/not-found";
import { CartProvider } from "@/context/CartContext";
import { RestaurantStatusProvider } from "@/context/RestaurantStatusContext";
import { NotificationProvider } from "@/context/NotificationContext";
import Loader from "@/components/Loader";

function Router() {
  const [location] = useLocation();

  // Check if current route is an admin, manager or driver route
  const isInternalPage =
    location.startsWith('/admin') ||
    location.startsWith('/driver') ||
    location.startsWith('/manager') ||
    location.startsWith('/auth') ||
    location === '/logout';

  return (
    <div className="flex flex-col min-h-screen">
      {!isInternalPage && <Header />}
      <main className={`flex-grow ${!isInternalPage ? 'pt-20' : ''}`}>
        <Switch>
          <Route path="/" component={Home} />
          <Route path="/menu" component={Menu} />
          <Route path="/cart" component={Cart} />
          <Route path="/checkout" component={Checkout} />
          <Route path="/contact" component={Contact} />
          <Route path="/order-confirmation/:orderId?" component={OrderConfirmation} />
          <Route path="/order-tracker/:orderId?" component={OrderTracker} />
          <Route path="/track-order/:orderId?" component={RealOrderTracker} />
          <Route path="/auth" component={AuthPage} />
          <Route path="/logout" component={LogoutPage} />
          <Route path="/admin/settings" component={AdminSettings} />
          <Route path="/admin/menu" component={AdminMenu} />
          <Route path="/admin/customizations" component={CustomizationManager} />
          <Route path="/admin/analytics" component={AdminAnalytics} />
          <Route path="/admin/orders" component={OrderManager} />
          <Route path="/driver" component={DriverPage} />
          <Route path="/manager" component={ManagerPage} />
          <Route component={NotFound} />
        </Switch>
      </main>
      {!isInternalPage && <Footer />}
    </div>
  );
}

function App() {
  useEffect(() => {
    // Set page title
    document.title = "Barbecuez Restaurant | Premium BBQ Experience";
  }, []);

  return (
    <QueryClientProvider client={queryClient}>
      <RestaurantStatusProvider>
        <CartProvider>
          <NotificationProvider>
            <TooltipProvider>
              <Toaster />
              <Router />
              <Loader />
            </TooltipProvider>
          </NotificationProvider>
        </CartProvider>
      </RestaurantStatusProvider>
    </QueryClientProvider>
  );
}

export default App;
