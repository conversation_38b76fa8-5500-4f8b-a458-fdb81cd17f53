import { pgTable, text, serial, integer, boolean, jsonb, timestamp } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

// Categories Schema
export const categories = pgTable("categories", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  imageUrl: text("image_url").notNull(),
});

export const insertCategorySchema = createInsertSchema(categories).omit({ id: true });
export type InsertCategory = z.infer<typeof insertCategorySchema>;
export type Category = typeof categories.$inferSelect;

// Dish Schema (renamed to menu_items to match the new API structure)
export const menuItems = pgTable("menu_items", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  description: text("description").notNull(),
  price: integer("price").notNull(), // Price in NOK
  imageUrl: text("image_url").notNull(),
  categoryId: integer("category_id").notNull().references(() => categories.id),
  available: boolean("available").default(true),
  rating: integer("rating").default(0),
  reviews: integer("reviews").default(0),
});

export const insertMenuItemSchema = createInsertSchema(menuItems).omit({ id: true });
export type InsertMenuItem = z.infer<typeof insertMenuItemSchema>;
export type MenuItem = typeof menuItems.$inferSelect;

// Customization Groups Schema
export const customizationGroups = pgTable("customization_groups", {
  id: serial("id").primaryKey(),
  title: text("title").notNull(),
});

export const insertCustomizationGroupSchema = createInsertSchema(customizationGroups).omit({ id: true });
export type InsertCustomizationGroup = z.infer<typeof insertCustomizationGroupSchema>;
export type CustomizationGroup = typeof customizationGroups.$inferSelect;

// Customization Options Schema
export const customizationOptions = pgTable("customization_options", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  extraPrice: integer("extra_price").default(0),
  imageUrl: text("image_url").notNull().default(""),
  groupId: integer("group_id").notNull().references(() => customizationGroups.id),
});

export const insertCustomizationOptionSchema = createInsertSchema(customizationOptions).omit({ id: true });
export type InsertCustomizationOption = z.infer<typeof insertCustomizationOptionSchema>;
export type CustomizationOption = typeof customizationOptions.$inferSelect;

// Item Customization Map (many-to-many relationship)
export const itemCustomizationMap = pgTable("item_customization_map", {
  id: serial("id").primaryKey(),
  itemId: integer("item_id").notNull().references(() => menuItems.id),
  optionId: integer("option_id").notNull().references(() => customizationOptions.id),
});

export const insertItemCustomizationMapSchema = createInsertSchema(itemCustomizationMap).omit({ id: true });
export type InsertItemCustomizationMap = z.infer<typeof insertItemCustomizationMapSchema>;
export type ItemCustomizationMap = typeof itemCustomizationMap.$inferSelect;

// For backwards compatibility with existing code
export const dishes = menuItems;
export const insertDishSchema = insertMenuItemSchema;
export type InsertDish = InsertMenuItem;
export type Dish = MenuItem;

// Order Schema
export const orders = pgTable("orders", {
  id: serial("id").primaryKey(),
  customer: jsonb("customer").notNull(),
  items: jsonb("items").notNull(),
  orderDetails: jsonb("order_details").notNull(),
  subtotal: integer("subtotal").notNull(),
  deliveryFee: integer("delivery_fee").notNull(),
  total: integer("total").notNull(),
  status: text("status").notNull().default("pending"),
  paymentMethod: text("payment_method").notNull(),
  notes: text("notes"),
  createdAt: timestamp("created_at").defaultNow(),
});

export const insertOrderSchema = createInsertSchema(orders).omit({
  id: true,
  createdAt: true
});
export type InsertOrder = z.infer<typeof insertOrderSchema>;
export type Order = typeof orders.$inferSelect;

// Contact Message Schema
export const contactMessages = pgTable("contact_messages", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  email: text("email").notNull(),
  subject: text("subject").notNull(),
  message: text("message").notNull(),
  createdAt: timestamp("created_at").defaultNow(),
});

export const insertContactSchema = createInsertSchema(contactMessages).omit({
  id: true,
  createdAt: true
});
export type InsertContactMessage = z.infer<typeof insertContactSchema>;
export type ContactMessage = typeof contactMessages.$inferSelect;

// User Schema
export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  username: text("username").notNull().unique(),
  email: text("email").notNull().unique(),
  password: text("password").notNull(),
  first_name: text("first_name"),
  last_name: text("last_name"),
  role: text("role").notNull().default("customer"),
  is_active: boolean("is_active").notNull().default(true),
  created_at: timestamp("created_at").defaultNow(),
  updated_at: timestamp("updated_at").defaultNow(),
});

export const insertUserSchema = createInsertSchema(users).omit({
  id: true,
  created_at: true,
  updated_at: true,
});

export const loginUserSchema = z.object({
  username: z.string().min(3).max(50),
  password: z.string().min(6),
});

export type InsertUser = z.infer<typeof insertUserSchema>;
export type LoginUser = z.infer<typeof loginUserSchema>;
export type User = typeof users.$inferSelect;

// Restaurant Settings Schema
export const restaurantSettings = pgTable("restaurant_settings", {
  id: serial("id").primaryKey(),
  restaurantOpen: boolean("restaurant_open").default(true),
  businessHours: jsonb("business_hours").default({}),
  deliveryFee: integer("delivery_fee").default(49),
  estimatedTime: text("estimated_time").default("25-35 min"),
});

export const insertRestaurantSettingsSchema = createInsertSchema(restaurantSettings).omit({ id: true });
export type InsertRestaurantSettings = z.infer<typeof insertRestaurantSettingsSchema>;
export type RestaurantSettings = typeof restaurantSettings.$inferSelect;
