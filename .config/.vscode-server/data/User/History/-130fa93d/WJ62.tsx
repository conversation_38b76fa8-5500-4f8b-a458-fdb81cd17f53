import { useState, useEffect } from "react";
import { useLocation, Link } from "wouter";
import { motion } from "framer-motion";

interface OrderConfirmationProps {
  params?: {
    orderId?: string;
  };
}

const OrderConfirmation = (props: OrderConfirmationProps) => {
  const orderId = props.params?.orderId || "BBC" + Math.floor(Math.random() * 100000);
  const [, setLocation] = useLocation();
  const [showConfetti, setShowConfetti] = useState(true);
  const [countdown, setCountdown] = useState(3);
  const [isRedirecting, setIsRedirecting] = useState(false);

  // Estimated delivery time calculation (30-45 minutes from now)
  const now = new Date();
  const deliveryTime = new Date(now.getTime() + 45 * 60000);
  const deliveryTimeString = deliveryTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

  // Countdown timer and redirect logic
  useEffect(() => {
    // Start countdown after 2 seconds to let user read the confirmation
    const startCountdownTimer = setTimeout(() => {
      const countdownInterval = setInterval(() => {
        setCountdown((prev) => {
          if (prev <= 1) {
            clearInterval(countdownInterval);
            handleRedirectToTracking();
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

      return () => clearInterval(countdownInterval);
    }, 2000);

    return () => clearTimeout(startCountdownTimer);
  }, []);

  // Function to handle redirect to order tracking
  const handleRedirectToTracking = () => {
    setIsRedirecting(true);

    // Validate order ID before redirecting
    if (!orderId || orderId.startsWith('BBC') && orderId.length < 6) {
      // If no valid order ID, redirect to a general order lookup page
      setLocation('/track-order');
      return;
    }

    // Redirect to real order tracking page
    setLocation(`/track-order/${orderId}`);
  };

  // Function to skip countdown and go directly to tracking
  const handleSkipCountdown = () => {
    setCountdown(0);
    handleRedirectToTracking();
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 10
      }
    }
  };

  const pulseVariants = {
    initial: { scale: 1, boxShadow: "0 0 0px rgba(57, 255, 20, 0)" },
    animate: {
      scale: [1, 1.05, 1],
      boxShadow: [
        "0 0 0px rgba(57, 255, 20, 0)",
        "0 0 20px rgba(57, 255, 20, 0.7)",
        "0 0 0px rgba(57, 255, 20, 0)"
      ],
      transition: {
        duration: 2,
        repeat: Infinity,
        repeatType: "loop" as const
      }
    }
  };

  // Disable confetti after a few seconds
  useEffect(() => {
    const timer = setTimeout(() => {
      setShowConfetti(false);
    }, 5000);

    return () => clearTimeout(timer);
  }, []);

  return (
    <main className="min-h-screen bg-black relative overflow-hidden flex items-center justify-center py-12">
      {/* Animated Gradient Background */}
      <div
        className="absolute inset-0 z-0"
        style={{
          background: "radial-gradient(circle at 20% 20%, rgba(0, 255, 255, 0.03), transparent 33%), " +
                    "radial-gradient(circle at 80% 80%, rgba(57, 255, 20, 0.03), transparent 33%), " +
                    "radial-gradient(circle at 50% 50%, rgba(255, 0, 255, 0.03), rgba(0, 0, 0, 1) 100%)",
        }}
      ></div>

      {/* Neon Grid Overlay */}
      <div className="absolute inset-0 z-0 opacity-[0.03]"
           style={{
             backgroundImage: "linear-gradient(to right, #39FF14 1px, transparent 1px), linear-gradient(to bottom, #39FF14 1px, transparent 1px)",
             backgroundSize: "40px 40px"
           }}>
      </div>

      {/* Confetti Animation */}
      {showConfetti && (
        <div className="absolute inset-0 pointer-events-none z-10">
          {[...Array(40)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-3 h-3 rounded-full"
              style={{
                backgroundColor:
                  i % 3 === 0 ? 'rgba(57, 255, 20, 0.7)' :
                  i % 3 === 1 ? 'rgba(0, 255, 255, 0.7)' :
                  'rgba(255, 0, 255, 0.7)',
                top: `${Math.random() * -10}%`,
                left: `${Math.random() * 100}%`,
              }}
              initial={{
                y: -20,
                opacity: 0,
                scale: 0
              }}
              animate={{
                y: `${100 + Math.random() * 50}vh`,
                opacity: [1, 0.8, 0],
                scale: [1, 0.8, 0.6],
                rotate: [0, Math.random() * 360]
              }}
              transition={{
                duration: 2.5 + Math.random() * 3.5,
                delay: Math.random() * 3,
                ease: "easeOut",
                repeat: 1,
                repeatType: "loop",
                repeatDelay: Math.random() * 2
              }}
            />
          ))}
        </div>
      )}

      <motion.div
        className="container mx-auto px-4 z-10 relative"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        <div className="max-w-xl mx-auto">
          <motion.div
            className="bg-black/30 backdrop-blur-sm rounded-2xl p-8 md:p-12 border border-lime-800/30
                       shadow-[0_0_30px_rgba(57,255,20,0.2)]"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6 }}
          >
            <div className="text-center">
              {/* Success Icon */}
              <motion.div
                className="w-28 h-28 mx-auto mb-8 rounded-full bg-gradient-to-br from-lime-500/20 to-green-700/20
                         flex items-center justify-center"
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{
                  type: "spring",
                  stiffness: 200,
                  damping: 15,
                  delay: 0.2
                }}
              >
                <motion.div
                  className="w-24 h-24 rounded-full bg-gradient-to-br from-lime-500/30 to-green-700/30
                           flex items-center justify-center"
                  variants={pulseVariants}
                  initial="initial"
                  animate="animate"
                >
                  <svg className="w-16 h-16 text-lime-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <motion.path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2.5}
                      d="M5 13l4 4L19 7"
                      initial={{ pathLength: 0 }}
                      animate={{ pathLength: 1 }}
                      transition={{ duration: 0.8, delay: 0.5 }}
                    />
                  </svg>
                </motion.div>
              </motion.div>

              {/* Heading */}
              <motion.h1
                className="font-playfair text-3xl md:text-4xl font-bold mb-3 text-white"
                variants={itemVariants}
              >
                Thank you for your order!
              </motion.h1>

              {/* Order ID */}
              <motion.div
                className="mb-6"
                variants={itemVariants}
              >
                <p className="text-gray-300 text-lg">Order <span className="text-lime-400 font-medium">#{orderId}</span> confirmed</p>
              </motion.div>

              {/* Order Summary Box */}
              <motion.div
                className="bg-black/50 rounded-xl p-6 mb-8 border border-gray-800"
                variants={itemVariants}
              >
                <div className="flex items-center justify-center mb-3">
                  <svg className="w-5 h-5 text-lime-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <h3 className="font-medium text-white">Estimated Delivery/Pickup</h3>
                </div>
                <p className="text-lime-400 text-xl font-bold mb-1">{deliveryTimeString}</p>
                <p className="text-gray-400 text-sm">
                  Our chef is preparing your delicious meal
                </p>
              </motion.div>

              {/* Countdown Timer and Track Order Section */}
              <motion.div variants={itemVariants} className="space-y-4">
                {/* Countdown Timer */}
                {countdown > 0 && !isRedirecting && (
                  <motion.div
                    className="bg-black/40 rounded-lg p-4 border border-lime-800/30"
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 2 }}
                  >
                    <div className="flex items-center justify-center space-x-2 text-gray-300">
                      <svg className="w-4 h-4 text-lime-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <span className="text-sm">
                        Redirecting to order tracking in{' '}
                        <motion.span
                          className="text-lime-400 font-bold text-lg"
                          key={countdown}
                          initial={{ scale: 1.2 }}
                          animate={{ scale: 1 }}
                          transition={{ duration: 0.3 }}
                        >
                          {countdown}
                        </motion.span>
                        {' '}second{countdown !== 1 ? 's' : ''}...
                      </span>
                    </div>
                  </motion.div>
                )}

                {/* Redirecting State */}
                {isRedirecting && (
                  <motion.div
                    className="bg-black/40 rounded-lg p-4 border border-lime-800/30"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                  >
                    <div className="flex items-center justify-center space-x-2 text-lime-400">
                      <motion.svg
                        className="w-4 h-4"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                        animate={{ rotate: 360 }}
                        transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                      >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                      </motion.svg>
                      <span className="text-sm font-medium">Redirecting to order tracking...</span>
                    </div>
                  </motion.div>
                )}

                {/* Track Order Now Button */}
                <motion.button
                  onClick={handleSkipCountdown}
                  disabled={isRedirecting}
                  className="relative overflow-hidden rounded-lg px-8 py-4 group w-full disabled:opacity-50 disabled:cursor-not-allowed"
                  whileHover={!isRedirecting ? { scale: 1.03 } : {}}
                  whileTap={!isRedirecting ? { scale: 0.97 } : {}}
                  transition={{ type: "spring", stiffness: 400, damping: 17 }}
                >
                  {/* Button Background */}
                  <span className="absolute inset-0 w-full h-full bg-gradient-to-r from-lime-700/70 to-lime-600/70"></span>

                  {/* Button Glow Effect */}
                  <span className="absolute inset-0 w-full h-full transition-all duration-300
                                  bg-gradient-to-r from-lime-600/50 via-lime-500/50 to-lime-600/50
                                  opacity-0 group-hover:opacity-100 group-hover:blur-md"></span>

                  {/* Button Border */}
                  <span className="absolute inset-0 w-full h-full border border-lime-500/50 rounded-lg"></span>

                  {/* Button Text */}
                  <span className="relative z-10 flex items-center justify-center text-white font-medium text-lg">
                    <svg className="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2}
                          d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                    </svg>
                    {countdown > 0 && !isRedirecting ? 'Track Order Now' : 'Track Your Order'}
                  </span>
                </motion.button>
              </motion.div>

              {/* Return to Home */}
              <motion.div
                variants={itemVariants}
                className="mt-6"
              >
                <Link href="/">
                  <motion.button
                    className="text-gray-400 hover:text-lime-400 transition-colors duration-300"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    Return to Home
                  </motion.button>
                </Link>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </motion.div>
    </main>
  );
};

export default OrderConfirmation;