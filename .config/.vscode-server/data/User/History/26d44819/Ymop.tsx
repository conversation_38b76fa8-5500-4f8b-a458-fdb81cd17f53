import { ReactNode, useState } from "react";
import { useLocation, Link } from "wouter";
import { motion } from "framer-motion";
import { ShoppingBag, Home, LogOut } from "lucide-react";
import { useNotifications } from "@/context/NotificationContext";
import NotificationDropdown from "@/components/NotificationDropdown";

interface DriverLayoutProps {
  children: ReactNode;
}

const DriverLayout = ({ children }: DriverLayoutProps) => {
  const [location] = useLocation();
  const { unreadCount } = useNotifications();
  const [isNotificationOpen, setIsNotificationOpen] = useState(false);

  return (
    <div className="min-h-screen bg-black text-white flex flex-col">
      {/* Header */}
      <motion.header
        className="bg-gray-900/80 border-b border-gray-800 py-4 px-6 flex justify-between items-center backdrop-blur-md"
        initial={{ y: -20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.3 }}
      >
        <div className="flex items-center">
          <ShoppingBag className="h-6 w-6 text-pink-500 mr-3" />
          <h1 className="text-xl font-bold bg-gradient-to-r from-pink-400 via-cyan-400 to-pink-400 text-transparent bg-clip-text">
            Barbecuez Driver Portal
          </h1>
        </div>

        <div className="flex items-center space-x-4">
          <motion.div
            className="relative"
            whileHover={{ scale: 1.05 }}
          >
            <button
              onClick={() => setIsNotificationOpen(!isNotificationOpen)}
              className="p-2 rounded-full bg-gray-800 text-pink-500 hover:bg-pink-900/30 border border-pink-800/50 transition-colors"
            >
              <ShoppingBag className="w-5 h-5" />
            </button>
            {unreadCount > 0 && (
              <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center animate-pulse">
                {unreadCount > 99 ? '99+' : unreadCount}
              </span>
            )}

            <NotificationDropdown
              isOpen={isNotificationOpen}
              onClose={() => setIsNotificationOpen(false)}
            />
          </motion.div>

          <Link href="/">
            <div className="flex items-center text-gray-400 hover:text-white hover:bg-gray-800/50 rounded-lg px-3 py-2 cursor-pointer">
              <Home className="w-5 h-5 mr-2" />
              <span>Home</span>
            </div>
          </Link>

          <Link href="/logout">
            <div className="flex items-center text-red-400 hover:text-white hover:bg-red-900/50 rounded-lg px-3 py-2 cursor-pointer">
              <LogOut className="w-5 h-5 mr-2" />
              <span>Logout</span>
            </div>
          </Link>
        </div>
      </motion.header>

      {/* Main Content */}
      <main className="flex-grow p-4 md:p-6">
        <div className="max-w-6xl mx-auto">
          {children}
        </div>
      </main>

      {/* Footer */}
      <footer className="py-4 px-6 text-center text-gray-500 text-sm">
        &copy; {new Date().getFullYear()} Barbecuez Restaurant Driver Portal. All rights reserved.
      </footer>
    </div>
  );
};

export default DriverLayout;