import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { CheckCircle, Clock, Package, ChefHat, Navigation, User } from 'lucide-react';
import { format } from 'date-fns';
import {
  ORDER_STATUSES,
  ORDER_TYPES,
  getStatusTimeline,
  getStatusLabel,
  getStatusDescription
} from '@/utils/orderStatusWorkflow';

interface OrderTrackerProps {
  params?: {
    orderId?: string;
  };
}

interface Order {
  id: number;
  status: string;
  orderDetails: {
    type: 'delivery' | 'takeaway';
    time: string;
    scheduledTime?: string;
  };
  items: Array<{
    id: number;
    name: string;
    quantity: number;
    price: number;
    description?: string;
    imageUrl?: string;
  }>;
  customer: {
    firstName: string;
    email: string;
    phone: string;
    address?: string;
    postalCode?: string;
    city?: string;
  };
  subtotal: number;
  deliveryFee: number;
  total: number;
  paymentMethod: string;
  notes?: string;
  createdAt: string;
  estimatedDeliveryTime?: string;
}

const RealOrderTracker = (props: OrderTrackerProps) => {
  const orderId = props.params?.orderId;
  const [order, setOrder] = useState<Order | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch order data
  useEffect(() => {
    const fetchOrder = async () => {
      if (!orderId) {
        setError('Order ID is required');
        setLoading(false);
        return;
      }

      try {
        const response = await fetch(`/api/orders/${orderId}`);
        if (!response.ok) {
          throw new Error('Order not found');
        }

        const orderData = await response.json();
        setOrder(orderData);
        setError(null);
      } catch (err) {
        console.error('Error fetching order:', err);
        setError('Failed to load order details');
      } finally {
        setLoading(false);
      }
    };

    fetchOrder();

    // Poll for updates every 30 seconds
    const interval = setInterval(fetchOrder, 30000);
    return () => clearInterval(interval);
  }, [orderId]);

  // Get status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case ORDER_STATUSES.CONFIRMED:
        return <CheckCircle className="w-6 h-6" />;
      case ORDER_STATUSES.PREPARING:
        return <ChefHat className="w-6 h-6" />;
      case ORDER_STATUSES.READY_FOR_PICKUP:
        return <Package className="w-6 h-6" />;
      case ORDER_STATUSES.READY_FOR_DELIVERY:
        return <Package className="w-6 h-6" />;
      case ORDER_STATUSES.WITH_DRIVER:
        return <User className="w-6 h-6" />;
      case ORDER_STATUSES.ON_THE_WAY:
        return <Navigation className="w-6 h-6" />;
      case ORDER_STATUSES.DELIVERED:
      case ORDER_STATUSES.COMPLETED:
        return <CheckCircle className="w-6 h-6" />;
      default:
        return <Clock className="w-6 h-6" />;
    }
  };

  // Get status color
  const getStatusColorClass = (status: string, isActive: boolean, isCompleted: boolean) => {
    if (isCompleted) return 'text-green-400 bg-green-900/20 border-green-700';
    if (isActive) {
      switch (status) {
        case ORDER_STATUSES.CONFIRMED:
          return 'text-blue-400 bg-blue-900/20 border-blue-700';
        case ORDER_STATUSES.PREPARING:
          return 'text-orange-400 bg-orange-900/20 border-orange-700';
        case ORDER_STATUSES.READY_FOR_PICKUP:
        case ORDER_STATUSES.READY_FOR_DELIVERY:
          return 'text-green-400 bg-green-900/20 border-green-700';
        case ORDER_STATUSES.WITH_DRIVER:
          return 'text-cyan-400 bg-cyan-900/20 border-cyan-700';
        case ORDER_STATUSES.ON_THE_WAY:
          return 'text-purple-400 bg-purple-900/20 border-purple-700';
        default:
          return 'text-gray-400 bg-gray-900/20 border-gray-700';
      }
    }
    return 'text-gray-600 bg-gray-900/10 border-gray-800';
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-cyan-400 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading order details...</p>
        </div>
      </div>
    );
  }

  if (error || !order) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-400 text-6xl mb-4">⚠️</div>
          <h1 className="text-2xl font-bold text-white mb-2">Order Not Found</h1>
          <p className="text-gray-400 mb-6">{error || 'The order you are looking for does not exist.'}</p>
          <a href="/" className="text-cyan-400 hover:text-cyan-300 underline">
            Return to Home
          </a>
        </div>
      </div>
    );
  }

  const orderType = order.orderDetails.type;
  const statusTimeline = getStatusTimeline(orderType);
  const currentStatusIndex = statusTimeline.indexOf(order.status as any);

  return (
    <div className="min-h-screen bg-black text-white py-12">
      {/* Background */}
      <div className="absolute inset-0 z-0 opacity-[0.02]"
           style={{
             backgroundImage: "linear-gradient(to right, #00FFFF 1px, transparent 1px), linear-gradient(to bottom, #00FFFF 1px, transparent 1px)",
             backgroundSize: "40px 40px"
           }}>
      </div>

      <div className="container mx-auto px-4 z-10 relative max-w-4xl">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-3xl md:text-4xl font-bold mb-4">
            Track Your Order
          </h1>
          <div className="space-y-2">
            <p className="text-xl">
              Order <span className="text-cyan-400 font-medium">#{order.id}</span>
            </p>
            <p className="text-gray-400">
              {orderType === ORDER_TYPES.DELIVERY ? 'Delivery' : 'Takeaway'} Order
            </p>
            <p className="text-gray-500 text-sm">
              Placed on {format(new Date(order.createdAt), 'PPp')}
            </p>
          </div>
        </div>

        {/* Current Status */}
        <div className="bg-gray-900/50 rounded-xl p-6 mb-8 border border-gray-800">
          <div className="flex items-center justify-center space-x-4">
            <div className={`p-3 rounded-full ${getStatusColorClass(order.status, true, false)}`}>
              {getStatusIcon(order.status)}
            </div>
            <div className="text-center">
              <h2 className="text-2xl font-bold">{getStatusLabel(order.status as any)}</h2>
              <p className="text-gray-400">{getStatusDescription(order.status as any)}</p>
            </div>
          </div>
        </div>

        {/* Progress Timeline */}
        <div className="bg-gray-900/50 rounded-xl p-6 mb-8 border border-gray-800">
          <h3 className="text-xl font-bold mb-6">Order Progress</h3>

          {/* Progress Bar */}
          <div className="relative h-2 bg-gray-800 rounded-full mb-8 overflow-hidden">
            <motion.div
              className="absolute h-full bg-gradient-to-r from-cyan-500 to-green-500 rounded-full"
              initial={{ width: "0%" }}
              animate={{ width: `${currentStatusIndex >= 0 ? (currentStatusIndex / (statusTimeline.length - 1)) * 100 : 0}%` }}
              transition={{ duration: 1, ease: "easeInOut" }}
            />
          </div>

          {/* Timeline Steps */}
          <div className="space-y-6">
            {statusTimeline.map((status, index) => {
              const isActive = index === currentStatusIndex;
              const isCompleted = index < currentStatusIndex;

              return (
                <motion.div
                  key={status}
                  className="flex items-center space-x-4"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <div className={`p-3 rounded-full border-2 ${getStatusColorClass(status, isActive, isCompleted)}`}>
                    {getStatusIcon(status)}
                  </div>
                  <div className="flex-1">
                    <h4 className={`font-medium ${isActive || isCompleted ? 'text-white' : 'text-gray-500'}`}>
                      {getStatusLabel(status)}
                    </h4>
                    <p className={`text-sm ${isActive || isCompleted ? 'text-gray-300' : 'text-gray-600'}`}>
                      {getStatusDescription(status)}
                    </p>
                  </div>
                  {isCompleted && (
                    <CheckCircle className="w-5 h-5 text-green-400" />
                  )}
                </motion.div>
              );
            })}
          </div>
        </div>

        {/* Order Details */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Items */}
          <div className="bg-gray-900/50 rounded-xl p-6 border border-gray-800">
            <h3 className="text-xl font-bold mb-4">Order Items</h3>
            <div className="space-y-3">
              {order.items.map((item, index) => (
                <div key={index} className="flex justify-between items-center">
                  <div>
                    <span className="text-white">{item.name}</span>
                    <span className="text-gray-400 ml-2">x{item.quantity}</span>
                  </div>
                  <span className="text-cyan-400">${item.price.toFixed(2)}</span>
                </div>
              ))}
              <div className="border-t border-gray-700 pt-3 mt-3">
                <div className="flex justify-between items-center font-bold">
                  <span>Total</span>
                  <span className="text-green-400">${order.total.toFixed(2)}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Customer Details */}
          <div className="bg-gray-900/50 rounded-xl p-6 border border-gray-800">
            <h3 className="text-xl font-bold mb-4">
              {orderType === ORDER_TYPES.DELIVERY ? 'Delivery Details' : 'Customer Details'}
            </h3>
            <div className="space-y-3">
              <div>
                <span className="text-gray-400">Name:</span>
                <span className="text-white ml-2">{order.customer.firstName}</span>
              </div>
              <div>
                <span className="text-gray-400">Phone:</span>
                <span className="text-white ml-2">{order.customer.phone}</span>
              </div>
              {orderType === ORDER_TYPES.DELIVERY && order.customer.address && (
                <div>
                  <span className="text-gray-400">Address:</span>
                  <span className="text-white ml-2">
                    {order.customer.address}
                    {order.customer.postalCode && `, ${order.customer.postalCode}`}
                    {order.customer.city && ` ${order.customer.city}`}
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="text-center mt-8">
          <a
            href="/"
            className="inline-flex items-center px-6 py-3 bg-cyan-600 hover:bg-cyan-700 text-white rounded-lg transition-colors"
          >
            Return to Home
          </a>
        </div>
      </div>
    </div>
  );
};

export default RealOrderTracker;
