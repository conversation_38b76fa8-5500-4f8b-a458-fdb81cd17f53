import { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  ShoppingBag,
  Truck,
  CheckCircle,
  Clock,
  AlertTriangle,
  PhoneCall,
  MessageSquare,
  Navigation
} from 'lucide-react';
import DriverLayout from './DriverLayout';
import { format, parseISO } from 'date-fns';
import { useNotifications } from '@/context/NotificationContext';
import { useAudioNotification } from '@/hooks/useAudioNotification';
import {
  ORDER_STATUSES,
  ORDER_TYPES,
  getNextStatus,
  getStatusLabel,
  isDriverOrder
} from '@/utils/orderStatusWorkflow';

// Order status constants
const STATUS = {
  READY_FOR_DELIVERY: 'ready_for_delivery',
  WITH_DRIVER: 'with_driver',
  DELIVERED: 'delivered'
};

// Format currency for display
const formatCurrency = (amount: number) => {
  return `$${(amount / 100).toFixed(2)}`;
};

// Format date for display
const formatDate = (dateString: string) => {
  try {
    const date = parseISO(dateString);
    return format(date, 'MMM d, h:mm a');
  } catch (e) {
    return dateString;
  }
};

interface OrderItem {
  id: number;
  name: string;
  price: number;
  quantity: number;
}

interface OrderCustomer {
  firstName: string;
  lastName: string;
  phone: string;
  email: string;
  address?: string;
  postalCode?: string;
  city?: string;
}

interface OrderDetails {
  type?: 'delivery' | 'takeaway';
  time?: 'asap' | 'scheduled';
  scheduledTime?: string | null;
}

interface Order {
  id: number;
  createdAt: string;
  status: string;
  items: OrderItem[];
  customer: OrderCustomer;
  orderDetails?: OrderDetails;
  subtotal: number;
  deliveryFee: number;
  total: number;
  paymentMethod: string;
  notes: string | null;
}

// Status Badge Component
const StatusBadge = ({ status }: { status: string }) => {
  let color = '';
  let icon = <AlertTriangle className="w-4 h-4" />;
  let label = status.replace(/_/g, ' ');

  switch (status) {
    case STATUS.READY_FOR_DELIVERY:
      color = 'bg-pink-900/40 border-pink-700/30 text-pink-400';
      icon = <ShoppingBag className="w-4 h-4" />;
      label = 'Ready for Delivery';
      break;
    case STATUS.WITH_DRIVER:
      color = 'bg-cyan-900/40 border-cyan-700/30 text-cyan-400';
      icon = <Truck className="w-4 h-4" />;
      label = 'With Driver';
      break;
    case STATUS.DELIVERED:
      color = 'bg-green-900/40 border-green-700/30 text-green-400';
      icon = <CheckCircle className="w-4 h-4" />;
      label = 'Delivered';
      break;
  }

  return (
    <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${color}`}>
      <span className="mr-1.5">{icon}</span>
      {label}
    </div>
  );
};

// Action Button Component
const ActionButton = ({
  status,
  onClick,
  isLoading
}: {
  status: string;
  onClick: () => void;
  isLoading: boolean;
}) => {
  let color = '';
  let icon = null;
  let label = '';

  switch (status) {
    case STATUS.READY_FOR_DELIVERY:
      color = 'bg-pink-900/50 hover:bg-pink-800 border-pink-700 text-pink-400 hover:text-white shadow-[0_0_15px_rgba(236,72,153,0.3)]';
      icon = <Truck className="w-5 h-5 mr-2" />;
      label = 'Accept Delivery';
      break;
    case STATUS.WITH_DRIVER:
      color = 'bg-cyan-900/50 hover:bg-cyan-800 border-cyan-700 text-cyan-400 hover:text-white shadow-[0_0_15px_rgba(34,211,238,0.3)]';
      icon = <CheckCircle className="w-5 h-5 mr-2" />;
      label = 'Mark as Delivered';
      break;
    default:
      return null;
  }

  return (
    <motion.button
      className={`flex items-center justify-center px-6 py-2.5 rounded-lg font-medium
                  border transition-all ${color} disabled:opacity-50 disabled:cursor-not-allowed
                  w-full md:w-auto`}
      onClick={onClick}
      disabled={isLoading}
      whileHover={{ scale: 1.03 }}
      whileTap={{ scale: 0.97 }}
    >
      {isLoading ? (
        <Clock className="w-5 h-5 animate-spin mr-2" />
      ) : (
        icon
      )}
      {label}
    </motion.button>
  );
};

// Driver Order Card Component
const DriverOrderCard = ({
  order,
  onStatusUpdate,
  isUpdating
}: {
  order: Order;
  onStatusUpdate: (orderId: number, newStatus: string) => void;
  isUpdating: boolean;
}) => {
  // Next status to transition to
  const getNextStatus = () => {
    switch (order.status) {
      case STATUS.READY_FOR_DELIVERY:
        return STATUS.WITH_DRIVER;
      case STATUS.WITH_DRIVER:
        return STATUS.DELIVERED;
      default:
        return null;
    }
  };

  const nextStatus = getNextStatus();

  // Handle status update
  const handleUpdateStatus = () => {
    if (nextStatus) {
      onStatusUpdate(order.id, nextStatus);
    }
  };

  // Get customer address
  const getFullAddress = () => {
    const { customer } = order;
    if (!customer.address) return 'No address provided';

    let address = customer.address;
    if (customer.postalCode) address += `, ${customer.postalCode}`;
    if (customer.city) address += `, ${customer.city}`;

    return address;
  };

  // Mock phone call function
  const handlePhoneCall = (e: React.MouseEvent) => {
    e.stopPropagation();
    alert(`Calling customer: ${order.customer.phone}`);
  };

  // Mock message function
  const handleMessage = (e: React.MouseEvent) => {
    e.stopPropagation();
    alert(`Messaging customer: ${order.customer.phone}`);
  };

  return (
    <motion.div
      className="bg-gray-900/70 rounded-xl border border-gray-800 overflow-hidden backdrop-blur-md shadow-lg"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
    >
      {/* Header */}
      <div className="bg-gray-900 p-4 border-b border-gray-800 flex justify-between items-center">
        <div className="flex items-center">
          <span className="text-lg font-medium text-white mr-3">Order #{order.id}</span>
          <StatusBadge status={order.status} />
        </div>
        <div className="text-sm text-gray-400">
          {formatDate(order.createdAt)}
        </div>
      </div>

      {/* Body */}
      <div className="p-5 space-y-4">
        {/* Customer Info */}
        <div className="bg-gray-800/50 rounded-lg p-4">
          <h3 className="text-white font-medium mb-3 flex items-center">
            <span className="inline-block w-2 h-2 bg-pink-500 rounded-full mr-2"></span>
            Customer Information
          </h3>

          <div className="flex flex-col md:flex-row justify-between">
            <div className="space-y-1 mb-4 md:mb-0">
              <p className="text-white font-medium">
                {order.customer.firstName} {order.customer.lastName}
              </p>
              <p className="text-gray-400">{order.customer.email}</p>
              <p className="text-cyan-400">{order.customer.phone}</p>
            </div>

            <div className="flex space-x-2">
              <motion.button
                className="p-2.5 rounded-full bg-gray-800 text-cyan-400 hover:bg-cyan-900/40 border border-cyan-800/50"
                onClick={handlePhoneCall}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <PhoneCall className="w-5 h-5" />
              </motion.button>

              <motion.button
                className="p-2.5 rounded-full bg-gray-800 text-pink-400 hover:bg-pink-900/40 border border-pink-800/50"
                onClick={handleMessage}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <MessageSquare className="w-5 h-5" />
              </motion.button>
            </div>
          </div>
        </div>

        {/* Delivery Info */}
        <div className="bg-gray-800/50 rounded-lg p-4">
          <h3 className="text-white font-medium mb-3 flex items-center">
            <span className="inline-block w-2 h-2 bg-cyan-500 rounded-full mr-2"></span>
            Delivery Information
          </h3>

          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-gray-400">Address:</span>
              <span className="text-white text-right">{getFullAddress()}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Order Time:</span>
              <span className="text-white">
                {order.orderDetails.time === 'asap'
                  ? 'ASAP'
                  : (order.orderDetails.scheduledTime
                      ? `Scheduled for ${formatDate(order.orderDetails.scheduledTime)}`
                      : 'Not specified'
                    )
                }
              </span>
            </div>
          </div>
        </div>

        {/* Order Items */}
        <div className="bg-gray-800/50 rounded-lg overflow-hidden">
          <h3 className="text-white font-medium p-4 border-b border-gray-700 flex items-center">
            <span className="inline-block w-2 h-2 bg-green-500 rounded-full mr-2"></span>
            Order Items
          </h3>

          <ul className="divide-y divide-gray-700">
            {order.items.map(item => (
              <li key={item.id} className="p-3 flex justify-between">
                <div className="flex items-start">
                  <span className="text-pink-400 font-medium mr-2">{item.quantity}x</span>
                  <span className="text-white">{item.name}</span>
                </div>
                <span className="text-gray-300">{formatCurrency(item.price * item.quantity)}</span>
              </li>
            ))}
          </ul>

          <div className="border-t border-gray-700 p-3 space-y-1">
            <div className="flex justify-between">
              <span className="text-gray-400">Subtotal:</span>
              <span className="text-white">{formatCurrency(order.subtotal)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Delivery Fee:</span>
              <span className="text-white">{formatCurrency(order.deliveryFee)}</span>
            </div>
            <div className="flex justify-between font-medium pt-1">
              <span className="text-cyan-400">Total:</span>
              <span className="text-white text-lg">{formatCurrency(order.total)}</span>
            </div>
          </div>
        </div>

        {/* Notes */}
        {order.notes && (
          <div className="bg-gray-800/50 rounded-lg p-4">
            <h3 className="text-white font-medium mb-2 flex items-center">
              <span className="inline-block w-2 h-2 bg-yellow-500 rounded-full mr-2"></span>
              Special Instructions
            </h3>
            <p className="text-gray-300">{order.notes}</p>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex justify-end pt-2">
          <ActionButton
            status={order.status}
            onClick={handleUpdateStatus}
            isLoading={isUpdating}
          />
        </div>
      </div>
    </motion.div>
  );
};

// Main Driver Page Component
const DriverPage = () => {
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isUpdating, setIsUpdating] = useState(false);

  // Fetch delivery orders
  useEffect(() => {
    const fetchOrders = async () => {
      setLoading(true);
      try {
        // In a real app, we'd have a dedicated API endpoint for driver orders
        // For now, we'll get all orders and filter for delivery ones
        const response = await fetch('/api/admin/orders');
        if (!response.ok) {
          throw new Error(`Failed to fetch orders: ${response.status}`);
        }

        const allOrders = await response.json();

        // Filter for delivery orders with appropriate status
        const driverOrders = allOrders.filter((order: Order) =>
          order.orderDetails?.type === 'delivery' &&
          (order.status === STATUS.READY_FOR_DELIVERY ||
           order.status === STATUS.WITH_DRIVER)
        );

        setOrders(driverOrders);
        setError(null);
      } catch (err) {
        console.error('Error fetching driver orders:', err);
        setError('Failed to load orders. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchOrders();

    // Periodically refresh orders (every 30 seconds)
    const interval = setInterval(fetchOrders, 30000);
    return () => clearInterval(interval);
  }, []);

  // Update order status
  const handleUpdateStatus = async (orderId: number, newStatus: string) => {
    setIsUpdating(true);
    try {
      const response = await fetch(`/api/admin/orders/${orderId}/status`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ newStatus })
      });

      if (!response.ok) {
        throw new Error(`Failed to update order status: ${response.status}`);
      }

      // Update local state
      setOrders(prevOrders =>
        prevOrders.map(order =>
          order.id === orderId
            ? { ...order, status: newStatus }
            : order
        )
      );

      // If order is now delivered, remove it from the list after a delay
      if (newStatus === STATUS.DELIVERED) {
        setTimeout(() => {
          setOrders(prevOrders => prevOrders.filter(order => order.id !== orderId));
        }, 2000);
      }

    } catch (err) {
      console.error('Error updating order status:', err);
      setError('Failed to update order status. Please try again.');
    } finally {
      setIsUpdating(false);
    }
  };

  // Count orders by status
  const countByStatus = (status: string) => {
    return orders.filter(order => order.status === status).length;
  };

  return (
    <DriverLayout>
      <div className="space-y-6">
        <div className="flex flex-col md:flex-row md:justify-between md:items-center gap-4">
          <div>
            <h1 className="text-2xl font-bold bg-gradient-to-r from-pink-400 via-cyan-400 to-pink-400 text-transparent bg-clip-text mb-2">
              Driver Delivery Dashboard
            </h1>
            <p className="text-gray-400">Manage your active deliveries and update order statuses</p>
          </div>

          <div className="flex space-x-4">
            <div className="bg-pink-900/30 border border-pink-700/30 rounded-lg p-3 text-center">
              <div className="text-pink-400 font-medium">Ready for Pickup</div>
              <div className="text-2xl font-bold text-white">{countByStatus(STATUS.READY_FOR_DELIVERY)}</div>
            </div>

            <div className="bg-cyan-900/30 border border-cyan-700/30 rounded-lg p-3 text-center">
              <div className="text-cyan-400 font-medium">With Driver</div>
              <div className="text-2xl font-bold text-white">{countByStatus(STATUS.WITH_DRIVER)}</div>
            </div>
          </div>
        </div>

        {error && (
          <div className="bg-red-900/40 border border-red-700 text-red-400 p-4 rounded-lg">
            {error}
          </div>
        )}

        {loading ? (
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-pink-500"></div>
          </div>
        ) : orders.length === 0 ? (
          <div className="bg-gray-900/50 border border-gray-800 rounded-xl p-8 text-center">
            <Truck className="w-16 h-16 text-gray-700 mx-auto mb-4" />
            <h2 className="text-xl font-medium text-white mb-2">No Active Deliveries</h2>
            <p className="text-gray-400">
              There are currently no orders ready for delivery or in progress.
              New orders will appear here automatically.
            </p>
          </div>
        ) : (
          <div className="space-y-6">
            <AnimatePresence>
              {orders.map(order => (
                <DriverOrderCard
                  key={order.id}
                  order={order}
                  onStatusUpdate={handleUpdateStatus}
                  isUpdating={isUpdating}
                />
              ))}
            </AnimatePresence>
          </div>
        )}
      </div>
    </DriverLayout>
  );
};

export default DriverPage;