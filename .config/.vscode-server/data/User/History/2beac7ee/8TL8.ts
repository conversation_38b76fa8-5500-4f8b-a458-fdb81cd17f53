import { Request, Response, Router } from 'express';
import { storage } from './storage';

const adminApiRouter = Router();

// Admin Settings Endpoints
adminApiRouter.get('/settings', async (req: Request, res: Response) => {
  try {
    const settings = await storage.getRestaurantSettings();

    if (!settings) {
      // Return default settings if none exist
      const defaultSettings = {
        id: 1,
        restaurant_open: true,
        business_hours: {
          "monday": { "open": "10:00", "close": "22:00", "delivery": true },
          "tuesday": { "open": "10:00", "close": "22:00", "delivery": true },
          "wednesday": { "open": "10:00", "close": "22:00", "delivery": true },
          "thursday": { "open": "10:00", "close": "22:00", "delivery": true },
          "friday": { "open": "10:00", "close": "23:00", "delivery": true },
          "saturday": { "open": "10:00", "close": "23:00", "delivery": true },
          "sunday": { "open": "12:00", "close": "21:00", "delivery": true }
        },
        delivery_fee: 49,
        estimated_time: "25-35 min"
      };
      return res.json(defaultSettings);
    }

    // Convert database format to API format
    const apiSettings = {
      id: settings.id,
      restaurant_open: settings.restaurantOpen,
      business_hours: settings.businessHours,
      delivery_fee: settings.deliveryFee,
      estimated_time: settings.estimatedTime
    };

    res.json(apiSettings);
  } catch (error) {
    console.error('Error fetching admin settings:', error);
    res.status(500).json({ error: 'Failed to fetch admin settings' });
  }
});

adminApiRouter.put('/settings', async (req: Request, res: Response) => {
  try {
    const { restaurant_open, business_hours, delivery_fee, estimated_time } = req.body;

    // Convert API format to database format
    const dbSettings = {
      restaurantOpen: restaurant_open,
      businessHours: business_hours,
      deliveryFee: delivery_fee,
      estimatedTime: estimated_time
    };

    const updatedSettings = await storage.updateRestaurantSettings(dbSettings);

    if (!updatedSettings) {
      return res.status(500).json({ error: 'Failed to update settings' });
    }

    // Convert back to API format for response
    const apiSettings = {
      id: updatedSettings.id,
      restaurant_open: updatedSettings.restaurantOpen,
      business_hours: updatedSettings.businessHours,
      delivery_fee: updatedSettings.deliveryFee,
      estimated_time: updatedSettings.estimatedTime
    };

    res.json(apiSettings);
  } catch (error) {
    console.error('Error updating admin settings:', error);
    res.status(500).json({ error: 'Failed to update admin settings' });
  }
});

// Analytics Endpoints
adminApiRouter.get('/analytics/summary', async (req: Request, res: Response) => {
  try {
    const summary = {
      today: 12500,
      week: 87230,
      month: 245890,
      orderCount: 198
    };

    res.json(summary);
  } catch (error) {
    console.error('Error fetching analytics summary:', error);
    res.status(500).json({ error: 'Failed to fetch analytics summary' });
  }
});

adminApiRouter.get('/analytics/daily', async (req: Request, res: Response) => {
  try {
    const dailyRevenue = [
      { date: '2023-05-15', total: 10200 },
      { date: '2023-05-16', total: 11450 },
      { date: '2023-05-17', total: 9870 },
      { date: '2023-05-18', total: 12340 },
      { date: '2023-05-19', total: 14560 },
      { date: '2023-05-20', total: 15780 },
      { date: '2023-05-21', total: 13030 }
    ];

    res.json(dailyRevenue);
  } catch (error) {
    console.error('Error fetching daily revenue:', error);
    res.status(500).json({ error: 'Failed to fetch daily revenue' });
  }
});

adminApiRouter.get('/analytics/categories', async (req: Request, res: Response) => {
  try {
    const categoryRevenue = [
      { category: 'BBQ Mains', total: 98450 },
      { category: 'Sides', total: 45670 },
      { category: 'Burgers', total: 67890 },
      { category: 'Drinks', total: 23450 },
      { category: 'Desserts', total: 10430 }
    ];

    res.json(categoryRevenue);
  } catch (error) {
    console.error('Error fetching category revenue:', error);
    res.status(500).json({ error: 'Failed to fetch category revenue' });
  }
});

// Order Management API Routes are now handled in routes.ts with staff middleware

// Category Management API Routes
adminApiRouter.get('/categories', async (req: Request, res: Response) => {
  try {
    const categories = await storage.getAllMenuCategories();
    res.json(categories);
  } catch (error) {
    console.error('Error fetching categories:', error);
    res.status(500).json({ error: 'Failed to fetch categories' });
  }
});

adminApiRouter.post('/categories', async (req: Request, res: Response) => {
  try {
    const { name, imageUrl } = req.body;

    if (!name) {
      return res.status(400).json({ error: 'Category name is required' });
    }

    const category = await storage.createMenuCategory({ name, imageUrl });
    res.status(201).json(category);
  } catch (error) {
    console.error('Error creating category:', error);
    res.status(500).json({ error: 'Failed to create category' });
  }
});

adminApiRouter.put('/categories/:id', async (req: Request, res: Response) => {
  try {
    const id = parseInt(req.params.id);
    const { name, imageUrl } = req.body;

    if (!name) {
      return res.status(400).json({ error: 'Category name is required' });
    }

    const category = await storage.updateMenuCategory(id, { name, imageUrl });

    if (!category) {
      return res.status(404).json({ error: 'Category not found' });
    }

    res.json(category);
  } catch (error) {
    console.error('Error updating category:', error);
    res.status(500).json({ error: 'Failed to update category' });
  }
});

adminApiRouter.delete('/categories/:id', async (req: Request, res: Response) => {
  try {
    const id = parseInt(req.params.id);
    const success = await storage.deleteMenuCategory(id);

    if (!success) {
      return res.status(404).json({ error: 'Category not found' });
    }

    res.json({ message: 'Category deleted successfully' });
  } catch (error) {
    console.error('Error deleting category:', error);
    res.status(500).json({ error: 'Failed to delete category' });
  }
});

// Menu Item Management API Routes
adminApiRouter.get('/items', async (req: Request, res: Response) => {
  try {
    const items = await storage.getAllMenuItems();
    res.json(items);
  } catch (error) {
    console.error('Error fetching menu items:', error);
    res.status(500).json({ error: 'Failed to fetch menu items' });
  }
});

adminApiRouter.post('/items', async (req: Request, res: Response) => {
  try {
    const { name, description, price, imageUrl, categoryId, available } = req.body;

    if (!name || !price) {
      return res.status(400).json({ error: 'Name and price are required' });
    }

    const item = await storage.createMenuItem({
      name,
      description,
      price,
      imageUrl,
      categoryId,
      available: available !== undefined ? available : true,
      rating: 0,
      reviews: 0
    });

    res.status(201).json(item);
  } catch (error) {
    console.error('Error creating menu item:', error);
    res.status(500).json({ error: 'Failed to create menu item' });
  }
});

adminApiRouter.put('/items/:id', async (req: Request, res: Response) => {
  try {
    const id = parseInt(req.params.id);
    const { name, description, price, imageUrl, categoryId, available } = req.body;

    if (!name || !price) {
      return res.status(400).json({ error: 'Name and price are required' });
    }

    const item = await storage.updateMenuItem(id, {
      name,
      description,
      price,
      imageUrl,
      categoryId,
      available
    });

    if (!item) {
      return res.status(404).json({ error: 'Menu item not found' });
    }

    res.json(item);
  } catch (error) {
    console.error('Error updating menu item:', error);
    res.status(500).json({ error: 'Failed to update menu item' });
  }
});

adminApiRouter.delete('/items/:id', async (req: Request, res: Response) => {
  try {
    const id = parseInt(req.params.id);
    const success = await storage.deleteMenuItem(id);

    if (!success) {
      return res.status(404).json({ error: 'Menu item not found' });
    }

    res.json({ message: 'Menu item deleted successfully' });
  } catch (error) {
    console.error('Error deleting menu item:', error);
    res.status(500).json({ error: 'Failed to delete menu item' });
  }
});

// Customization Groups API Routes
adminApiRouter.get('/customization-groups', async (req: Request, res: Response) => {
  try {
    const groups = await storage.getAllCustomizationGroups();
    res.json(groups);
  } catch (error) {
    console.error('Error fetching customization groups:', error);
    res.status(500).json({ error: 'Failed to fetch customization groups' });
  }
});

adminApiRouter.post('/customization-groups', async (req: Request, res: Response) => {
  try {
    const { title } = req.body;

    if (!title) {
      return res.status(400).json({ error: 'Group title is required' });
    }

    const newGroup = await storage.createCustomizationGroup({ title });
    res.status(201).json(newGroup);
  } catch (error) {
    console.error('Error creating customization group:', error);
    res.status(500).json({ error: 'Failed to create customization group' });
  }
});

adminApiRouter.put('/customization-groups/:id', async (req: Request, res: Response) => {
  try {
    const id = parseInt(req.params.id);
    const { title } = req.body;

    if (!title) {
      return res.status(400).json({ error: 'Group title is required' });
    }

    const updatedGroup = await storage.updateCustomizationGroup(id, { title });

    if (!updatedGroup) {
      return res.status(404).json({ error: 'Customization group not found' });
    }

    res.json(updatedGroup);
  } catch (error) {
    console.error('Error updating customization group:', error);
    res.status(500).json({ error: 'Failed to update customization group' });
  }
});

adminApiRouter.delete('/customization-groups/:id', async (req: Request, res: Response) => {
  try {
    const id = parseInt(req.params.id);
    const success = await storage.deleteCustomizationGroup(id);

    if (!success) {
      return res.status(404).json({ error: 'Customization group not found' });
    }

    res.json({ message: 'Customization group deleted successfully' });
  } catch (error) {
    console.error('Error deleting customization group:', error);
    res.status(500).json({ error: 'Failed to delete customization group' });
  }
});

// Customization Options API Routes
adminApiRouter.get('/customization-options', async (req: Request, res: Response) => {
  try {
    const options = await storage.getAllCustomizationOptions();
    res.json(options);
  } catch (error) {
    console.error('Error fetching customization options:', error);
    res.status(500).json({ error: 'Failed to fetch customization options' });
  }
});

adminApiRouter.get('/customization-options/group/:groupId', async (req: Request, res: Response) => {
  try {
    const groupId = parseInt(req.params.groupId);
    const options = await storage.getCustomizationOptionsByGroup(groupId);
    res.json(options);
  } catch (error) {
    console.error('Error fetching customization options for group:', error);
    res.status(500).json({ error: 'Failed to fetch customization options for group' });
  }
});

adminApiRouter.post('/customization-options', async (req: Request, res: Response) => {
  try {
    const { name, extraPrice, imageUrl, groupId } = req.body;

    if (!name || !groupId) {
      return res.status(400).json({ error: 'Option name and group ID are required' });
    }

    const newOption = await storage.createCustomizationOption({
      name,
      extraPrice: extraPrice || 0,
      imageUrl: imageUrl || "",
      groupId
    });

    res.status(201).json(newOption);
  } catch (error) {
    console.error('Error creating customization option:', error);
    res.status(500).json({ error: 'Failed to create customization option' });
  }
});

adminApiRouter.put('/customization-options/:id', async (req: Request, res: Response) => {
  try {
    const id = parseInt(req.params.id);
    const { name, extraPrice, imageUrl, groupId } = req.body;

    const updatedOption = await storage.updateCustomizationOption(id, {
      name,
      extraPrice,
      imageUrl,
      groupId
    });

    if (!updatedOption) {
      return res.status(404).json({ error: 'Customization option not found' });
    }

    res.json(updatedOption);
  } catch (error) {
    console.error('Error updating customization option:', error);
    res.status(500).json({ error: 'Failed to update customization option' });
  }
});

adminApiRouter.delete('/customization-options/:id', async (req: Request, res: Response) => {
  try {
    const id = parseInt(req.params.id);
    const success = await storage.deleteCustomizationOption(id);

    if (!success) {
      return res.status(404).json({ error: 'Customization option not found' });
    }

    res.json({ message: 'Customization option deleted successfully' });
  } catch (error) {
    console.error('Error deleting customization option:', error);
    res.status(500).json({ error: 'Failed to delete customization option' });
  }
});

// Item Customization Mapping API Routes
adminApiRouter.get('/items/:itemId/customizations', async (req: Request, res: Response) => {
  try {
    const itemId = parseInt(req.params.itemId);
    const customizations = await storage.getCustomizationOptionsForMenuItem(itemId);
    res.json(customizations);
  } catch (error) {
    console.error('Error fetching customizations for menu item:', error);
    res.status(500).json({ error: 'Failed to fetch customizations for menu item' });
  }
});

adminApiRouter.post('/items/:itemId/customizations/:optionId', async (req: Request, res: Response) => {
  try {
    const itemId = parseInt(req.params.itemId);
    const optionId = parseInt(req.params.optionId);

    const mapping = await storage.mapCustomizationOptionToMenuItem(itemId, optionId);
    res.status(201).json(mapping);
  } catch (error) {
    console.error('Error mapping customization to menu item:', error);
    res.status(500).json({ error: 'Failed to map customization to menu item' });
  }
});

adminApiRouter.delete('/items/:itemId/customizations/:optionId', async (req: Request, res: Response) => {
  try {
    const itemId = parseInt(req.params.itemId);
    const optionId = parseInt(req.params.optionId);

    const success = await storage.unmapCustomizationOptionFromMenuItem(itemId, optionId);

    if (!success) {
      return res.status(404).json({ error: 'Customization mapping not found' });
    }

    res.json({ message: 'Customization mapping removed successfully' });
  } catch (error) {
    console.error('Error removing customization mapping:', error);
    res.status(500).json({ error: 'Failed to remove customization mapping' });
  }
});

export default adminApiRouter;