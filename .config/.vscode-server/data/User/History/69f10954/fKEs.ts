import { useCallback, useRef } from 'react';
import { useNotifications } from '@/context/NotificationContext';

interface AudioNotificationOptions {
  volume?: number;
  fallbackToBeep?: boolean;
}

export const useAudioNotification = (options: AudioNotificationOptions = {}) => {
  const { soundEnabled } = useNotifications();
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const { volume = 0.5, fallbackToBeep = true } = options;

  const playNotificationSound = useCallback(async (soundType: 'new_order' | 'status_update' | 'error' = 'new_order') => {
    if (!soundEnabled) return;

    try {
      // For now, use programmatically generated sounds since we don't have audio files yet
      // In production, replace this with actual sound files
      await playSystemBeep(soundType);
    } catch (error) {
      console.warn('Could not play notification sound:', error);
    }
  }, [soundEnabled]);

  const playSystemBeep = useCallback(async () => {
    const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);

    oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
    oscillator.type = 'sine';

    gainNode.gain.setValueAtTime(0, audioContext.currentTime);
    gainNode.gain.linearRampToValueAtTime(0.3, audioContext.currentTime + 0.01);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);

    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + 0.3);

    // Clean up
    setTimeout(() => {
      audioContext.close();
    }, 500);
  }, []);

  const preloadSounds = useCallback(() => {
    const soundFiles = [
      '/sounds/new-order.mp3',
      '/sounds/status-update.mp3',
      '/sounds/error.mp3'
    ];

    soundFiles.forEach(soundFile => {
      const audio = new Audio();
      audio.preload = 'auto';
      audio.src = soundFile;
      // Don't store these, just preload them
    });
  }, []);

  return {
    playNotificationSound,
    playSystemBeep,
    preloadSounds,
    soundEnabled
  };
};
