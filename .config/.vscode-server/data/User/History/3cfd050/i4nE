# Security
- Remove 'Staff Login' from public header navigation for security reasons while maintaining the functionality through a more secure method.
- Remove browser alerts from authentication flows that expose user roles for security reasons; use silent redirects and non-revealing success messages instead.

# Website Control
- Ensure admin settings properly control the restaurant website with real-time updates to the public-facing site.

# Data Persistence
- User prefers persistent database storage (SQLite, PostgreSQL, or MySQL) over in-memory storage for restaurant management systems, with proper schema, migrations, and full CRUD persistence for all entities including menu items, orders, and admin settings.

# Checkout & Order Confirmation
- User prefers delivery and takeaway cart options at the top of checkout page with 'As Soon As Possible' and 'Schedule for Later' options prominently displayed.
- User prefers checkout forms with single name field (no last name), Norwegian phone numbers with +47 prefix as default, and optional email fields for better user experience.
- User prefers geolocation buttons in checkout forms with loading states, graceful permission requests, clear privacy messaging, optional usage, reverse geocoding for address auto-fill, delivery area validation, and seamless integration with existing form design.
- User prefers order confirmation pages with 3-second countdown timers that automatically redirect to order tracking, with skip buttons and graceful error handling for missing order IDs.

# Order Notifications
- User prefers subtle audio notifications (gentle chime/bell) for new orders with toggle options, and notification dropdowns/modals that show detailed order information with real-time updates and read status tracking.

# Order Workflow
- Restaurant order workflows require different status sequences for delivery orders (confirmed → preparing → ready_for_delivery → with_driver → on_the_way → delivered) vs takeaway orders (confirmed → preparing → ready_for_pickup), with automatic driver dispatch for delivery orders and real-time status updates across manager/driver pages and public order tracker.

# Codebase
- User prefers to remove demo/simulation components and keep only production-ready components that integrate with real APIs and databases for cleaner, streamlined codebases.