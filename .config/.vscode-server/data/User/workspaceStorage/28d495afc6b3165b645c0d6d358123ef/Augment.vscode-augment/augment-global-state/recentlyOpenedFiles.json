[["/home/<USER>/workspace/client/src/index.css", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/index.css"}}], ["/home/<USER>/workspace/client/src/pages/manager/ManagerDashboard.tsx", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/manager/ManagerDashboard.tsx"}}], ["/home/<USER>/workspace/client/src/api/adminApi.ts", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/api/adminApi.ts"}}], ["/home/<USER>/workspace/server/schema.sql", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "server/schema.sql"}}], ["/home/<USER>/workspace/client/src/App.tsx", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/App.tsx"}}], ["/home/<USER>/workspace/client/src/pages/Menu.tsx", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/Menu.tsx"}}], ["/home/<USER>/workspace/test-db.js", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "test-db.js"}}], ["/home/<USER>/workspace/server/migrate.ts", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "server/migrate.ts"}}], ["/home/<USER>/workspace/server/index.ts", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "server/index.ts"}}], ["/home/<USER>/workspace/migrations/0000_freezing_vulcan.sql", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "migrations/0000_freezing_vulcan.sql"}}], ["/home/<USER>/workspace/server/test-db.ts", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "server/test-db.ts"}}], ["/home/<USER>/workspace/client/src/api/api.ts", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/api/api.ts"}}], ["/home/<USER>/workspace/server/admin-api.ts", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "server/admin-api.ts"}}], ["/home/<USER>/workspace/client/src/pages/admin/OrderManager.tsx", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/admin/OrderManager.tsx"}}], ["/home/<USER>/workspace/server/storage.ts", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "server/storage.ts"}}], ["/home/<USER>/workspace/server/routes.ts", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "server/routes.ts"}}], ["/home/<USER>/workspace/client/src/pages/admin/Menu.tsx", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/admin/Menu.tsx"}}], ["/home/<USER>/workspace/shared/schema.ts", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "shared/schema.ts"}}], ["/home/<USER>/workspace/client/src/pages/auth/AuthPage.tsx", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/auth/AuthPage.tsx"}}], ["/home/<USER>/workspace/client/src/pages/auth/LogoutPage.tsx", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/auth/LogoutPage.tsx"}}], ["/home/<USER>/workspace/public/sounds/README.md", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "public/sounds/README.md"}}], ["/home/<USER>/workspace/cookies2.txt", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "cookies2.txt"}}], ["/home/<USER>/workspace/cookies.txt", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "cookies.txt"}}], ["/home/<USER>/workspace/client/src/hooks/useAudioNotification.ts", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/hooks/useAudioNotification.ts"}}], ["/home/<USER>/workspace/client/src/pages/OrderConfirmation.tsx", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/OrderConfirmation.tsx"}}], ["/home/<USER>/workspace/client/src/pages/RealOrderTracker.tsx", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/RealOrderTracker.tsx"}}]]