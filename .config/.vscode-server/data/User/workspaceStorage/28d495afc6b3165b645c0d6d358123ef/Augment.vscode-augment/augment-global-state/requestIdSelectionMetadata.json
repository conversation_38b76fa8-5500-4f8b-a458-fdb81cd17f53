[["f45f92c1-8a46-4aa9-81be-b1aa6fff0e0c", {"value": {"selectedCode": "", "prefix": "import passport from 'passport';\n", "suffix": "import { Strategy as LocalStrategy } from 'passport-local';\nimport { authService } from './auth-service';\nimport { User } from '@shared/schema';\n\n/**\n * Configure Passport.js authentication strategies\n */\nexport function configurePassport() {\n  // Configure the local strategy for username/password authentication\n  passport.use(new LocalStrategy(\n    async (username, password, done) => {\n      try {\n        // Attempt to authenticate the user\n        const user = await authService.authenticateUser(username, password);\n        \n        if (!user) {\n          // Authentication failed\n          return done(null, false, { message: 'Invalid username or password' });\n        }\n        \n        // Authentication successful\n        return done(null, user);\n      } catch (error) {\n        return done(error);\n      }\n    }\n  ));\n\n  // Serialize user to the session\n  passport.serializeUser((user: Express.User, done) => {\n    done(null, (user as Omit<User, 'password'>).id);\n  });\n\n  // Deserialize user from the session\n  passport.deserializeUser(async (id: number, done) => {\n    try {\n      const user = await authService.getUserById(id);\n      done(null, user);\n    } catch (error) {\n      done(error);\n    }\n  });\n\n  return passport;\n}\n", "path": "server/auth/passport-config.ts", "language": "typescript", "prefixBegin": 0, "suffixEnd": 0}}], ["3a0aece0-62a5-4171-966c-0ceabb0cc571", {"value": {"selectedCode": "", "prefix": "import { useEffect } from \"react\";\n", "suffix": "import { Route, Switch, useLocation } from \"wouter\";\nimport { QueryClientProvider } from \"@tanstack/react-query\";\nimport { queryClient } from \"./lib/queryClient\";\nimport { Toaster } from \"@/components/ui/toaster\";\nimport { TooltipProvider } from \"@/components/ui/tooltip\";\n\nimport Header from \"@/components/Header\";\nimport Footer from \"@/components/Footer\";\nimport Home from \"@/pages/Home\";\nimport Menu from \"@/pages/Menu\";\nimport Cart from \"@/pages/Cart\";\nimport Checkout from \"@/pages/Checkout\";\nimport Contact from \"@/pages/Contact\";\nimport OrderConfirmation from \"@/pages/OrderConfirmation\";\nimport OrderTracker from \"@/pages/OrderTracker\";\nimport AdminSettings from \"@/pages/admin/Settings\";\nimport AdminMenu from \"@/pages/admin/Menu\";\nimport AdminAnalytics from \"@/pages/admin/Analytics\";\nimport OrderManager from \"@/pages/admin/OrderManager\";\nimport DriverPage from \"./pages/driver/DriverPage\";\nimport ManagerPage from \"./pages/manager/ManagerPage\";\nimport AuthPage from \"./pages/auth/AuthPage\";\nimport LogoutPage from \"./pages/auth/LogoutPage\";\nimport NotFound from \"@/pages/not-found\";\nimport { CartProvider } from \"@/context/CartContext\";\nimport Loader from \"@/components/Loader\";\n\nfunction Router() {\n  const [location] = useLocation();\n  \n  // Check if current route is an admin, manager or driver route\n  const isInternalPage = \n    location.startsWith('/admin') || \n    location.startsWith('/driver') || \n    location.startsWith('/manager') ||\n    location.startsWith('/auth') ||\n    location === '/logout';\n  \n  return (\n    <div className=\"flex flex-col min-h-screen\">\n      {!isInternalPage && <Header />}\n      <main className={`flex-grow ${!isInternalPage ? 'pt-20' : ''}`}>\n        <Switch>\n          <Route path=\"/\" component={Home} />\n          <Route path=\"/menu\" component={Menu} />\n          <Route path=\"/cart\" component={Cart} />\n          <Route path=\"/checkout\" component={Checkout} />\n          <Route path=\"/contact\" component={Contact} />\n          <Route path=\"/order-confirmation/:orderId?\" component={OrderConfirmation} />\n          <Route path=\"/order-tracker/:orderId?\" component={OrderTracker} />\n          <Route path=\"/auth\" component={AuthPage} />\n          <Route path=\"/logout\" component={LogoutPage} />\n          <Route path=\"/admin/settings\" component={AdminSettings} />\n          <Route path=\"/admin/menu\" component={AdminMenu} />\n          <Route path=\"/admin/analytics\" component={AdminAnalytics} />\n          <Route path=\"/admin/orders\" component={OrderManager} />\n          <Route path=\"/driver\" component={DriverPage} />\n          <Route path=\"/manager\" component={ManagerPage} />\n          <Route component={NotFound} />\n        </Switch>\n      </main>\n      {!isInternalPage && <Footer />}\n    </div>\n  );\n}\n\nfunction App() {\n  useEffect(() => {\n    // Set page title\n    document.title = \"Barbecuez Restaurant | Premium BBQ Experience\";\n  }, []);\n\n  return (\n    <QueryClientProvider client={queryClient}>\n      <CartProvider>\n        <TooltipProvider>\n          <Toaster />\n          <Router />\n          <Loader />\n        </TooltipProvider>\n      </CartProvider>\n    </QueryClientProvider>\n  );\n}\n\nexport default App;\n", "path": "client/src/App.tsx", "language": "typescriptreact", "prefixBegin": 0, "suffixEnd": 0}}], ["e28bdb25-833a-4a97-9c16-cd53e371d152", {"value": {"selectedCode": "", "prefix": "import { Express, Request, Response, NextFunction, Router } from 'express';\n", "suffix": "import http from 'http';\nimport { log } from './vite';\nimport { storage } from './storage';\nimport adminApiRouter from './admin-api';\nimport { setupAuth } from './auth';\n\nexport async function registerRoutes(app: Express): Promise<http.Server> {\n  const apiRouter = Router();\n\n  // Logging middleware\n  app.use((req: Request, _res: Response, next: NextFunction) => {\n    if (req.url.startsWith('/api')) {\n      log(`${req.method} ${req.url}`, 'api');\n    }\n    next();\n  });\n\n  // Reusable error handler\n  const handleError = (error: any, res: Response) => {\n    console.error(error);\n    res.status(500).json({ error: 'An unexpected error occurred' });\n  };\n\n  // Menu Items / Dishes Routes\n  apiRouter.get('/dishes', async (req: Request, res: Response) => {\n    try {\n      const dishes = await storage.getAllDishes();\n      res.json(dishes);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  apiRouter.get('/dishes/:id', async (req: Request, res: Response) => {\n    try {\n      const id = parseInt(req.params.id);\n      const dish = await storage.getDishById(id);\n\n      if (!dish) {\n        return res.status(404).json({ error: 'Dish not found' });\n      }\n\n      res.json(dish);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  // Categories Routes\n  apiRouter.get('/categories', async (req: Request, res: Response) => {\n    try {\n      const categories = await storage.getAllMenuCategories();\n      res.json(categories);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  apiRouter.post('/categories', async (req: Request, res: Response) => {\n    try {\n      if (!req.body.name) {\n        return res.status(400).json({ error: 'Category name is required' });\n      }\n\n      const newCategory = await storage.createMenuCategory(req.body);\n      res.status(201).json(newCategory);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  apiRouter.put('/categories/:id', async (req: Request, res: Response) => {\n    try {\n      const id = parseInt(req.params.id);\n      const category = await storage.getMenuCategoryById(id);\n\n      if (!category) {\n        return res.status(404).json({ error: 'Category not found' });\n      }\n\n      const updatedCategory = await storage.updateMenuCategory(id, req.body);\n      res.json(updatedCategory);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  apiRouter.delete('/categories/:id', async (req: Request, res: Response) => {\n    try {\n      const id = parseInt(req.params.id);\n      const category = await storage.getMenuCategoryById(id);\n\n      if (!category) {\n        return res.status(404).json({ error: 'Category not found' });\n      }\n\n      const deletedCategory = await storage.deleteMenuCategory(id);\n      res.json({ message: 'Category deleted successfully', category: deletedCategory });\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  // Menu Items Routes\n  apiRouter.get('/items', async (req: Request, res: Response) => {\n    try {\n      const items = await storage.getAllMenuItems();\n      res.json(items);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  apiRouter.post('/items', async (req: Request, res: Response) => {\n    try {\n      const newItem = await storage.createMenuItem(req.body);\n      res.status(201).json(newItem);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  apiRouter.put('/items/:id', async (req: Request, res: Response) => {\n    try {\n      const id = parseInt(req.params.id);\n      const item = await storage.getMenuItemById(id);\n\n      if (!item) {\n        return res.status(404).json({ error: 'Menu item not found' });\n      }\n\n      // Update logic would go here\n      res.json({ ...item, ...req.body });\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  // Order Routes\n  apiRouter.post('/orders', async (req: Request, res: Response) => {\n    try {\n      // Get restaurant settings to check if it's open\n      const adminSettings = await fetch('http://localhost:3001/api/admin/settings').then(res => res.json());\n\n      // Check if restaurant is open\n      if (!adminSettings.restaurant_open) {\n        return res.status(403).json({\n          error: 'Restaurant is currently closed. Orders cannot be placed at this time.'\n        });\n      }\n\n      // If restaurant is open, proceed with order creation\n      const newOrder = await storage.createOrder(req.body);\n      res.status(201).json(newOrder);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  apiRouter.get('/orders/:id', async (req: Request, res: Response) => {\n    try {\n      const id = parseInt(req.params.id);\n      const order = await storage.getOrderById(id);\n\n      if (!order) {\n        return res.status(404).json({ error: 'Order not found' });\n      }\n\n      res.json(order);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  // Contact Form Route\n  apiRouter.post('/contact', async (req: Request, res: Response) => {\n    try {\n      const message = await storage.createContactMessage(req.body);\n      res.status(201).json({ success: true, message });\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  // Cart Routes\n  apiRouter.post('/cart', async (req: Request, res: Response) => {\n    try {\n      const result = await storage.createCart(req.body);\n      res.status(201).json(result);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  // Register the API router with app\n  app.use('/api', apiRouter);\n\n  // Setup authentication\n  setupAuth(app);\n\n  // Register admin API routes\n  app.use('/api/admin', adminApiRouter);\n\n  // Create and return the server\n  const server = http.createServer(app);\n  return server;\n}", "path": "server/routes.ts", "language": "typescript", "prefixBegin": 0, "suffixEnd": 1}}], ["43a4fc2b-40ca-4534-8a6e-24b1e0851010", {"value": {"selectedCode": "", "prefix": "import { Express, Request, Response, NextFunction, Router } from 'express';\nimport http from 'http';\nimport { log } from './vite';\nimport { storage } from './storage';\nimport adminApiRouter from './admin-api';\nimport { setupAuth } from './auth';\n\nexport async function registerRoutes(app: Express): Promise<http.Server> {\n  const apiRouter = Router();\n\n  // Logging middleware\n  app.use((req: Request, _res: Response, next: NextFunction) => {\n    if (req.url.startsWith('/api')) {\n      log(`${req.method} ${req.url}`, 'api');\n    }\n    next();\n  });\n\n  // Reusable error handler\n  const handleError = (error: any, res: Response) => {\n    console.error(error);\n    res.status(500).json({ error: 'An unexpected error occurred' });\n  };\n\n  // Menu Items / Dishes Routes\n  apiRouter.get('/dishes', async (req: Request, res: Response) => {\n    try {\n      const dishes = await storage.getAllDishes();\n      res.json(dishes);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  apiRouter.get('/dishes/:id', async (req: Request, res: Response) => {\n    try {\n      const id = parseInt(req.params.id);\n      const dish = await storage.getDishById(id);\n\n      if (!dish) {\n        return res.status(404).json({ error: 'Dish not found' });\n      }\n\n      res.json(dish);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  // Categories Routes\n  apiRouter.get('/categories', async (req: Request, res: Response) => {\n    try {\n      const categories = await storage.getAllMenuCategories();\n      res.json(categories);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  apiRouter.post('/categories', async (req: Request, res: Response) => {\n    try {\n      if (!req.body.name) {\n        return res.status(400).json({ error: 'Category name is required' });\n      }\n\n      const newCategory = await storage.createMenuCategory(req.body);\n      res.status(201).json(newCategory);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  apiRouter.put('/categories/:id', async (req: Request, res: Response) => {\n    try {\n      const id = parseInt(req.params.id);\n      const category = await storage.getMenuCategoryById(id);\n\n      if (!category) {\n        return res.status(404).json({ error: 'Category not found' });\n      }\n\n      const updatedCategory = await storage.updateMenuCategory(id, req.body);\n      res.json(updatedCategory);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  apiRouter.delete('/categories/:id', async (req: Request, res: Response) => {\n    try {\n      const id = parseInt(req.params.id);\n      const category = await storage.getMenuCategoryById(id);\n\n      if (!category) {\n        return res.status(404).json({ error: 'Category not found' });\n      }\n\n      const deletedCategory = await storage.deleteMenuCategory(id);\n      res.json({ message: 'Category deleted successfully', category: deletedCategory });\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  // Menu Items Routes\n  apiRouter.get('/items', async (req: Request, res: Response) => {\n    try {\n      const items = await storage.getAllMenuItems();\n      res.json(items);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  apiRouter.post('/items', async (req: Request, res: Response) => {\n    try {\n      const newItem = await storage.createMenuItem(req.body);\n      res.status(201).json(newItem);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  apiRouter.put('/items/:id', async (req: Request, res: Response) => {\n    try {\n      const id = parseInt(req.params.id);\n      const item = await storage.getMenuItemById(id);\n\n      if (!item) {\n        return res.status(404).json({ error: 'Menu item not found' });\n      }\n\n      // Update logic would go here\n      res.json({ ...item, ...req.body });\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  // Order Routes\n  apiRouter.post('/orders', async (req: Request, res: Response) => {\n    try {\n      // Get restaurant settings to check if it's open\n      const adminSettings = await fetch('http://localhost:5000/api/admin/settings').then(res => res.json());\n\n      // Check if restaurant is open\n      if (!adminSettings.restaurant_open) {\n        return res.status(403).json({\n          error: 'Restaurant is currently closed. Orders cannot be placed at this time.'\n", "suffix": "        });\n      }\n\n      // If restaurant is open, proceed with order creation\n      const newOrder = await storage.createOrder(req.body);\n      res.status(201).json(newOrder);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  apiRouter.get('/orders/:id', async (req: Request, res: Response) => {\n    try {\n      const id = parseInt(req.params.id);\n      const order = await storage.getOrderById(id);\n\n      if (!order) {\n        return res.status(404).json({ error: 'Order not found' });\n      }\n\n      res.json(order);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  // Contact Form Route\n  apiRouter.post('/contact', async (req: Request, res: Response) => {\n    try {\n      const message = await storage.createContactMessage(req.body);\n      res.status(201).json({ success: true, message });\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  // Cart Routes\n  apiRouter.post('/cart', async (req: Request, res: Response) => {\n    try {\n      const result = await storage.createCart(req.body);\n      res.status(201).json(result);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  // Register the API router with app\n  app.use('/api', apiRouter);\n\n  // Setup authentication\n  setupAuth(app);\n\n  // Register admin API routes\n  app.use('/api/admin', adminApiRouter);\n\n  // Create and return the server\n  const server = http.createServer(app);\n  return server;\n}", "path": "server/routes.ts", "language": "typescript", "prefixBegin": 0, "suffixEnd": 1}}], ["881c07d9-5e40-4b05-ad0f-c0aeeb57b956", {"value": {"selectedCode": "", "prefix": "import { Express, Request, Response, NextFunction, Router } from 'express';\nimport http from 'http';\nimport { log } from './vite';\nimport { storage } from './storage';\nimport adminApiRouter from './admin-api';\nimport { setupAuth } from './auth';\n\nexport async function registerRoutes(app: Express): Promise<http.Server> {\n  const apiRouter = Router();\n\n  // Logging middleware\n  app.use((req: Request, _res: Response, next: NextFunction) => {\n    if (req.url.startsWith('/api')) {\n      log(`${req.method} ${req.url}`, 'api');\n    }\n    next();\n  });\n\n  // Reusable error handler\n  const handleError = (error: any, res: Response) => {\n    console.error(error);\n    res.status(500).json({ error: 'An unexpected error occurred' });\n  };\n\n  // Menu Items / Dishes Routes\n  apiRouter.get('/dishes', async (req: Request, res: Response) => {\n    try {\n      const dishes = await storage.getAllDishes();\n      res.json(dishes);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  apiRouter.get('/dishes/:id', async (req: Request, res: Response) => {\n    try {\n      const id = parseInt(req.params.id);\n      const dish = await storage.getDishById(id);\n\n      if (!dish) {\n        return res.status(404).json({ error: 'Dish not found' });\n      }\n\n      res.json(dish);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  // Categories Routes\n  apiRouter.get('/categories', async (req: Request, res: Response) => {\n    try {\n      const categories = await storage.getAllMenuCategories();\n      res.json(categories);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  apiRouter.post('/categories', async (req: Request, res: Response) => {\n    try {\n      if (!req.body.name) {\n        return res.status(400).json({ error: 'Category name is required' });\n      }\n\n      const newCategory = await storage.createMenuCategory(req.body);\n      res.status(201).json(newCategory);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  apiRouter.put('/categories/:id', async (req: Request, res: Response) => {\n    try {\n      const id = parseInt(req.params.id);\n      const category = await storage.getMenuCategoryById(id);\n\n      if (!category) {\n        return res.status(404).json({ error: 'Category not found' });\n      }\n\n      const updatedCategory = await storage.updateMenuCategory(id, req.body);\n      res.json(updatedCategory);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  apiRouter.delete('/categories/:id', async (req: Request, res: Response) => {\n    try {\n      const id = parseInt(req.params.id);\n      const category = await storage.getMenuCategoryById(id);\n\n      if (!category) {\n        return res.status(404).json({ error: 'Category not found' });\n      }\n\n      const deletedCategory = await storage.deleteMenuCategory(id);\n      res.json({ message: 'Category deleted successfully', category: deletedCategory });\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  // Menu Items Routes\n  apiRouter.get('/items', async (req: Request, res: Response) => {\n    try {\n      const items = await storage.getAllMenuItems();\n      res.json(items);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  apiRouter.post('/items', async (req: Request, res: Response) => {\n    try {\n      const newItem = await storage.createMenuItem(req.body);\n      res.status(201).json(newItem);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  apiRouter.put('/items/:id', async (req: Request, res: Response) => {\n    try {\n      const id = parseInt(req.params.id);\n      const item = await storage.getMenuItemById(id);\n\n      if (!item) {\n        return res.status(404).json({ error: 'Menu item not found' });\n      }\n\n      // Update logic would go here\n      res.json({ ...item, ...req.body });\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  // Order Routes\n  apiRouter.post('/orders', async (req: Request, res: Response) => {\n    try {\n      // Get restaurant settings to check if it's open\n      const adminSettings = await fetch('http://localhost:5000/api/admin/settings').then(res => res.json());\n\n      // Check if restaurant is open\n      if (!adminSettings.restaurant_open) {\n        return res.status(403).json({\n          error: 'Restaurant is currently closed. Orders cannot be placed at this time.'\n", "suffix": "        });\n      }\n\n      // If restaurant is open, proceed with order creation\n      const newOrder = await storage.createOrder(req.body);\n      res.status(201).json(newOrder);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  apiRouter.get('/orders/:id', async (req: Request, res: Response) => {\n    try {\n      const id = parseInt(req.params.id);\n      const order = await storage.getOrderById(id);\n\n      if (!order) {\n        return res.status(404).json({ error: 'Order not found' });\n      }\n\n      res.json(order);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  // Contact Form Route\n  apiRouter.post('/contact', async (req: Request, res: Response) => {\n    try {\n      const message = await storage.createContactMessage(req.body);\n      res.status(201).json({ success: true, message });\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  // Cart Routes\n  apiRouter.post('/cart', async (req: Request, res: Response) => {\n    try {\n      const result = await storage.createCart(req.body);\n      res.status(201).json(result);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  // Register the API router with app\n  app.use('/api', apiRouter);\n\n  // Setup authentication\n  setupAuth(app);\n\n  // Register admin API routes\n  app.use('/api/admin', adminApiRouter);\n\n  // Create and return the server\n  const server = http.createServer(app);\n  return server;\n}", "path": "server/routes.ts", "language": "typescript", "prefixBegin": 0, "suffixEnd": 1}}], ["6e55cf67-f2c0-413d-9dd9-8119e2541e84", {"value": {"selectedCode": "", "prefix": "import { pgTable, text, serial, integer, boolean, jsonb, timestamp, foreignKey } from \"drizzle-orm/pg-core\";\n", "suffix": "import { createInsertSchema } from \"drizzle-zod\";\nimport { z } from \"zod\";\n\n// Categories Schema\nexport const categories = pgTable(\"categories\", {\n  id: serial(\"id\").primaryKey(),\n  name: text(\"name\").notNull(),\n  imageUrl: text(\"image_url\").notNull(),\n});\n\nexport const insertCategorySchema = createInsertSchema(categories).omit({ id: true });\nexport type InsertCategory = z.infer<typeof insertCategorySchema>;\nexport type Category = typeof categories.$inferSelect;\n\n// Dish Schema (renamed to menu_items to match the new API structure)\nexport const menuItems = pgTable(\"menu_items\", {\n  id: serial(\"id\").primaryKey(),\n  name: text(\"name\").notNull(),\n  description: text(\"description\").notNull(),\n  price: integer(\"price\").notNull(), // Price in NOK\n  imageUrl: text(\"image_url\").notNull(),\n  categoryId: integer(\"category_id\").notNull().references(() => categories.id),\n  available: boolean(\"available\").default(true),\n  rating: integer(\"rating\").default(0),\n  reviews: integer(\"reviews\").default(0),\n});\n\nexport const insertMenuItemSchema = createInsertSchema(menuItems).omit({ id: true });\nexport type InsertMenuItem = z.infer<typeof insertMenuItemSchema>;\nexport type MenuItem = typeof menuItems.$inferSelect;\n\n// Customization Groups Schema\nexport const customizationGroups = pgTable(\"customization_groups\", {\n  id: serial(\"id\").primaryKey(),\n  title: text(\"title\").notNull(),\n});\n\nexport const insertCustomizationGroupSchema = createInsertSchema(customizationGroups).omit({ id: true });\nexport type InsertCustomizationGroup = z.infer<typeof insertCustomizationGroupSchema>;\nexport type CustomizationGroup = typeof customizationGroups.$inferSelect;\n\n// Customization Options Schema\nexport const customizationOptions = pgTable(\"customization_options\", {\n  id: serial(\"id\").primaryKey(),\n  name: text(\"name\").notNull(),\n  extraPrice: integer(\"extra_price\").default(0),\n  imageUrl: text(\"image_url\").notNull().default(\"\"),\n  groupId: integer(\"group_id\").notNull().references(() => customizationGroups.id),\n});\n\nexport const insertCustomizationOptionSchema = createInsertSchema(customizationOptions).omit({ id: true });\nexport type InsertCustomizationOption = z.infer<typeof insertCustomizationOptionSchema>;\nexport type CustomizationOption = typeof customizationOptions.$inferSelect;\n\n// Item Customization Map (many-to-many relationship)\nexport const itemCustomizationMap = pgTable(\"item_customization_map\", {\n  id: serial(\"id\").primaryKey(),\n  itemId: integer(\"item_id\").notNull().references(() => menuItems.id),\n  optionId: integer(\"option_id\").notNull().references(() => customizationOptions.id),\n});\n\nexport const insertItemCustomizationMapSchema = createInsertSchema(itemCustomizationMap).omit({ id: true });\nexport type InsertItemCustomizationMap = z.infer<typeof insertItemCustomizationMapSchema>;\nexport type ItemCustomizationMap = typeof itemCustomizationMap.$inferSelect;\n\n// For backwards compatibility with existing code\nexport const dishes = menuItems;\nexport const insertDishSchema = insertMenuItemSchema;\nexport type InsertDish = InsertMenuItem;\nexport type Dish = MenuItem;\n\n// Order Schema\nexport const orders = pgTable(\"orders\", {\n  id: serial(\"id\").primaryKey(),\n  customer: jsonb(\"customer\").notNull(),\n  items: jsonb(\"items\").notNull(),\n  subtotal: integer(\"subtotal\").notNull(),\n  deliveryFee: integer(\"delivery_fee\").notNull(),\n  total: integer(\"total\").notNull(),\n  status: text(\"status\").notNull().default(\"pending\"),\n  paymentMethod: text(\"payment_method\").notNull(),\n  notes: text(\"notes\"),\n  createdAt: timestamp(\"created_at\").defaultNow(),\n});\n\nexport const insertOrderSchema = createInsertSchema(orders).omit({\n  id: true,\n  createdAt: true\n});\nexport type InsertOrder = z.infer<typeof insertOrderSchema>;\nexport type Order = typeof orders.$inferSelect;\n\n// Contact Message Schema\nexport const contactMessages = pgTable(\"contact_messages\", {\n  id: serial(\"id\").primaryKey(),\n  name: text(\"name\").notNull(),\n  email: text(\"email\").notNull(),\n  subject: text(\"subject\").notNull(),\n  message: text(\"message\").notNull(),\n  createdAt: timestamp(\"created_at\").defaultNow(),\n});\n\nexport const insertContactSchema = createInsertSchema(contactMessages).omit({\n  id: true,\n  createdAt: true\n});\nexport type InsertContactMessage = z.infer<typeof insertContactSchema>;\nexport type ContactMessage = typeof contactMessages.$inferSelect;\n\n// User Schema\nexport const users = pgTable(\"users\", {\n  id: serial(\"id\").primaryKey(),\n  username: text(\"username\").notNull().unique(),\n  email: text(\"email\").notNull().unique(),\n  password: text(\"password\").notNull(),\n  first_name: text(\"first_name\"),\n  last_name: text(\"last_name\"),\n  role: text(\"role\").notNull().default(\"customer\"),\n  is_active: boolean(\"is_active\").notNull().default(true),\n  created_at: timestamp(\"created_at\").defaultNow(),\n  updated_at: timestamp(\"updated_at\").defaultNow(),\n});\n\nexport const insertUserSchema = createInsertSchema(users).omit({\n  id: true,\n  created_at: true,\n  updated_at: true,\n});\n\nexport const loginUserSchema = z.object({\n  username: z.string().min(3).max(50),\n  password: z.string().min(6),\n});\n\nexport type InsertUser = z.infer<typeof insertUserSchema>;\nexport type LoginUser = z.infer<typeof loginUserSchema>;\nexport type User = typeof users.$inferSelect;\n\n// Restaurant Settings Schema\nexport const restaurantSettings = pgTable(\"restaurant_settings\", {\n  id: serial(\"id\").primaryKey(),\n  restaurantOpen: boolean(\"restaurant_open\").default(true),\n  businessHours: jsonb(\"business_hours\").default({}),\n  deliveryFee: integer(\"delivery_fee\").default(49),\n  estimatedTime: text(\"estimated_time\").default(\"25-35 min\"),\n});\n\nexport const insertRestaurantSettingsSchema = createInsertSchema(restaurantSettings).omit({ id: true });\nexport type InsertRestaurantSettings = z.infer<typeof insertRestaurantSettingsSchema>;\nexport type RestaurantSettings = typeof restaurantSettings.$inferSelect;\n", "path": "shared/schema.ts", "language": "typescript", "prefixBegin": 0, "suffixEnd": 0}}], ["1654c990-3c3c-4658-b46e-570a4baf2101", {"value": {"selectedCode": "", "prefix": "import { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport {\n  Clock,\n  CheckCircle2,\n  X,\n  Truck,\n  Bar<PERSON>hart,\n  ChefHat,\n  ShoppingBag,\n  Package,\n  AlertTriangle\n} from 'lucide-react';\nimport AdminLayout from './AdminLayout';\nimport SingleUpdateButton from '@/components/admin/SingleUpdateButton';\n// We'll use native fetch instead of apiRequest for this component\nimport { format, parseISO } from 'date-fns';\n\n// Status options for orders\nconst ORDER_STATUSES = [\n  { value: 'confirmed', label: 'Confirmed', icon: <CheckCircle2 className=\"w-4 h-4\" /> },\n  { value: 'processing', label: 'Processing', icon: <Clock className=\"w-4 h-4\" /> },\n  { value: 'preparing', label: 'Preparing', icon: <ChefHat className=\"w-4 h-4\" /> },\n  { value: 'ready_for_pickup', label: 'Ready for Pickup', icon: <Package className=\"w-4 h-4\" /> },\n  { value: 'ready_for_delivery', label: 'Ready for Delivery', icon: <ShoppingBag className=\"w-4 h-4\" /> },\n  { value: 'out_for_delivery', label: 'Out for Delivery', icon: <Truck className=\"w-4 h-4\" /> },\n  { value: 'delivered', label: 'Delivered', icon: <CheckCircle2 className=\"w-4 h-4\" /> },\n  { value: 'completed', label: 'Completed', icon: <BarChart className=\"w-4 h-4\" /> },\n  { value: 'cancelled', label: 'Cancelled', icon: <X className=\"w-4 h-4\" /> }\n];\n\n// Format currency for display\nconst formatCurrency = (amount: number) => {\n  return `$${(amount / 100).toFixed(2)}`;\n};\n\n// Format date for display\nconst formatDate = (dateString: string) => {\n  try {\n    const date = parseISO(dateString);\n    return format(date, 'MMM d, h:mm a');\n  } catch (e) {\n    return dateString;\n  }\n};\n\ninterface OrderItem {\n  id: number;\n  name: string;\n  price: number;\n  quantity: number;\n}\n\ninterface OrderCustomer {\n  firstName: string;\n  lastName: string;\n  phone: string;\n  email: string;\n}\n\ninterface OrderDetails {\n  type?: 'delivery' | 'takeaway';\n  time?: 'asap' | 'scheduled';\n  scheduledTime?: string | null;\n}\n\ninterface Order {\n  id: number;\n  createdAt: string;\n  status: string;\n  items: OrderItem[];\n  customer: OrderCustomer;\n  orderDetails?: OrderDetails;\n  subtotal: number;\n  deliveryFee: number;\n  total: number;\n  paymentMethod: string;\n  notes: string | null;\n}\n\n// Component for status badge\nconst StatusBadge = ({ status }: { status: string }) => {\n  const statusObj = ORDER_STATUSES.find(s => s.value === status) || {\n    value: status,\n    label: status.charAt(0).toUpperCase() + status.slice(1).replace('_', ' '),\n    icon: <AlertTriangle className=\"w-4 h-4\" />\n  };\n\n  const getBadgeColor = () => {\n    switch (status) {\n      case 'confirmed':\n        return 'bg-blue-900/30 text-blue-400 border-blue-700/30';\n      case 'processing':\n        return 'bg-purple-900/30 text-purple-400 border-purple-700/30';\n      case 'preparing':\n        return 'bg-yellow-900/30 text-yellow-400 border-yellow-700/30';\n      case 'ready_for_pickup':\n      case 'ready_for_delivery':\n        return 'bg-green-900/30 text-green-400 border-green-700/30';\n      case 'out_for_delivery':\n        return 'bg-cyan-900/30 text-cyan-400 border-cyan-700/30';\n      case 'delivered':\n      case 'completed':\n        return 'bg-emerald-900/30 text-emerald-400 border-emerald-700/30';\n      case 'cancelled':\n        return 'bg-red-900/30 text-red-400 border-red-700/30';\n      default:\n        return 'bg-gray-900/30 text-gray-400 border-gray-700/30';\n    }\n  };\n\n  return (\n    <span className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium border ${getBadgeColor()}`}>\n      <span className=\"mr-1\">{statusObj.icon}</span>\n      {statusObj.label}\n    </span>\n  );\n};\n\nconst OrderManager = () => {\n  const [orders, setOrders] = useState<Order[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [statusFilter, setStatusFilter] = useState('active');\n  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);\n  const [isUpdating, setIsUpdating] = useState(false);\n\n  // Fetch orders on component mount and when status filter changes\n  useEffect(() => {\n    const fetchOrders = async () => {\n      setLoading(true);\n      try {\n        const response = await fetch(`/api/admin/orders?status=${statusFilter}`);\n        if (!response.ok) {\n          throw new Error(`Failed to fetch orders: ${response.status}`);\n        }\n        const data = await response.json();\n        setOrders(data);\n        setError(null);\n      } catch (err) {\n        console.error('Error fetching orders:', err);\n        setError('Failed to load orders. Please try again.');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchOrders();\n  }, [statusFilter]);\n\n  // Determine the next status based on current status and order type\n  const getNextStatus = (order: Order): string | null => {\n    const { status, orderDetails } = order;\n    const orderType = orderDetails.type;\n", "suffix": "\n    switch (status) {\n      case 'confirmed':\n        return 'preparing';\n      case 'preparing':\n        return orderType === 'delivery' ? 'ready_for_delivery' : 'ready_for_pickup';\n      case 'ready_for_pickup':\n        return 'completed';\n      case 'ready_for_delivery':\n        return 'out_for_delivery';\n      case 'out_for_delivery':\n        return 'delivered';\n      case 'delivered':\n        return 'completed';\n      default:\n        return null;\n    }\n  };\n\n  // Get human-readable label for the next status\n  const getNextStatusLabel = (order: Order): string => {\n    const nextStatus = getNextStatus(order);\n    if (!nextStatus) return '';\n\n    // Find the status object that matches the next status\n    const statusObj = ORDER_STATUSES.find(s => s.value === nextStatus);\n    return statusObj ? statusObj.label : nextStatus.replace('_', ' ').replace(/\\b\\w/g, l => l.toUpperCase());\n  };\n\n  // Update order status\n  const updateOrderStatus = async (orderId: number, newStatus: string) => {\n    if (!newStatus) return;\n\n    setIsUpdating(true);\n    try {\n      const response = await fetch(`/api/admin/orders/${orderId}/status`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({ newStatus })\n      });\n\n      if (!response.ok) {\n        throw new Error(`Failed to update order status: ${response.status}`);\n      }\n\n      // Update the orders list with the new status\n      setOrders(orders.map(order =>\n        order.id === orderId\n          ? { ...order, status: newStatus }\n          : order\n      ));\n\n      // If the selected order is the one being updated, update it as well\n      if (selectedOrder && selectedOrder.id === orderId) {\n        setSelectedOrder({ ...selectedOrder, status: newStatus });\n      }\n\n      // Show success notification\n      console.log(`Order #${orderId} status updated to ${newStatus}`);\n\n    } catch (err) {\n      console.error('Error updating order status:', err);\n      setError('Failed to update order status. Please try again.');\n    } finally {\n      setIsUpdating(false);\n    }\n  };\n\n  // Handle order selection\n  const handleOrderClick = (order: Order) => {\n    setSelectedOrder(order);\n  };\n\n  return (\n    <AdminLayout>\n      <motion.div\n        className=\"space-y-6\"\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.3 }}\n      >\n        <div className=\"flex justify-between items-center\">\n          <h1 className=\"text-2xl font-bold bg-gradient-to-r from-orange-400 to-red-500 text-transparent bg-clip-text\">\n            Kitchen Order Manager\n          </h1>\n          <div className=\"flex space-x-2\">\n            <select\n              value={statusFilter}\n              onChange={(e) => setStatusFilter(e.target.value)}\n              className=\"bg-gray-900 border border-gray-700 rounded-lg px-3 py-2 text-sm text-white focus:ring-cyan-500 focus:border-cyan-500\"\n            >\n              <option value=\"active\">Active Orders</option>\n              {ORDER_STATUSES.map(status => (\n                <option key={status.value} value={status.value}>\n                  {status.label} Only\n                </option>\n              ))}\n              <option value=\"all\">All Orders</option>\n            </select>\n          </div>\n        </div>\n\n        {error && (\n          <div className=\"bg-red-900/40 border border-red-700 text-red-400 p-4 rounded-lg\">\n            {error}\n          </div>\n        )}\n\n        {loading ? (\n          <div className=\"flex justify-center items-center py-12\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-cyan-500\"></div>\n          </div>\n        ) : (\n          <div className=\"grid grid-cols-1 xl:grid-cols-[1fr_1.5fr] gap-6\">\n            {/* Orders List */}\n            <div className=\"bg-gray-900/50 rounded-xl border border-gray-800 overflow-hidden backdrop-blur-sm h-[calc(100vh-220px)] flex flex-col\">\n              <div className=\"p-4 border-b border-gray-800 bg-gray-900/80\">\n                <h2 className=\"text-lg font-medium text-white flex items-center\">\n                  <ShoppingBag className=\"w-5 h-5 mr-2 text-orange-400\" />\n                  Orders ({orders.length})\n                </h2>\n              </div>\n\n              <div className=\"flex-1 overflow-y-auto\">\n                {orders.length === 0 ? (\n                  <div className=\"flex flex-col items-center justify-center h-full p-6 text-center\">\n                    <ShoppingBag className=\"w-16 h-16 text-gray-600 mb-4\" />\n                    <p className=\"text-gray-400\">No orders found matching your filter.</p>\n                    {statusFilter !== 'all' && (\n                      <button\n                        onClick={() => setStatusFilter('all')}\n                        className=\"mt-4 text-cyan-400 hover:text-cyan-300\"\n                      >\n                        View all orders\n                      </button>\n                    )}\n                  </div>\n                ) : (\n                  <ul className=\"divide-y divide-gray-800\">\n                    {orders.map(order => (\n                      <li\n                        key={order.id}\n                        className={`p-4 hover:bg-gray-800/50 cursor-pointer transition-colors ${selectedOrder?.id === order.id ? 'bg-gray-800/70' : ''}`}\n                        onClick={() => handleOrderClick(order)}\n                      >\n                        <div className=\"flex justify-between items-center mb-2\">\n                          <span className=\"font-medium text-white\">Order #{order.id}</span>\n                          <StatusBadge status={order.status} />\n                        </div>\n                        <div className=\"flex justify-between text-sm text-gray-400 mb-2\">\n                          <span>{formatDate(order.createdAt)}</span>\n                          <span className=\"font-medium text-white\">{formatCurrency(order.total)}</span>\n                        </div>\n                        <div className=\"flex justify-between text-sm\">\n                          <span className=\"text-gray-400\">\n                            {order.customer.firstName} {order.customer.lastName}\n                          </span>\n                          <span className=\"text-cyan-400 font-medium\">\n                            {order.orderDetails?.type === 'delivery' ? 'Delivery' : 'Takeaway'}\n                          </span>\n                        </div>\n                      </li>\n                    ))}\n                  </ul>\n                )}\n              </div>\n            </div>\n\n            {/* Order Details */}\n            <div className=\"bg-gray-900/50 rounded-xl border border-gray-800 overflow-hidden backdrop-blur-sm h-[calc(100vh-220px)] flex flex-col\">\n              {selectedOrder ? (\n                <>\n                  <div className=\"p-4 border-b border-gray-800 bg-gray-900/80 flex justify-between items-center\">\n                    <h2 className=\"text-lg font-medium text-white\">Order #{selectedOrder.id} Details</h2>\n                    <StatusBadge status={selectedOrder.status} />\n                  </div>\n\n                  <div className=\"flex-1 overflow-y-auto p-4 space-y-6\">\n                    {/* Order Info */}\n                    <div className=\"grid grid-cols-2 gap-4 text-sm\">\n                      <div className=\"space-y-1\">\n                        <p className=\"text-gray-500\">Order Date</p>\n                        <p className=\"text-white\">{formatDate(selectedOrder.createdAt)}</p>\n                      </div>\n                      <div className=\"space-y-1\">\n                        <p className=\"text-gray-500\">Order Type</p>\n                        <p className=\"text-white capitalize\">{selectedOrder.orderDetails?.type || 'N/A'}</p>\n                      </div>\n                      <div className=\"space-y-1\">\n                        <p className=\"text-gray-500\">Payment Method</p>\n                        <p className=\"text-white capitalize\">{selectedOrder.paymentMethod}</p>\n                      </div>\n                      <div className=\"space-y-1\">\n                        <p className=\"text-gray-500\">Timing</p>\n                        <p className=\"text-white capitalize\">\n                          {selectedOrder.orderDetails?.time === 'asap'\n                            ? 'ASAP'\n                            : selectedOrder.orderDetails?.time === 'scheduled'\n                            ? `Scheduled: ${selectedOrder.orderDetails?.scheduledTime\n                                ? formatDate(selectedOrder.orderDetails.scheduledTime)\n                                : 'N/A'}`\n                            : 'N/A'\n                          }\n                        </p>\n                      </div>\n                    </div>\n\n                    {/* Customer Info */}\n                    <div>\n                      <h3 className=\"text-md font-medium text-white mb-3\">Customer Information</h3>\n                      <div className=\"bg-gray-800/50 rounded-lg p-4 text-sm space-y-2\">\n                        <p className=\"text-white\">\n                          {selectedOrder.customer.firstName} {selectedOrder.customer.lastName}\n                        </p>\n                        <p className=\"text-gray-400\">{selectedOrder.customer.email}</p>\n                        <p className=\"text-gray-400\">{selectedOrder.customer.phone}</p>\n                      </div>\n                    </div>\n\n                    {/* Order Items */}\n                    <div>\n                      <h3 className=\"text-md font-medium text-white mb-3\">Order Items</h3>\n                      <div className=\"bg-gray-800/50 rounded-lg overflow-hidden\">\n                        <ul className=\"divide-y divide-gray-700\">\n                          {selectedOrder.items.map(item => (\n                            <li key={item.id} className=\"p-3 flex justify-between\">\n                              <div className=\"flex items-start\">\n                                <span className=\"text-orange-400 font-medium mr-2\">{item.quantity}x</span>\n                                <span className=\"text-white\">{item.name}</span>\n                              </div>\n                              <span className=\"text-gray-300\">{formatCurrency(item.price * item.quantity)}</span>\n                            </li>\n                          ))}\n                        </ul>\n\n                        {/* Order Totals */}\n                        <div className=\"border-t border-gray-700 p-3 space-y-1 text-sm\">\n                          <div className=\"flex justify-between\">\n                            <span className=\"text-gray-400\">Subtotal</span>\n                            <span className=\"text-white\">{formatCurrency(selectedOrder.subtotal)}</span>\n                          </div>\n                          <div className=\"flex justify-between\">\n                            <span className=\"text-gray-400\">Delivery Fee</span>\n                            <span className=\"text-white\">{formatCurrency(selectedOrder.deliveryFee)}</span>\n                          </div>\n                          <div className=\"flex justify-between font-medium pt-1\">\n                            <span className=\"text-gray-300\">Total</span>\n                            <span className=\"text-white\">{formatCurrency(selectedOrder.total)}</span>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n\n                    {/* Order Notes */}\n                    {selectedOrder.notes && (\n                      <div>\n                        <h3 className=\"text-md font-medium text-white mb-3\">Special Instructions</h3>\n                        <div className=\"bg-gray-800/50 rounded-lg p-4 text-sm text-gray-300\">\n                          {selectedOrder.notes}\n                        </div>\n                      </div>\n                    )}\n\n                    {/* Status Management - Simplified with single update button */}\n                    <div>\n                      <h3 className=\"text-md font-medium text-white mb-3\">Update Order Status</h3>\n                      <div className=\"bg-gray-800/50 rounded-lg p-4\">\n                        <SingleUpdateButton\n                          currentStatus={selectedOrder.status}\n                          nextStatus={getNextStatus(selectedOrder)}\n                          nextStatusLabel={getNextStatusLabel(selectedOrder)}\n                          isUpdating={isUpdating}\n                          onUpdate={() => {\n                            const nextStatus = getNextStatus(selectedOrder);\n                            if (nextStatus) {\n                              updateOrderStatus(selectedOrder.id, nextStatus);\n                            }\n                          }}\n                        />\n                      </div>\n                    </div>\n                  </div>\n                </>\n              ) : (\n                <div className=\"flex flex-col items-center justify-center h-full p-6 text-center\">\n                  <Package className=\"w-16 h-16 text-gray-600 mb-4\" />\n                  <p className=\"text-gray-400\">Select an order to see details.</p>\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n      </motion.div>\n    </AdminLayout>\n  );\n};\n\nexport default OrderManager;", "path": "client/src/pages/admin/OrderManager.tsx", "language": "typescriptreact", "prefixBegin": 0, "suffixEnd": 28}}], ["e9438891-881a-4562-a5c6-c473c0844bfc", {"value": {"selectedCode": "", "prefix": "import { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport {\n  Clock,\n  CheckCircle2,\n  X,\n  Truck,\n  Bar<PERSON>hart,\n  ChefHat,\n  ShoppingBag,\n  Package,\n  AlertTriangle\n} from 'lucide-react';\nimport AdminLayout from './AdminLayout';\nimport SingleUpdateButton from '@/components/admin/SingleUpdateButton';\n// We'll use native fetch instead of apiRequest for this component\nimport { format, parseISO } from 'date-fns';\n\n// Status options for orders\nconst ORDER_STATUSES = [\n  { value: 'confirmed', label: 'Confirmed', icon: <CheckCircle2 className=\"w-4 h-4\" /> },\n  { value: 'processing', label: 'Processing', icon: <Clock className=\"w-4 h-4\" /> },\n  { value: 'preparing', label: 'Preparing', icon: <ChefHat className=\"w-4 h-4\" /> },\n  { value: 'ready_for_pickup', label: 'Ready for Pickup', icon: <Package className=\"w-4 h-4\" /> },\n  { value: 'ready_for_delivery', label: 'Ready for Delivery', icon: <ShoppingBag className=\"w-4 h-4\" /> },\n  { value: 'out_for_delivery', label: 'Out for Delivery', icon: <Truck className=\"w-4 h-4\" /> },\n  { value: 'delivered', label: 'Delivered', icon: <CheckCircle2 className=\"w-4 h-4\" /> },\n  { value: 'completed', label: 'Completed', icon: <BarChart className=\"w-4 h-4\" /> },\n  { value: 'cancelled', label: 'Cancelled', icon: <X className=\"w-4 h-4\" /> }\n];\n\n// Format currency for display\nconst formatCurrency = (amount: number) => {\n  return `$${(amount / 100).toFixed(2)}`;\n};\n\n// Format date for display\nconst formatDate = (dateString: string) => {\n  try {\n    const date = parseISO(dateString);\n    return format(date, 'MMM d, h:mm a');\n  } catch (e) {\n    return dateString;\n  }\n};\n\ninterface OrderItem {\n  id: number;\n  name: string;\n  price: number;\n  quantity: number;\n}\n\ninterface OrderCustomer {\n  firstName: string;\n  lastName: string;\n  phone: string;\n  email: string;\n}\n\ninterface OrderDetails {\n  type?: 'delivery' | 'takeaway';\n  time?: 'asap' | 'scheduled';\n  scheduledTime?: string | null;\n}\n\ninterface Order {\n  id: number;\n  createdAt: string;\n  status: string;\n  items: OrderItem[];\n  customer: OrderCustomer;\n  orderDetails?: OrderDetails;\n  subtotal: number;\n  deliveryFee: number;\n  total: number;\n  paymentMethod: string;\n  notes: string | null;\n}\n\n// Component for status badge\nconst StatusBadge = ({ status }: { status: string }) => {\n  const statusObj = ORDER_STATUSES.find(s => s.value === status) || {\n    value: status,\n    label: status.charAt(0).toUpperCase() + status.slice(1).replace('_', ' '),\n    icon: <AlertTriangle className=\"w-4 h-4\" />\n  };\n\n  const getBadgeColor = () => {\n    switch (status) {\n      case 'confirmed':\n        return 'bg-blue-900/30 text-blue-400 border-blue-700/30';\n      case 'processing':\n        return 'bg-purple-900/30 text-purple-400 border-purple-700/30';\n      case 'preparing':\n        return 'bg-yellow-900/30 text-yellow-400 border-yellow-700/30';\n      case 'ready_for_pickup':\n      case 'ready_for_delivery':\n        return 'bg-green-900/30 text-green-400 border-green-700/30';\n      case 'out_for_delivery':\n        return 'bg-cyan-900/30 text-cyan-400 border-cyan-700/30';\n      case 'delivered':\n      case 'completed':\n        return 'bg-emerald-900/30 text-emerald-400 border-emerald-700/30';\n      case 'cancelled':\n        return 'bg-red-900/30 text-red-400 border-red-700/30';\n      default:\n        return 'bg-gray-900/30 text-gray-400 border-gray-700/30';\n    }\n  };\n\n  return (\n    <span className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium border ${getBadgeColor()}`}>\n      <span className=\"mr-1\">{statusObj.icon}</span>\n      {statusObj.label}\n    </span>\n  );\n};\n\nconst OrderManager = () => {\n  const [orders, setOrders] = useState<Order[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [statusFilter, setStatusFilter] = useState('active');\n  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);\n  const [isUpdating, setIsUpdating] = useState(false);\n\n  // Fetch orders on component mount and when status filter changes\n  useEffect(() => {\n    const fetchOrders = async () => {\n      setLoading(true);\n      try {\n        const response = await fetch(`/api/admin/orders?status=${statusFilter}`);\n        if (!response.ok) {\n          throw new Error(`Failed to fetch orders: ${response.status}`);\n        }\n        const data = await response.json();\n        setOrders(data);\n        setError(null);\n      } catch (err) {\n        console.error('Error fetching orders:', err);\n        setError('Failed to load orders. Please try again.');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchOrders();\n  }, [statusFilter]);\n\n  // Determine the next status based on current status and order type\n  const getNextStatus = (order: Order): string | null => {\n    const { status, orderDetails } = order;\n    const orderType = orderDetails?.type;\n", "suffix": "\n    switch (status) {\n      case 'confirmed':\n        return 'preparing';\n      case 'preparing':\n        return orderType === 'delivery' ? 'ready_for_delivery' : 'ready_for_pickup';\n      case 'ready_for_pickup':\n        return 'completed';\n      case 'ready_for_delivery':\n        return 'out_for_delivery';\n      case 'out_for_delivery':\n        return 'delivered';\n      case 'delivered':\n        return 'completed';\n      default:\n        return null;\n    }\n  };\n\n  // Get human-readable label for the next status\n  const getNextStatusLabel = (order: Order): string => {\n    const nextStatus = getNextStatus(order);\n    if (!nextStatus) return '';\n\n    // Find the status object that matches the next status\n    const statusObj = ORDER_STATUSES.find(s => s.value === nextStatus);\n    return statusObj ? statusObj.label : nextStatus.replace('_', ' ').replace(/\\b\\w/g, l => l.toUpperCase());\n  };\n\n  // Update order status\n  const updateOrderStatus = async (orderId: number, newStatus: string) => {\n    if (!newStatus) return;\n\n    setIsUpdating(true);\n    try {\n      const response = await fetch(`/api/admin/orders/${orderId}/status`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({ newStatus })\n      });\n\n      if (!response.ok) {\n        throw new Error(`Failed to update order status: ${response.status}`);\n      }\n\n      // Update the orders list with the new status\n      setOrders(orders.map(order =>\n        order.id === orderId\n          ? { ...order, status: newStatus }\n          : order\n      ));\n\n      // If the selected order is the one being updated, update it as well\n      if (selectedOrder && selectedOrder.id === orderId) {\n        setSelectedOrder({ ...selectedOrder, status: newStatus });\n      }\n\n      // Show success notification\n      console.log(`Order #${orderId} status updated to ${newStatus}`);\n\n    } catch (err) {\n      console.error('Error updating order status:', err);\n      setError('Failed to update order status. Please try again.');\n    } finally {\n      setIsUpdating(false);\n    }\n  };\n\n  // Handle order selection\n  const handleOrderClick = (order: Order) => {\n    setSelectedOrder(order);\n  };\n\n  return (\n    <AdminLayout>\n      <motion.div\n        className=\"space-y-6\"\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.3 }}\n      >\n        <div className=\"flex justify-between items-center\">\n          <h1 className=\"text-2xl font-bold bg-gradient-to-r from-orange-400 to-red-500 text-transparent bg-clip-text\">\n            Kitchen Order Manager\n          </h1>\n          <div className=\"flex space-x-2\">\n            <select\n              value={statusFilter}\n              onChange={(e) => setStatusFilter(e.target.value)}\n              className=\"bg-gray-900 border border-gray-700 rounded-lg px-3 py-2 text-sm text-white focus:ring-cyan-500 focus:border-cyan-500\"\n            >\n              <option value=\"active\">Active Orders</option>\n              {ORDER_STATUSES.map(status => (\n                <option key={status.value} value={status.value}>\n                  {status.label} Only\n                </option>\n              ))}\n              <option value=\"all\">All Orders</option>\n            </select>\n          </div>\n        </div>\n\n        {error && (\n          <div className=\"bg-red-900/40 border border-red-700 text-red-400 p-4 rounded-lg\">\n            {error}\n          </div>\n        )}\n\n        {loading ? (\n          <div className=\"flex justify-center items-center py-12\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-cyan-500\"></div>\n          </div>\n        ) : (\n          <div className=\"grid grid-cols-1 xl:grid-cols-[1fr_1.5fr] gap-6\">\n            {/* Orders List */}\n            <div className=\"bg-gray-900/50 rounded-xl border border-gray-800 overflow-hidden backdrop-blur-sm h-[calc(100vh-220px)] flex flex-col\">\n              <div className=\"p-4 border-b border-gray-800 bg-gray-900/80\">\n                <h2 className=\"text-lg font-medium text-white flex items-center\">\n                  <ShoppingBag className=\"w-5 h-5 mr-2 text-orange-400\" />\n                  Orders ({orders.length})\n                </h2>\n              </div>\n\n              <div className=\"flex-1 overflow-y-auto\">\n                {orders.length === 0 ? (\n                  <div className=\"flex flex-col items-center justify-center h-full p-6 text-center\">\n                    <ShoppingBag className=\"w-16 h-16 text-gray-600 mb-4\" />\n                    <p className=\"text-gray-400\">No orders found matching your filter.</p>\n                    {statusFilter !== 'all' && (\n                      <button\n                        onClick={() => setStatusFilter('all')}\n                        className=\"mt-4 text-cyan-400 hover:text-cyan-300\"\n                      >\n                        View all orders\n                      </button>\n                    )}\n                  </div>\n                ) : (\n                  <ul className=\"divide-y divide-gray-800\">\n                    {orders.map(order => (\n                      <li\n                        key={order.id}\n                        className={`p-4 hover:bg-gray-800/50 cursor-pointer transition-colors ${selectedOrder?.id === order.id ? 'bg-gray-800/70' : ''}`}\n                        onClick={() => handleOrderClick(order)}\n                      >\n                        <div className=\"flex justify-between items-center mb-2\">\n                          <span className=\"font-medium text-white\">Order #{order.id}</span>\n                          <StatusBadge status={order.status} />\n                        </div>\n                        <div className=\"flex justify-between text-sm text-gray-400 mb-2\">\n                          <span>{formatDate(order.createdAt)}</span>\n                          <span className=\"font-medium text-white\">{formatCurrency(order.total)}</span>\n                        </div>\n                        <div className=\"flex justify-between text-sm\">\n                          <span className=\"text-gray-400\">\n                            {order.customer.firstName} {order.customer.lastName}\n                          </span>\n                          <span className=\"text-cyan-400 font-medium\">\n                            {order.orderDetails?.type === 'delivery' ? 'Delivery' : 'Takeaway'}\n                          </span>\n                        </div>\n                      </li>\n                    ))}\n                  </ul>\n                )}\n              </div>\n            </div>\n\n            {/* Order Details */}\n            <div className=\"bg-gray-900/50 rounded-xl border border-gray-800 overflow-hidden backdrop-blur-sm h-[calc(100vh-220px)] flex flex-col\">\n              {selectedOrder ? (\n                <>\n                  <div className=\"p-4 border-b border-gray-800 bg-gray-900/80 flex justify-between items-center\">\n                    <h2 className=\"text-lg font-medium text-white\">Order #{selectedOrder.id} Details</h2>\n                    <StatusBadge status={selectedOrder.status} />\n                  </div>\n\n                  <div className=\"flex-1 overflow-y-auto p-4 space-y-6\">\n                    {/* Order Info */}\n                    <div className=\"grid grid-cols-2 gap-4 text-sm\">\n                      <div className=\"space-y-1\">\n                        <p className=\"text-gray-500\">Order Date</p>\n                        <p className=\"text-white\">{formatDate(selectedOrder.createdAt)}</p>\n                      </div>\n                      <div className=\"space-y-1\">\n                        <p className=\"text-gray-500\">Order Type</p>\n                        <p className=\"text-white capitalize\">{selectedOrder.orderDetails?.type || 'N/A'}</p>\n                      </div>\n                      <div className=\"space-y-1\">\n                        <p className=\"text-gray-500\">Payment Method</p>\n                        <p className=\"text-white capitalize\">{selectedOrder.paymentMethod}</p>\n                      </div>\n                      <div className=\"space-y-1\">\n                        <p className=\"text-gray-500\">Timing</p>\n                        <p className=\"text-white capitalize\">\n                          {selectedOrder.orderDetails?.time === 'asap'\n                            ? 'ASAP'\n                            : selectedOrder.orderDetails?.time === 'scheduled'\n                            ? `Scheduled: ${selectedOrder.orderDetails?.scheduledTime\n                                ? formatDate(selectedOrder.orderDetails.scheduledTime)\n                                : 'N/A'}`\n                            : 'N/A'\n                          }\n                        </p>\n                      </div>\n                    </div>\n\n                    {/* Customer Info */}\n                    <div>\n                      <h3 className=\"text-md font-medium text-white mb-3\">Customer Information</h3>\n                      <div className=\"bg-gray-800/50 rounded-lg p-4 text-sm space-y-2\">\n                        <p className=\"text-white\">\n                          {selectedOrder.customer.firstName} {selectedOrder.customer.lastName}\n                        </p>\n                        <p className=\"text-gray-400\">{selectedOrder.customer.email}</p>\n                        <p className=\"text-gray-400\">{selectedOrder.customer.phone}</p>\n                      </div>\n                    </div>\n\n                    {/* Order Items */}\n                    <div>\n                      <h3 className=\"text-md font-medium text-white mb-3\">Order Items</h3>\n                      <div className=\"bg-gray-800/50 rounded-lg overflow-hidden\">\n                        <ul className=\"divide-y divide-gray-700\">\n                          {selectedOrder.items.map(item => (\n                            <li key={item.id} className=\"p-3 flex justify-between\">\n                              <div className=\"flex items-start\">\n                                <span className=\"text-orange-400 font-medium mr-2\">{item.quantity}x</span>\n                                <span className=\"text-white\">{item.name}</span>\n                              </div>\n                              <span className=\"text-gray-300\">{formatCurrency(item.price * item.quantity)}</span>\n                            </li>\n                          ))}\n                        </ul>\n\n                        {/* Order Totals */}\n                        <div className=\"border-t border-gray-700 p-3 space-y-1 text-sm\">\n                          <div className=\"flex justify-between\">\n                            <span className=\"text-gray-400\">Subtotal</span>\n                            <span className=\"text-white\">{formatCurrency(selectedOrder.subtotal)}</span>\n                          </div>\n                          <div className=\"flex justify-between\">\n                            <span className=\"text-gray-400\">Delivery Fee</span>\n                            <span className=\"text-white\">{formatCurrency(selectedOrder.deliveryFee)}</span>\n                          </div>\n                          <div className=\"flex justify-between font-medium pt-1\">\n                            <span className=\"text-gray-300\">Total</span>\n                            <span className=\"text-white\">{formatCurrency(selectedOrder.total)}</span>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n\n                    {/* Order Notes */}\n                    {selectedOrder.notes && (\n                      <div>\n                        <h3 className=\"text-md font-medium text-white mb-3\">Special Instructions</h3>\n                        <div className=\"bg-gray-800/50 rounded-lg p-4 text-sm text-gray-300\">\n                          {selectedOrder.notes}\n                        </div>\n                      </div>\n                    )}\n\n                    {/* Status Management - Simplified with single update button */}\n                    <div>\n                      <h3 className=\"text-md font-medium text-white mb-3\">Update Order Status</h3>\n                      <div className=\"bg-gray-800/50 rounded-lg p-4\">\n                        <SingleUpdateButton\n                          currentStatus={selectedOrder.status}\n                          nextStatus={getNextStatus(selectedOrder)}\n                          nextStatusLabel={getNextStatusLabel(selectedOrder)}\n                          isUpdating={isUpdating}\n                          onUpdate={() => {\n                            const nextStatus = getNextStatus(selectedOrder);\n                            if (nextStatus) {\n                              updateOrderStatus(selectedOrder.id, nextStatus);\n                            }\n                          }}\n                        />\n                      </div>\n                    </div>\n                  </div>\n                </>\n              ) : (\n                <div className=\"flex flex-col items-center justify-center h-full p-6 text-center\">\n                  <Package className=\"w-16 h-16 text-gray-600 mb-4\" />\n                  <p className=\"text-gray-400\">Select an order to see details.</p>\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n      </motion.div>\n    </AdminLayout>\n  );\n};\n\nexport default OrderManager;", "path": "client/src/pages/admin/OrderManager.tsx", "language": "typescriptreact", "prefixBegin": 0, "suffixEnd": 28}}], ["dee542fe-18bf-4dae-bd08-7c4b790cbc66", {"value": {"selectedCode": "", "prefix": "import { pgTable, text, serial, integer, boolean, jsonb, timestamp, foreignKey } from \"drizzle-orm/pg-core\";\n", "suffix": "import { createInsertSchema } from \"drizzle-zod\";\nimport { z } from \"zod\";\n\n// Categories Schema\nexport const categories = pgTable(\"categories\", {\n  id: serial(\"id\").primaryKey(),\n  name: text(\"name\").notNull(),\n  imageUrl: text(\"image_url\").notNull(),\n});\n\nexport const insertCategorySchema = createInsertSchema(categories).omit({ id: true });\nexport type InsertCategory = z.infer<typeof insertCategorySchema>;\nexport type Category = typeof categories.$inferSelect;\n\n// Dish Schema (renamed to menu_items to match the new API structure)\nexport const menuItems = pgTable(\"menu_items\", {\n  id: serial(\"id\").primaryKey(),\n  name: text(\"name\").notNull(),\n  description: text(\"description\").notNull(),\n  price: integer(\"price\").notNull(), // Price in NOK\n  imageUrl: text(\"image_url\").notNull(),\n  categoryId: integer(\"category_id\").notNull().references(() => categories.id),\n  available: boolean(\"available\").default(true),\n  rating: integer(\"rating\").default(0),\n  reviews: integer(\"reviews\").default(0),\n});\n\nexport const insertMenuItemSchema = createInsertSchema(menuItems).omit({ id: true });\nexport type InsertMenuItem = z.infer<typeof insertMenuItemSchema>;\nexport type MenuItem = typeof menuItems.$inferSelect;\n\n// Customization Groups Schema\nexport const customizationGroups = pgTable(\"customization_groups\", {\n  id: serial(\"id\").primaryKey(),\n  title: text(\"title\").notNull(),\n});\n\nexport const insertCustomizationGroupSchema = createInsertSchema(customizationGroups).omit({ id: true });\nexport type InsertCustomizationGroup = z.infer<typeof insertCustomizationGroupSchema>;\nexport type CustomizationGroup = typeof customizationGroups.$inferSelect;\n\n// Customization Options Schema\nexport const customizationOptions = pgTable(\"customization_options\", {\n  id: serial(\"id\").primaryKey(),\n  name: text(\"name\").notNull(),\n  extraPrice: integer(\"extra_price\").default(0),\n  imageUrl: text(\"image_url\").notNull().default(\"\"),\n  groupId: integer(\"group_id\").notNull().references(() => customizationGroups.id),\n});\n\nexport const insertCustomizationOptionSchema = createInsertSchema(customizationOptions).omit({ id: true });\nexport type InsertCustomizationOption = z.infer<typeof insertCustomizationOptionSchema>;\nexport type CustomizationOption = typeof customizationOptions.$inferSelect;\n\n// Item Customization Map (many-to-many relationship)\nexport const itemCustomizationMap = pgTable(\"item_customization_map\", {\n  id: serial(\"id\").primaryKey(),\n  itemId: integer(\"item_id\").notNull().references(() => menuItems.id),\n  optionId: integer(\"option_id\").notNull().references(() => customizationOptions.id),\n});\n\nexport const insertItemCustomizationMapSchema = createInsertSchema(itemCustomizationMap).omit({ id: true });\nexport type InsertItemCustomizationMap = z.infer<typeof insertItemCustomizationMapSchema>;\nexport type ItemCustomizationMap = typeof itemCustomizationMap.$inferSelect;\n\n// For backwards compatibility with existing code\nexport const dishes = menuItems;\nexport const insertDishSchema = insertMenuItemSchema;\nexport type InsertDish = InsertMenuItem;\nexport type Dish = MenuItem;\n\n// Order Schema\nexport const orders = pgTable(\"orders\", {\n  id: serial(\"id\").primaryKey(),\n  customer: jsonb(\"customer\").notNull(),\n  items: jsonb(\"items\").notNull(),\n  orderDetails: jsonb(\"order_details\").notNull(),\n  subtotal: integer(\"subtotal\").notNull(),\n  deliveryFee: integer(\"delivery_fee\").notNull(),\n  total: integer(\"total\").notNull(),\n  status: text(\"status\").notNull().default(\"pending\"),\n  paymentMethod: text(\"payment_method\").notNull(),\n  notes: text(\"notes\"),\n  createdAt: timestamp(\"created_at\").defaultNow(),\n});\n\nexport const insertOrderSchema = createInsertSchema(orders).omit({\n  id: true,\n  createdAt: true\n});\nexport type InsertOrder = z.infer<typeof insertOrderSchema>;\nexport type Order = typeof orders.$inferSelect;\n\n// Contact Message Schema\nexport const contactMessages = pgTable(\"contact_messages\", {\n  id: serial(\"id\").primaryKey(),\n  name: text(\"name\").notNull(),\n  email: text(\"email\").notNull(),\n  subject: text(\"subject\").notNull(),\n  message: text(\"message\").notNull(),\n  createdAt: timestamp(\"created_at\").defaultNow(),\n});\n\nexport const insertContactSchema = createInsertSchema(contactMessages).omit({\n  id: true,\n  createdAt: true\n});\nexport type InsertContactMessage = z.infer<typeof insertContactSchema>;\nexport type ContactMessage = typeof contactMessages.$inferSelect;\n\n// User Schema\nexport const users = pgTable(\"users\", {\n  id: serial(\"id\").primaryKey(),\n  username: text(\"username\").notNull().unique(),\n  email: text(\"email\").notNull().unique(),\n  password: text(\"password\").notNull(),\n  first_name: text(\"first_name\"),\n  last_name: text(\"last_name\"),\n  role: text(\"role\").notNull().default(\"customer\"),\n  is_active: boolean(\"is_active\").notNull().default(true),\n  created_at: timestamp(\"created_at\").defaultNow(),\n  updated_at: timestamp(\"updated_at\").defaultNow(),\n});\n\nexport const insertUserSchema = createInsertSchema(users).omit({\n  id: true,\n  created_at: true,\n  updated_at: true,\n});\n\nexport const loginUserSchema = z.object({\n  username: z.string().min(3).max(50),\n  password: z.string().min(6),\n});\n\nexport type InsertUser = z.infer<typeof insertUserSchema>;\nexport type LoginUser = z.infer<typeof loginUserSchema>;\nexport type User = typeof users.$inferSelect;\n\n// Restaurant Settings Schema\nexport const restaurantSettings = pgTable(\"restaurant_settings\", {\n  id: serial(\"id\").primaryKey(),\n  restaurantOpen: boolean(\"restaurant_open\").default(true),\n  businessHours: jsonb(\"business_hours\").default({}),\n  deliveryFee: integer(\"delivery_fee\").default(49),\n  estimatedTime: text(\"estimated_time\").default(\"25-35 min\"),\n});\n\nexport const insertRestaurantSettingsSchema = createInsertSchema(restaurantSettings).omit({ id: true });\nexport type InsertRestaurantSettings = z.infer<typeof insertRestaurantSettingsSchema>;\nexport type RestaurantSettings = typeof restaurantSettings.$inferSelect;\n", "path": "shared/schema.ts", "language": "typescript", "prefixBegin": 0, "suffixEnd": 0}}], ["37887149-2abb-4579-9c1c-2e2f968dee2a", {"value": {"selectedCode": "", "prefix": "import {\n", "suffix": "  User, InsertUser,\n  Dish, InsertDish,\n  Order, InsertOrder,\n  ContactMessage, InsertContactMessage,\n  Category, InsertCategory,\n  MenuItem, InsertMenuItem,\n  CustomizationGroup, InsertCustomizationGroup,\n  CustomizationOption, InsertCustomizationOption,\n  ItemCustomizationMap, InsertItemCustomizationMap,\n  RestaurantSettings, InsertRestaurantSettings,\n  users, categories, menuItems, orders, contactMessages,\n  customizationGroups, customizationOptions, itemCustomizationMap,\n  restaurantSettings\n} from \"@shared/schema\";\nimport { db } from \"./db\";\nimport { eq, desc, and } from \"drizzle-orm\";\nimport * as crypto from \"crypto\";\n\n// modify the interface with any CRUD methods\n// you might need\nexport interface IStorage {\n  // User operations\n  getUser(id: number): Promise<User | undefined>;\n  getUserByUsername(username: string): Promise<User | undefined>;\n  createUser(user: InsertUser): Promise<User>;\n  updateUser(id: number, user: Partial<InsertUser>): Promise<User | undefined>;\n  deleteUser(id: number): Promise<boolean>;\n  verifyUserCredentials(username: string, password: string): Promise<User | null>;\n\n  // Dish/MenuItem operations\n  getAllDishes(): Promise<Dish[]>;\n  getDishById(id: number): Promise<Dish | undefined>;\n  createDish(dish: InsertDish): Promise<Dish>;\n  updateDish(id: number, dish: Partial<InsertDish>): Promise<Dish | undefined>;\n  deleteDish(id: number): Promise<boolean>;\n\n  // Enhanced Menu Item operations\n  getAllMenuItems(): Promise<(MenuItem & { category_name?: string })[]>;\n  getMenuItemsByCategory(categoryId: number): Promise<MenuItem[]>;\n  getMenuItemById(id: number): Promise<MenuItem | undefined>;\n  createMenuItem(item: InsertMenuItem): Promise<MenuItem>;\n  updateMenuItem(id: number, item: Partial<InsertMenuItem>): Promise<MenuItem | undefined>;\n  deleteMenuItem(id: number): Promise<boolean>;\n\n  // Category operations\n  getCategories(): Promise<string[]>; // Original method for backwards compatibility\n  getAllMenuCategories(): Promise<Category[]>;\n  getMenuCategoryById(id: number): Promise<Category | undefined>;\n  createMenuCategory(category: InsertCategory): Promise<Category>;\n  updateMenuCategory(id: number, category: Partial<InsertCategory>): Promise<Category | undefined>;\n  deleteMenuCategory(id: number): Promise<boolean>;\n\n  // Customization operations\n  getAllCustomizationGroups(): Promise<CustomizationGroup[]>;\n  getCustomizationGroupById(id: number): Promise<CustomizationGroup | undefined>;\n  createCustomizationGroup(group: InsertCustomizationGroup): Promise<CustomizationGroup>;\n  updateCustomizationGroup(id: number, group: Partial<InsertCustomizationGroup>): Promise<CustomizationGroup | undefined>;\n  deleteCustomizationGroup(id: number): Promise<boolean>;\n\n  getAllCustomizationOptions(): Promise<CustomizationOption[]>;\n  getCustomizationOptionsByGroup(groupId: number): Promise<CustomizationOption[]>;\n  getCustomizationOptionById(id: number): Promise<CustomizationOption | undefined>;\n  createCustomizationOption(option: InsertCustomizationOption): Promise<CustomizationOption>;\n  updateCustomizationOption(id: number, option: Partial<InsertCustomizationOption>): Promise<CustomizationOption | undefined>;\n  deleteCustomizationOption(id: number): Promise<boolean>;\n\n  // Item Customization Map operations\n  getCustomizationOptionsForMenuItem(itemId: number): Promise<{\n    group: CustomizationGroup;\n    options: CustomizationOption[];\n  }[]>;\n  mapCustomizationOptionToMenuItem(itemId: number, optionId: number): Promise<ItemCustomizationMap>;\n  unmapCustomizationOptionFromMenuItem(itemId: number, optionId: number): Promise<boolean>;\n\n  // Order operations\n  getOrderById(id: number): Promise<Order | undefined>;\n  getAllOrders(): Promise<Order[]>;\n  createOrder(order: InsertOrder): Promise<Order>;\n  updateOrder(id: number, order: Partial<InsertOrder>): Promise<Order | undefined>;\n  deleteOrder(id: number): Promise<boolean>;\n\n  // Cart operations\n  createCart(cart: { itemId: number, quantity: number, customizations: number[] }): Promise<{ success: boolean, id: number }>;\n\n  // Contact operations\n  createContactMessage(message: InsertContactMessage): Promise<ContactMessage>;\n  getAllContactMessages(): Promise<ContactMessage[]>;\n  getContactMessageById(id: number): Promise<ContactMessage | undefined>;\n  deleteContactMessage(id: number): Promise<boolean>;\n\n  // Restaurant settings operations\n  getRestaurantSettings(): Promise<RestaurantSettings | undefined>;\n  updateRestaurantSettings(settings: Partial<InsertRestaurantSettings>): Promise<RestaurantSettings | undefined>;\n}\n\nexport class MemStorage implements IStorage {\n  private users: Map<number, User>;\n  private dishes: Map<number, Dish>;\n  private orders: Map<number, Order>;\n  private contactMessages: Map<number, ContactMessage>;\n\n  // New storage for enhanced menu system\n  private menuCategories: Map<number, Category>;\n  private menuItems: Map<number, MenuItem>;\n  private customizationGroups: Map<number, CustomizationGroup>;\n  private customizationOptions: Map<number, CustomizationOption>;\n  private itemCustomizationMaps: Map<number, ItemCustomizationMap>;\n\n  // ID counters\n  currentUserId: number;\n  currentDishId: number;\n  currentOrderId: number;\n  currentContactId: number;\n  currentCategoryId: number;\n  currentMenuItemId: number;\n  currentCustomizationGroupId: number;\n  currentCustomizationOptionId: number;\n  currentItemCustomizationMapId: number;\n\n  constructor() {\n    // Initialize maps\n    this.users = new Map();\n    this.dishes = new Map();\n    this.orders = new Map();\n    this.contactMessages = new Map();\n    this.menuCategories = new Map();\n    this.menuItems = new Map();\n    this.customizationGroups = new Map();\n    this.customizationOptions = new Map();\n    this.itemCustomizationMaps = new Map();\n\n    // Initialize counters\n    this.currentUserId = 1;\n    this.currentDishId = 1;\n    this.currentOrderId = 1;\n    this.currentContactId = 1;\n    this.currentCategoryId = 1;\n    this.currentMenuItemId = 1;\n    this.currentCustomizationGroupId = 1;\n    this.currentCustomizationOptionId = 1;\n    this.currentItemCustomizationMapId = 1;\n\n    // Initialize sample data\n    this.initializeSampleData();\n  }\n\n  // User methods\n  async getUser(id: number): Promise<User | undefined> {\n    return this.users.get(id);\n  }\n\n  async getUserByUsername(username: string): Promise<User | undefined> {\n    return Array.from(this.users.values()).find(\n      (user) => user.username === username,\n    );\n  }\n\n  async createUser(insertUser: InsertUser): Promise<User> {\n    const id = this.currentUserId++;\n    const user: User = { ...insertUser, id };\n    this.users.set(id, user);\n    return user;\n  }\n\n  async verifyUserCredentials(username: string, password: string): Promise<User | null> {\n    const user = await this.getUserByUsername(username);\n\n    if (!user) {\n      return null;\n    }\n\n    // In a real implementation, you would use bcrypt.compare\n    // For this demo, we'll just do a direct comparison\n    if (user.password === password) {\n      return user;\n    }\n\n    return null;\n  }\n\n  // Original Dish methods (for backwards compatibility)\n  async getAllDishes(): Promise<Dish[]> {\n    return Array.from(this.dishes.values());\n  }\n\n  async getDishById(id: number): Promise<Dish | undefined> {\n    return this.dishes.get(id);\n  }\n\n  async createDish(insertDish: InsertDish): Promise<Dish> {\n    const id = this.currentDishId++;\n    const dish: Dish = { ...insertDish, id };\n    this.dishes.set(id, dish);\n    return dish;\n  }\n\n  // Enhanced Menu Item methods\n  async getAllMenuItems(): Promise<(MenuItem & { category_name?: string })[]> {\n    return Array.from(this.menuItems.values()).map(item => {\n      const category = this.menuCategories.get(item.categoryId);\n      return {\n        ...item,\n        category_name: category?.name\n      };\n    });\n  }\n\n  async getMenuItemsByCategory(categoryId: number): Promise<MenuItem[]> {\n    return Array.from(this.menuItems.values()).filter(\n      (item) => item.categoryId === categoryId\n    );\n  }\n\n  async getMenuItemById(id: number): Promise<MenuItem | undefined> {\n    return this.menuItems.get(id);\n  }\n\n  async createMenuItem(item: InsertMenuItem): Promise<MenuItem> {\n    const id = this.currentMenuItemId++;\n    const menuItem: MenuItem = { ...item, id };\n    this.menuItems.set(id, menuItem);\n\n    // Also add to dishes for backward compatibility\n    const category = this.menuCategories.get(item.categoryId)?.name || \"Uncategorized\";\n    this.dishes.set(id, {\n      ...menuItem,\n      category\n    } as Dish);\n\n    return menuItem;\n  }\n\n  // Enhanced Category methods\n  async getAllMenuCategories(): Promise<Category[]> {\n    try {\n      // Make sure we actually return the full category objects\n      return Array.from(this.menuCategories.values());\n    } catch (error) {\n      console.error(\"Error getting menu categories:\", error);\n      return [];\n    }\n  }\n\n  async getMenuCategoryById(id: number): Promise<Category | undefined> {\n    return this.menuCategories.get(id);\n  }\n\n  async createMenuCategory(category: InsertCategory): Promise<Category> {\n    const id = this.currentCategoryId++;\n    const newCategory: Category = { ...category, id };\n    this.menuCategories.set(id, newCategory);\n    return newCategory;\n  }\n\n  async updateMenuCategory(id: number, category: Partial<Category>): Promise<Category> {\n    const existingCategory = this.menuCategories.get(id);\n    if (!existingCategory) {\n      throw new Error(`Category with id ${id} not found`);\n    }\n\n    const updatedCategory = { ...existingCategory, ...category };\n    this.menuCategories.set(id, updatedCategory);\n    return updatedCategory;\n  }\n\n  async deleteMenuCategory(id: number): Promise<boolean> {\n    const category = this.menuCategories.get(id);\n    if (!category) {\n      return false;\n    }\n\n    this.menuCategories.delete(id);\n\n    // Update any menu items that were in this category\n    for (const [itemId, item] of this.menuItems.entries()) {\n      if (item.categoryId === id) {\n        const updatedItem = { ...item, categoryId: 0 };\n        this.menuItems.set(itemId, updatedItem);\n      }\n    }\n\n    return true;\n  }\n\n  // Customization Group methods\n  async getAllCustomizationGroups(): Promise<CustomizationGroup[]> {\n    return Array.from(this.customizationGroups.values());\n  }\n\n  async getCustomizationGroupById(id: number): Promise<CustomizationGroup | undefined> {\n    return this.customizationGroups.get(id);\n  }\n\n  async createCustomizationGroup(group: InsertCustomizationGroup): Promise<CustomizationGroup> {\n    const id = this.currentCustomizationGroupId++;\n    const newGroup: CustomizationGroup = { ...group, id };\n    this.customizationGroups.set(id, newGroup);\n    return newGroup;\n  }\n\n  async updateCustomizationGroup(id: number, group: Partial<InsertCustomizationGroup>): Promise<CustomizationGroup | undefined> {\n    const existingGroup = this.customizationGroups.get(id);\n    if (!existingGroup) {\n      return undefined;\n    }\n\n    const updatedGroup = { ...existingGroup, ...group };\n    this.customizationGroups.set(id, updatedGroup);\n    return updatedGroup;\n  }\n\n  async deleteCustomizationGroup(id: number): Promise<boolean> {\n    const group = this.customizationGroups.get(id);\n    if (!group) {\n      return false;\n    }\n\n    // Delete all options in this group\n    const optionsToDelete = Array.from(this.customizationOptions.values())\n      .filter(option => option.groupId === id);\n\n    optionsToDelete.forEach(option => {\n      this.customizationOptions.delete(option.id);\n\n      // Remove any item mappings for these options\n      const mappingsToDelete = Array.from(this.itemCustomizationMaps.entries())\n        .filter(([, mapping]) => mapping.optionId === option.id);\n\n      mappingsToDelete.forEach(([mappingId]) => {\n        this.itemCustomizationMaps.delete(mappingId);\n      });\n    });\n\n    this.customizationGroups.delete(id);\n    return true;\n  }\n\n  // Customization Option methods\n  async getAllCustomizationOptions(): Promise<CustomizationOption[]> {\n    return Array.from(this.customizationOptions.values());\n  }\n\n  async getCustomizationOptionsByGroup(groupId: number): Promise<CustomizationOption[]> {\n    return Array.from(this.customizationOptions.values()).filter(\n      (option) => option.groupId === groupId\n    );\n  }\n\n  async getCustomizationOptionById(id: number): Promise<CustomizationOption | undefined> {\n    return this.customizationOptions.get(id);\n  }\n\n  async createCustomizationOption(option: InsertCustomizationOption): Promise<CustomizationOption> {\n    const id = this.currentCustomizationOptionId++;\n    const newOption: CustomizationOption = {\n      ...option,\n      id,\n      imageUrl: option.imageUrl || \"\",\n      extraPrice: option.extraPrice || 0\n    };\n    this.customizationOptions.set(id, newOption);\n    return newOption;\n  }\n\n  async updateCustomizationOption(id: number, option: Partial<InsertCustomizationOption>): Promise<CustomizationOption | undefined> {\n    const existingOption = this.customizationOptions.get(id);\n    if (!existingOption) {\n      return undefined;\n    }\n\n    const updatedOption = { ...existingOption, ...option };\n    this.customizationOptions.set(id, updatedOption);\n    return updatedOption;\n  }\n\n  async deleteCustomizationOption(id: number): Promise<boolean> {\n    const option = this.customizationOptions.get(id);\n    if (!option) {\n      return false;\n    }\n\n    // Remove any item mappings for this option\n    const mappingsToDelete = Array.from(this.itemCustomizationMaps.entries())\n      .filter(([, mapping]) => mapping.optionId === id);\n\n    mappingsToDelete.forEach(([mappingId]) => {\n      this.itemCustomizationMaps.delete(mappingId);\n    });\n\n    this.customizationOptions.delete(id);\n    return true;\n  }\n\n  // Item Customization Map methods\n  async getCustomizationOptionsForMenuItem(itemId: number): Promise<{\n    group: CustomizationGroup;\n    options: CustomizationOption[];\n  }[]> {\n    // Get all mapping entries for this item\n    const mappings = Array.from(this.itemCustomizationMaps.values()).filter(\n      (map) => map.itemId === itemId\n    );\n\n    // Get all option IDs for this item\n    const optionIds = mappings.map((map) => map.optionId);\n\n    // Get all options for these IDs\n    const options = optionIds.map((id) => this.customizationOptions.get(id)!).filter(Boolean);\n\n    // Group options by group ID\n    const groupedOptions = new Map<number, CustomizationOption[]>();\n    options.forEach((option) => {\n      if (!groupedOptions.has(option.groupId)) {\n        groupedOptions.set(option.groupId, []);\n      }\n      groupedOptions.get(option.groupId)!.push(option);\n    });\n\n    // Format the result with group objects\n    const result: { group: CustomizationGroup; options: CustomizationOption[] }[] = [];\n    groupedOptions.forEach((options, groupId) => {\n      const group = this.customizationGroups.get(groupId);\n      if (group) {\n        result.push({ group, options });\n      }\n    });\n\n    return result;\n  }\n\n  async mapCustomizationOptionToMenuItem(itemId: number, optionId: number): Promise<ItemCustomizationMap> {\n    // Check if the menu item and option exist\n    const menuItem = await this.getMenuItemById(itemId);\n    const option = await this.getCustomizationOptionById(optionId);\n\n    if (!menuItem || !option) {\n      throw new Error(\"Menu item or customization option not found\");\n    }\n\n    // Check if mapping already exists\n    const existingMapping = Array.from(this.itemCustomizationMaps.values())\n      .find(mapping => mapping.itemId === itemId && mapping.optionId === optionId);\n\n    if (existingMapping) {\n      return existingMapping;\n    }\n\n    // Create the mapping\n    const id = this.currentItemCustomizationMapId++;\n    const mapping: ItemCustomizationMap = { id, itemId, optionId };\n    this.itemCustomizationMaps.set(id, mapping);\n\n    return mapping;\n  }\n\n  async unmapCustomizationOptionFromMenuItem(itemId: number, optionId: number): Promise<boolean> {\n    const mappingToDelete = Array.from(this.itemCustomizationMaps.entries())\n      .find(([, mapping]) => mapping.itemId === itemId && mapping.optionId === optionId);\n\n    if (!mappingToDelete) {\n      return false;\n    }\n\n    this.itemCustomizationMaps.delete(mappingToDelete[0]);\n    return true;\n  }\n\n  // Cart operations\n  async createCart(cart: { itemId: number, quantity: number, customizations: number[] }): Promise<{ success: boolean, id: number }> {\n    // Validate that the item exists\n    const menuItem = await this.getMenuItemById(cart.itemId);\n    if (!menuItem) {\n      throw new Error(\"Menu item not found\");\n    }\n\n    // Validate that all customization options exist\n    for (const optionId of cart.customizations) {\n      const option = await this.getCustomizationOptionById(optionId);\n      if (!option) {\n        throw new Error(`Customization option ${optionId} not found`);\n      }\n    }\n\n    // Since we don't have a cart table yet, we'll just return success\n    return { success: true, id: Date.now() };\n  }\n\n  // Order methods\n  async getOrderById(id: number): Promise<Order | undefined> {\n    return this.orders.get(id);\n  }\n\n  async getAllOrders(): Promise<Order[]> {\n    return Array.from(this.orders.values());\n  }\n\n  async createOrder(insertOrder: InsertOrder): Promise<Order> {\n    const id = this.currentOrderId++;\n    const now = new Date();\n    const order: Order = { ...insertOrder, id, createdAt: now };\n    this.orders.set(id, order);\n    return order;\n  }\n\n  // Contact methods\n  async createContactMessage(insertMessage: InsertContactMessage): Promise<ContactMessage> {\n    const id = this.currentContactId++;\n    const now = new Date();\n    const message: ContactMessage = { ...insertMessage, id, createdAt: now };\n    this.contactMessages.set(id, message);\n    return message;\n  }\n\n  // Category methods (for backwards compatibility)\n  async getCategories(): Promise<string[]> {\n    // Get all category names from the categories table\n    return Array.from(this.menuCategories.values()).map(category => category.name);\n  }\n\n  // Initialize sample data for the menu system\n  private initializeSampleData() {\n    // 1. Initialize categories\n    const sampleCategories: InsertCategory[] = [\n      {\n        name: \"Signature BBQ\",\n        imageUrl: \"https://images.unsplash.com/photo-1529193591184-b1d58069ecdd?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n      },\n      {\n        name: \"Starters\",\n        imageUrl: \"https://images.unsplash.com/photo-1527477396000-e27163b481c2?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n      },\n      {\n        name: \"Main Course\",\n        imageUrl: \"https://images.unsplash.com/photo-1519708227418-c8fd9a32b7a2?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n      },\n      {\n        name: \"Desserts\",\n        imageUrl: \"https://images.unsplash.com/photo-1624353365286-3f8d62daad51?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n      },\n      {\n        name: \"BurgerZ\",\n        imageUrl: \"https://images.unsplash.com/photo-1568901346375-23c9450c58cd?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n      },\n      {\n        name: \"SandwichZ\",\n        imageUrl: \"https://images.unsplash.com/photo-1621996346565-e3dbc646d9a9?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n      }\n    ];\n\n    // Insert categories and create a map for reference when creating menu items\n    const categoryMap: Record<string, number> = {};\n    sampleCategories.forEach(category => {\n      const id = this.currentCategoryId++;\n      this.menuCategories.set(id, { ...category, id });\n      categoryMap[category.name] = id;\n    });\n\n    // 2. Initialize menu items\n    const sampleMenuItems: Array<InsertMenuItem & { categoryName: string }> = [\n      {\n        name: \"Smoked Beef Brisket\",\n        description: \"24-hour smoked premium beef brisket with our signature spice rub, served with homemade BBQ sauce and pickled vegetables.\",\n        price: 329,\n        imageUrl: \"https://images.unsplash.com/photo-1529193591184-b1d58069ecdd?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n        categoryId: 0, // Will be replaced with actual ID\n        categoryName: \"Signature BBQ\",\n        available: true,\n        rating: 49,\n        reviews: 120\n      },\n      {\n        name: \"BBQ Pork Ribs\",\n        description: \"Slow-cooked St. Louis style ribs with our house dry rub, glazed with maple bourbon sauce and finished over open flame.\",\n        price: 289,\n        imageUrl: \"https://images.unsplash.com/photo-1544025162-d76694265947?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n        categoryId: 0,\n        categoryName: \"Signature BBQ\",\n        available: true,\n        rating: 48,\n        reviews: 98\n      },\n      {\n        name: \"Pulled Pork Sandwich\",\n        description: \"12-hour smoked pork shoulder, hand-pulled and tossed in Carolina vinegar sauce, served on a brioche bun with coleslaw.\",\n        price: 219,\n        imageUrl: \"https://images.unsplash.com/photo-1621996346565-e3dbc646d9a9?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n        categoryId: 0,\n        categoryName: \"SandwichZ\",\n        available: true,\n        rating: 47,\n        reviews: 86\n      },\n      {\n        name: \"Smoked Chicken Wings\",\n        description: \"Applewood smoked wings finished on the grill, tossed in your choice of sauce: Classic Buffalo, Honey Chipotle, or Garlic Parmesan.\",\n        price: 189,\n        imageUrl: \"https://images.unsplash.com/photo-1527477396000-e27163b481c2?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n        categoryId: 0,\n        categoryName: \"Starters\",\n        available: true,\n        rating: 46,\n        reviews: 112\n      },\n      {\n        name: \"Cedar Plank Salmon\",\n        description: \"Norwegian salmon fillet grilled on a cedar plank with maple glaze, served with grilled lemon and seasonal vegetables.\",\n        price: 299,\n        imageUrl: \"https://images.unsplash.com/photo-1519708227418-c8fd9a32b7a2?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n        categoryId: 0,\n        categoryName: \"Main Course\",\n        available: true,\n        rating: 49,\n        reviews: 74\n      },\n      {\n        name: \"Smokehouse Burger\",\n        description: \"House-ground prime beef patty with smoked cheddar, bacon jam, caramelized onions, and bourbon BBQ sauce on a toasted brioche bun.\",\n        price: 249,\n        imageUrl: \"https://images.unsplash.com/photo-1568901346375-23c9450c58cd?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n        categoryId: 0,\n        categoryName: \"BurgerZ\",\n        available: true,\n        rating: 48,\n        reviews: 103\n      },\n      {\n        name: \"BBQ Sampler Platter\",\n        description: \"A selection of our signature meats including brisket, ribs, pulled pork, and smoked sausage, served with two sides of your choice.\",\n        price: 399,\n        imageUrl: \"https://images.unsplash.com/photo-1508615263227-c5d58c1e5821?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n        categoryId: 0,\n        categoryName: \"Signature BBQ\",\n        available: true,\n        rating: 50,\n        reviews: 135\n      },\n      {\n        name: \"Grilled Vegetable Skewers\",\n        description: \"Seasonal vegetables marinated in herbs and olive oil, grilled to perfection and served with chimichurri sauce.\",\n        price: 179,\n        imageUrl: \"https://images.unsplash.com/photo-1625944525533-473f1a3d54a7?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n        categoryId: 0,\n        categoryName: \"Starters\",\n        available: true,\n        rating: 45,\n        reviews: 62\n      },\n      {\n        name: \"Chocolate Lava Cake\",\n        description: \"Warm chocolate cake with a molten center, served with vanilla bean ice cream and fresh berries.\",\n        price: 149,\n        imageUrl: \"https://images.unsplash.com/photo-1624353365286-3f8d62daad51?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n        categoryId: 0,\n        categoryName: \"Desserts\",\n        available: true,\n        rating: 48,\n        reviews: 89\n      }\n    ];\n\n    // Insert menu items with the correct category IDs\n    sampleMenuItems.forEach(menuItem => {\n      const { categoryName, ...item } = menuItem;\n      const categoryId = categoryMap[categoryName];\n      const id = this.currentMenuItemId++;\n      const dishToInsert = { ...item, categoryId, id };\n\n      // Add to both menu items and dishes (for backward compatibility)\n      this.menuItems.set(id, dishToInsert);\n      this.dishes.set(id, {\n        ...dishToInsert,\n        category: categoryName // For backward compatibility\n      } as Dish);\n    });\n\n    // 3. Initialize customization groups\n    const customizationGroups: InsertCustomizationGroup[] = [\n      { title: \"Saus & Topping\" },\n      { title: \"Ost\" },\n      { title: \"Ekstra Produkter\" },\n      { title: \"Grønnsaker\" }\n    ];\n\n    // Add customization groups and track IDs\n    const groupMap: Record<string, number> = {};\n    customizationGroups.forEach(group => {\n      const id = this.currentCustomizationGroupId++;\n      this.customizationGroups.set(id, { ...group, id });\n      groupMap[group.title] = id;\n    });\n\n    // 4. Initialize customization options\n    const customizationOptions: Array<InsertCustomizationOption & { groupTitle: string }> = [\n      { name: \"BBQ saus\", extraPrice: 10, imageUrl: \"/icons/bbq.png\", groupId: 0, groupTitle: \"Saus & Topping\" },\n      { name: \"Hot Sauce\", extraPrice: 10, imageUrl: \"/icons/hot.png\", groupId: 0, groupTitle: \"Saus & Topping\" },\n      { name: \"Mayo\", extraPrice: 10, imageUrl: \"/icons/mayo.png\", groupId: 0, groupTitle: \"Saus & Topping\" },\n      { name: \"Cheddar\", extraPrice: 15, imageUrl: \"/icons/cheddar.png\", groupId: 0, groupTitle: \"Ost\" },\n      { name: \"Blue Cheese\", extraPrice: 20, imageUrl: \"/icons/blue.png\", groupId: 0, groupTitle: \"Ost\" },\n      { name: \"Bacon\", extraPrice: 25, imageUrl: \"/icons/bacon.png\", groupId: 0, groupTitle: \"Ekstra Produkter\" },\n      { name: \"Double Meat\", extraPrice: 40, imageUrl: \"/icons/meat.png\", groupId: 0, groupTitle: \"Ekstra Produkter\" },\n      { name: \"Lettuce\", extraPrice: 0, imageUrl: \"/icons/lettuce.png\", groupId: 0, groupTitle: \"Grønnsaker\" },\n      { name: \"Tomato\", extraPrice: 0, imageUrl: \"/icons/tomato.png\", groupId: 0, groupTitle: \"Grønnsaker\" },\n      { name: \"Onion\", extraPrice: 0, imageUrl: \"/icons/onion.png\", groupId: 0, groupTitle: \"Grønnsaker\" },\n      { name: \"Avocado\", extraPrice: 20, imageUrl: \"/icons/avocado.png\", groupId: 0, groupTitle: \"Grønnsaker\" }\n    ];\n\n    // Add customization options with correct group IDs\n    const optionMap: Record<string, number> = {};\n    customizationOptions.forEach(option => {\n      const { groupTitle, ...optionData } = option;\n      const groupId = groupMap[groupTitle];\n      const id = this.currentCustomizationOptionId++;\n      this.customizationOptions.set(id, { ...optionData, groupId, id });\n      optionMap[option.name] = id;\n    });\n\n    // 5. Map customization options to menu items\n    // For each burger, add some customization options\n    const burgerItems = Array.from(this.menuItems.values())\n      .filter(item => {\n        const category = Array.from(this.menuCategories.values())\n          .find(cat => cat.id === item.categoryId);\n        return category && (category.name === \"BurgerZ\" || category.name === \"SandwichZ\");\n      });\n\n    burgerItems.forEach(burger => {\n      // Add all sauce options\n      const sauceOptions = Array.from(this.customizationOptions.values())\n        .filter(option => {\n          const group = this.customizationGroups.get(option.groupId);\n          return group && group.title === \"Saus & Topping\";\n        });\n\n      sauceOptions.forEach(sauce => {\n        const id = this.currentItemCustomizationMapId++;\n        this.itemCustomizationMaps.set(id, {\n          id,\n          itemId: burger.id,\n          optionId: sauce.id\n        });\n      });\n\n      // Add all cheese options\n      const cheeseOptions = Array.from(this.customizationOptions.values())\n        .filter(option => {\n          const group = this.customizationGroups.get(option.groupId);\n          return group && group.title === \"Ost\";\n        });\n\n      cheeseOptions.forEach(cheese => {\n        const id = this.currentItemCustomizationMapId++;\n        this.itemCustomizationMaps.set(id, {\n          id,\n          itemId: burger.id,\n          optionId: cheese.id\n        });\n      });\n\n      // Add all vegetable options\n      const vegOptions = Array.from(this.customizationOptions.values())\n        .filter(option => {\n          const group = this.customizationGroups.get(option.groupId);\n          return group && group.title === \"Grønnsaker\";\n        });\n\n      vegOptions.forEach(veg => {\n        const id = this.currentItemCustomizationMapId++;\n        this.itemCustomizationMaps.set(id, {\n          id,\n          itemId: burger.id,\n          optionId: veg.id\n        });\n      });\n\n      // Add extra options\n      const extraOptions = Array.from(this.customizationOptions.values())\n        .filter(option => {\n          const group = this.customizationGroups.get(option.groupId);\n          return group && group.title === \"Ekstra Produkter\";\n        });\n\n      extraOptions.forEach(extra => {\n        const id = this.currentItemCustomizationMapId++;\n        this.itemCustomizationMaps.set(id, {\n          id,\n          itemId: burger.id,\n          optionId: extra.id\n        });\n      });\n    });\n  }\n}\n\n// Password hashing utility functions\nasync function hashPassword(password: string): Promise<string> {\n  const salt = crypto.randomBytes(16).toString(\"hex\");\n  return new Promise((resolve, reject) => {\n    crypto.scrypt(password, salt, 64, (err, derivedKey) => {\n      if (err) reject(err);\n      resolve(`${derivedKey.toString(\"hex\")}.${salt}`);\n    });\n  });\n}\n\nasync function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {\n  const [hash, salt] = hashedPassword.split(\".\");\n  return new Promise((resolve, reject) => {\n    crypto.scrypt(password, salt, 64, (err, derivedKey) => {\n      if (err) reject(err);\n      resolve(hash === derivedKey.toString(\"hex\"));\n    });\n  });\n}\n\nexport class DatabaseStorage implements IStorage {\n  constructor() {\n    // Initialize database with sample data if needed\n    this.initializeDatabase();\n  }\n\n  private async initializeDatabase() {\n    try {\n      // Check if we have any categories, if not, initialize with sample data\n      const existingCategories = await db.select().from(categories).limit(1);\n\n      if (existingCategories.length === 0) {\n        console.log(\"Initializing database with sample data...\");\n        await this.seedDatabase();\n      }\n    } catch (error) {\n      console.error(\"Error initializing database:\", error);\n    }\n  }\n\n  private async seedDatabase() {\n    try {\n      // Insert sample categories\n      const sampleCategories = [\n        {\n          name: \"Signature BBQ\",\n          imageUrl: \"https://images.unsplash.com/photo-1529193591184-b1d58069ecdd?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n        },\n        {\n          name: \"Starters\",\n          imageUrl: \"https://images.unsplash.com/photo-1527477396000-e27163b481c2?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n        },\n        {\n          name: \"Main Course\",\n          imageUrl: \"https://images.unsplash.com/photo-1519708227418-c8fd9a32b7a2?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n        },\n        {\n          name: \"Desserts\",\n          imageUrl: \"https://images.unsplash.com/photo-1624353365286-3f8d62daad51?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n        },\n        {\n          name: \"BurgerZ\",\n          imageUrl: \"https://images.unsplash.com/photo-1568901346375-23c9450c58cd?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n        },\n        {\n          name: \"SandwichZ\",\n          imageUrl: \"https://images.unsplash.com/photo-1621996346565-e3dbc646d9a9?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n        }\n      ];\n\n      const insertedCategories = await db.insert(categories).values(sampleCategories).returning();\n      console.log(`Inserted ${insertedCategories.length} categories`);\n\n      // Insert sample menu items\n      const sampleMenuItems = [\n        {\n          name: \"Smoked Beef Brisket\",\n          description: \"24-hour smoked premium beef brisket with our signature spice rub, served with homemade BBQ sauce and pickled vegetables.\",\n          price: 329,\n          imageUrl: \"https://images.unsplash.com/photo-1529193591184-b1d58069ecdd?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n          categoryId: insertedCategories[0].id, // Signature BBQ\n          available: true,\n          rating: 49,\n          reviews: 120\n        },\n        {\n          name: \"BBQ Pulled Pork\",\n          description: \"Slow-cooked pulled pork shoulder with tangy BBQ sauce, served on a brioche bun with coleslaw.\",\n          price: 269,\n          imageUrl: \"https://images.unsplash.com/photo-1544025162-d76694265947?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n          categoryId: insertedCategories[0].id, // Signature BBQ\n          available: true,\n          rating: 46,\n          reviews: 89\n        },\n        {\n          name: \"Loaded Nachos\",\n          description: \"Crispy tortilla chips topped with melted cheese, jalapeños, sour cream, and guacamole.\",\n          price: 189,\n          imageUrl: \"https://images.unsplash.com/photo-1513456852971-30c0b8199d4d?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n          categoryId: insertedCategories[1].id, // Starters\n          available: true,\n          rating: 42,\n          reviews: 67\n        },\n        {\n          name: \"BBQ Wings\",\n          description: \"Crispy chicken wings tossed in our signature BBQ sauce, served with ranch dipping sauce.\",\n          price: 219,\n          imageUrl: \"https://images.unsplash.com/photo-1527477396000-e27163b481c2?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=400\",\n          categoryId: insertedCategories[1].id, // Starters\n          available: true,\n          rating: 44,\n          reviews: 78\n        }\n      ];\n\n      const insertedMenuItems = await db.insert(menuItems).values(sampleMenuItems).returning();\n      console.log(`Inserted ${insertedMenuItems.length} menu items`);\n\n      // Insert customization groups\n      const sampleGroups = [\n        { title: \"Saus & Topping\" },\n        { title: \"Ost\" },\n        { title: \"Ekstra Produkter\" },\n        { title: \"Grønnsaker\" }\n      ];\n\n      const insertedGroups = await db.insert(customizationGroups).values(sampleGroups).returning();\n      console.log(`Inserted ${insertedGroups.length} customization groups`);\n\n      // Insert customization options\n      const sampleOptions = [\n        { name: \"BBQ Sauce\", extraPrice: 10, imageUrl: \"\", groupId: insertedGroups[0].id },\n        { name: \"Hot Sauce\", extraPrice: 10, imageUrl: \"\", groupId: insertedGroups[0].id },\n        { name: \"Mayo\", extraPrice: 10, imageUrl: \"\", groupId: insertedGroups[0].id },\n        { name: \"Honey Mustard\", extraPrice: 15, imageUrl: \"\", groupId: insertedGroups[0].id },\n\n        { name: \"Cheddar\", extraPrice: 15, imageUrl: \"\", groupId: insertedGroups[1].id },\n        { name: \"Blue Cheese\", extraPrice: 20, imageUrl: \"\", groupId: insertedGroups[1].id },\n        { name: \"Mozzarella\", extraPrice: 15, imageUrl: \"\", groupId: insertedGroups[1].id },\n\n        { name: \"Bacon\", extraPrice: 25, imageUrl: \"\", groupId: insertedGroups[2].id },\n        { name: \"Double Meat\", extraPrice: 40, imageUrl: \"\", groupId: insertedGroups[2].id },\n        { name: \"Fried Egg\", extraPrice: 15, imageUrl: \"\", groupId: insertedGroups[2].id },\n\n        { name: \"Lettuce\", extraPrice: 0, imageUrl: \"\", groupId: insertedGroups[3].id },\n        { name: \"Tomato\", extraPrice: 0, imageUrl: \"\", groupId: insertedGroups[3].id },\n        { name: \"Onion\", extraPrice: 0, imageUrl: \"\", groupId: insertedGroups[3].id },\n        { name: \"Cucumber\", extraPrice: 0, imageUrl: \"\", groupId: insertedGroups[3].id },\n        { name: \"Avocado\", extraPrice: 20, imageUrl: \"\", groupId: insertedGroups[3].id }\n      ];\n\n      const insertedOptions = await db.insert(customizationOptions).values(sampleOptions).returning();\n      console.log(`Inserted ${insertedOptions.length} customization options`);\n\n      console.log(\"Database seeded successfully!\");\n    } catch (error) {\n      console.error(\"Error seeding database:\", error);\n    }\n  }\n\n  // User operations\n  async getUser(id: number): Promise<User | undefined> {\n    try {\n      const result = await db.select().from(users).where(eq(users.id, id)).limit(1);\n      return result[0];\n    } catch (error) {\n      console.error(\"Error getting user:\", error);\n      return undefined;\n    }\n  }\n\n  async getUserByUsername(username: string): Promise<User | undefined> {\n    try {\n      const result = await db.select().from(users).where(eq(users.username, username)).limit(1);\n      return result[0];\n    } catch (error) {\n      console.error(\"Error getting user by username:\", error);\n      return undefined;\n    }\n  }\n\n  async createUser(insertUser: InsertUser): Promise<User> {\n    try {\n      // Hash the password before storing\n      const hashedPassword = await hashPassword(insertUser.password);\n      const userToInsert = { ...insertUser, password: hashedPassword };\n\n      const result = await db.insert(users).values(userToInsert).returning();\n      return result[0];\n    } catch (error) {\n      console.error(\"Error creating user:\", error);\n      throw error;\n    }\n  }\n\n  async updateUser(id: number, user: Partial<InsertUser>): Promise<User | undefined> {\n    try {\n      // Hash password if it's being updated\n      const updateData = { ...user };\n      if (updateData.password) {\n        updateData.password = await hashPassword(updateData.password);\n      }\n\n      const result = await db.update(users).set(updateData).where(eq(users.id, id)).returning();\n      return result[0];\n    } catch (error) {\n      console.error(\"Error updating user:\", error);\n      return undefined;\n    }\n  }\n\n  async deleteUser(id: number): Promise<boolean> {\n    try {\n      const result = await db.delete(users).where(eq(users.id, id));\n      return result.rowCount > 0;\n    } catch (error) {\n      console.error(\"Error deleting user:\", error);\n      return false;\n    }\n  }\n\n  async verifyUserCredentials(username: string, password: string): Promise<User | null> {\n    try {\n      const user = await this.getUserByUsername(username);\n      if (!user) {\n        return null;\n      }\n\n      const isValid = await verifyPassword(password, user.password);\n      return isValid ? user : null;\n    } catch (error) {\n      console.error(\"Error verifying user credentials:\", error);\n      return null;\n    }\n  }\n\n  // Dish/MenuItem operations (for backward compatibility)\n  async getAllDishes(): Promise<Dish[]> {\n    try {\n      const result = await db.select().from(menuItems);\n      return result;\n    } catch (error) {\n      console.error(\"Error getting all dishes:\", error);\n      return [];\n    }\n  }\n\n  async getDishById(id: number): Promise<Dish | undefined> {\n    try {\n      const result = await db.select().from(menuItems).where(eq(menuItems.id, id)).limit(1);\n      return result[0];\n    } catch (error) {\n      console.error(\"Error getting dish by id:\", error);\n      return undefined;\n    }\n  }\n\n  async createDish(insertDish: InsertDish): Promise<Dish> {\n    try {\n      const result = await db.insert(menuItems).values(insertDish).returning();\n      return result[0];\n    } catch (error) {\n      console.error(\"Error creating dish:\", error);\n      throw error;\n    }\n  }\n\n  async updateDish(id: number, dish: Partial<InsertDish>): Promise<Dish | undefined> {\n    try {\n      const result = await db.update(menuItems).set(dish).where(eq(menuItems.id, id)).returning();\n      return result[0];\n    } catch (error) {\n      console.error(\"Error updating dish:\", error);\n      return undefined;\n    }\n  }\n\n  async deleteDish(id: number): Promise<boolean> {\n    try {\n      const result = await db.delete(menuItems).where(eq(menuItems.id, id));\n      return (result.rowCount || 0) > 0;\n    } catch (error) {\n      console.error(\"Error deleting dish:\", error);\n      return false;\n    }\n  }\n\n  // Enhanced Menu Item operations\n  async getAllMenuItems(): Promise<(MenuItem & { category_name?: string })[]> {\n    try {\n      const result = await db\n        .select({\n          id: menuItems.id,\n          name: menuItems.name,\n          description: menuItems.description,\n          price: menuItems.price,\n          imageUrl: menuItems.imageUrl,\n          categoryId: menuItems.categoryId,\n          available: menuItems.available,\n          rating: menuItems.rating,\n          reviews: menuItems.reviews,\n          category_name: categories.name,\n        })\n        .from(menuItems)\n        .leftJoin(categories, eq(menuItems.categoryId, categories.id));\n\n      // Map the result to handle null category_name\n      return result.map(item => ({\n        ...item,\n        category_name: item.category_name || undefined\n      }));\n    } catch (error) {\n      console.error(\"Error getting all menu items:\", error);\n      return [];\n    }\n  }\n\n  async getMenuItemsByCategory(categoryId: number): Promise<MenuItem[]> {\n    try {\n      const result = await db.select().from(menuItems).where(eq(menuItems.categoryId, categoryId));\n      return result;\n    } catch (error) {\n      console.error(\"Error getting menu items by category:\", error);\n      return [];\n    }\n  }\n\n  async getMenuItemById(id: number): Promise<MenuItem | undefined> {\n    try {\n      const result = await db.select().from(menuItems).where(eq(menuItems.id, id)).limit(1);\n      return result[0];\n    } catch (error) {\n      console.error(\"Error getting menu item by id:\", error);\n      return undefined;\n    }\n  }\n\n  async createMenuItem(item: InsertMenuItem): Promise<MenuItem> {\n    try {\n      const result = await db.insert(menuItems).values(item).returning();\n      return result[0];\n    } catch (error) {\n      console.error(\"Error creating menu item:\", error);\n      throw error;\n    }\n  }\n\n  async updateMenuItem(id: number, item: Partial<InsertMenuItem>): Promise<MenuItem | undefined> {\n    try {\n      const result = await db.update(menuItems).set(item).where(eq(menuItems.id, id)).returning();\n      return result[0];\n    } catch (error) {\n      console.error(\"Error updating menu item:\", error);\n      return undefined;\n    }\n  }\n\n  async deleteMenuItem(id: number): Promise<boolean> {\n    try {\n      const result = await db.delete(menuItems).where(eq(menuItems.id, id));\n      return (result.rowCount || 0) > 0;\n    } catch (error) {\n      console.error(\"Error deleting menu item:\", error);\n      return false;\n    }\n  }\n\n  // Category operations\n  async getCategories(): Promise<string[]> {\n    try {\n      const result = await db.select({ name: categories.name }).from(categories);\n      return result.map(cat => cat.name);\n    } catch (error) {\n      console.error(\"Error getting categories:\", error);\n      return [];\n    }\n  }\n\n  async getAllMenuCategories(): Promise<Category[]> {\n    try {\n      const result = await db.select().from(categories);\n      return result;\n    } catch (error) {\n      console.error(\"Error getting all menu categories:\", error);\n      return [];\n    }\n  }\n\n  async getMenuCategoryById(id: number): Promise<Category | undefined> {\n    try {\n      const result = await db.select().from(categories).where(eq(categories.id, id)).limit(1);\n      return result[0];\n    } catch (error) {\n      console.error(\"Error getting menu category by id:\", error);\n      return undefined;\n    }\n  }\n\n  async createMenuCategory(category: InsertCategory): Promise<Category> {\n    try {\n      const result = await db.insert(categories).values(category).returning();\n      return result[0];\n    } catch (error) {\n      console.error(\"Error creating menu category:\", error);\n      throw error;\n    }\n  }\n\n  async updateMenuCategory(id: number, category: Partial<InsertCategory>): Promise<Category | undefined> {\n    try {\n      const result = await db.update(categories).set(category).where(eq(categories.id, id)).returning();\n      return result[0];\n    } catch (error) {\n      console.error(\"Error updating menu category:\", error);\n      return undefined;\n    }\n  }\n\n  async deleteMenuCategory(id: number): Promise<boolean> {\n    try {\n      const result = await db.delete(categories).where(eq(categories.id, id));\n      return (result.rowCount || 0) > 0;\n    } catch (error) {\n      console.error(\"Error deleting menu category:\", error);\n      return false;\n    }\n  }\n\n  // Customization Group operations\n  async getAllCustomizationGroups(): Promise<CustomizationGroup[]> {\n    try {\n      const result = await db.select().from(customizationGroups);\n      return result;\n    } catch (error) {\n      console.error(\"Error getting all customization groups:\", error);\n      return [];\n    }\n  }\n\n  async getCustomizationGroupById(id: number): Promise<CustomizationGroup | undefined> {\n    try {\n      const result = await db.select().from(customizationGroups).where(eq(customizationGroups.id, id)).limit(1);\n      return result[0];\n    } catch (error) {\n      console.error(\"Error getting customization group by id:\", error);\n      return undefined;\n    }\n  }\n\n  async createCustomizationGroup(group: InsertCustomizationGroup): Promise<CustomizationGroup> {\n    try {\n      const result = await db.insert(customizationGroups).values(group).returning();\n      return result[0];\n    } catch (error) {\n      console.error(\"Error creating customization group:\", error);\n      throw error;\n    }\n  }\n\n  async updateCustomizationGroup(id: number, group: Partial<InsertCustomizationGroup>): Promise<CustomizationGroup | undefined> {\n    try {\n      const result = await db.update(customizationGroups).set(group).where(eq(customizationGroups.id, id)).returning();\n      return result[0];\n    } catch (error) {\n      console.error(\"Error updating customization group:\", error);\n      return undefined;\n    }\n  }\n\n  async deleteCustomizationGroup(id: number): Promise<boolean> {\n    try {\n      const result = await db.delete(customizationGroups).where(eq(customizationGroups.id, id));\n      return (result.rowCount || 0) > 0;\n    } catch (error) {\n      console.error(\"Error deleting customization group:\", error);\n      return false;\n    }\n  }\n\n  // Customization Option operations\n  async getAllCustomizationOptions(): Promise<CustomizationOption[]> {\n    try {\n      const result = await db.select().from(customizationOptions);\n      return result;\n    } catch (error) {\n      console.error(\"Error getting all customization options:\", error);\n      return [];\n    }\n  }\n\n  async getCustomizationOptionsByGroup(groupId: number): Promise<CustomizationOption[]> {\n    try {\n      const result = await db.select().from(customizationOptions).where(eq(customizationOptions.groupId, groupId));\n      return result;\n    } catch (error) {\n      console.error(\"Error getting customization options by group:\", error);\n      return [];\n    }\n  }\n\n  async getCustomizationOptionById(id: number): Promise<CustomizationOption | undefined> {\n    try {\n      const result = await db.select().from(customizationOptions).where(eq(customizationOptions.id, id)).limit(1);\n      return result[0];\n    } catch (error) {\n      console.error(\"Error getting customization option by id:\", error);\n      return undefined;\n    }\n  }\n\n  async createCustomizationOption(option: InsertCustomizationOption): Promise<CustomizationOption> {\n    try {\n      const result = await db.insert(customizationOptions).values(option).returning();\n      return result[0];\n    } catch (error) {\n      console.error(\"Error creating customization option:\", error);\n      throw error;\n    }\n  }\n\n  async updateCustomizationOption(id: number, option: Partial<InsertCustomizationOption>): Promise<CustomizationOption | undefined> {\n    try {\n      const result = await db.update(customizationOptions).set(option).where(eq(customizationOptions.id, id)).returning();\n      return result[0];\n    } catch (error) {\n      console.error(\"Error updating customization option:\", error);\n      return undefined;\n    }\n  }\n\n  async deleteCustomizationOption(id: number): Promise<boolean> {\n    try {\n      const result = await db.delete(customizationOptions).where(eq(customizationOptions.id, id));\n      return (result.rowCount || 0) > 0;\n    } catch (error) {\n      console.error(\"Error deleting customization option:\", error);\n      return false;\n    }\n  }\n\n  // Item Customization Map operations\n  async getCustomizationOptionsForMenuItem(itemId: number): Promise<{\n    group: CustomizationGroup;\n    options: CustomizationOption[];\n  }[]> {\n    try {\n      // This is a complex query that joins multiple tables\n      // For now, we'll implement a simpler version\n      const maps = await db.select().from(itemCustomizationMap).where(eq(itemCustomizationMap.itemId, itemId));\n\n      const groupedOptions: Record<number, { group: CustomizationGroup; options: CustomizationOption[] }> = {};\n\n      for (const map of maps) {\n        const option = await this.getCustomizationOptionById(map.optionId);\n        if (option) {\n          const group = await this.getCustomizationGroupById(option.groupId);\n          if (group) {\n            if (!groupedOptions[group.id]) {\n              groupedOptions[group.id] = { group, options: [] };\n            }\n            groupedOptions[group.id].options.push(option);\n          }\n        }\n      }\n\n      return Object.values(groupedOptions);\n    } catch (error) {\n      console.error(\"Error getting customization options for menu item:\", error);\n      return [];\n    }\n  }\n\n  async mapCustomizationOptionToMenuItem(itemId: number, optionId: number): Promise<ItemCustomizationMap> {\n    try {\n      const result = await db.insert(itemCustomizationMap).values({ itemId, optionId }).returning();\n      return result[0];\n    } catch (error) {\n      console.error(\"Error mapping customization option to menu item:\", error);\n      throw error;\n    }\n  }\n\n  async unmapCustomizationOptionFromMenuItem(itemId: number, optionId: number): Promise<boolean> {\n    try {\n      const result = await db.delete(itemCustomizationMap)\n        .where(and(eq(itemCustomizationMap.itemId, itemId), eq(itemCustomizationMap.optionId, optionId)));\n      return (result.rowCount || 0) > 0;\n    } catch (error) {\n      console.error(\"Error unmapping customization option from menu item:\", error);\n      return false;\n    }\n  }\n\n  // Order operations\n  async getOrderById(id: number): Promise<Order | undefined> {\n    try {\n      const result = await db.select().from(orders).where(eq(orders.id, id)).limit(1);\n      return result[0];\n    } catch (error) {\n      console.error(\"Error getting order by id:\", error);\n      return undefined;\n    }\n  }\n\n  async getAllOrders(): Promise<Order[]> {\n    try {\n      const result = await db.select().from(orders);\n      return result;\n    } catch (error) {\n      console.error(\"Error getting all orders:\", error);\n      return [];\n    }\n  }\n\n  async createOrder(insertOrder: InsertOrder): Promise<Order> {\n    try {\n      const result = await db.insert(orders).values(insertOrder).returning();\n      return result[0];\n    } catch (error) {\n      console.error(\"Error creating order:\", error);\n      throw error;\n    }\n  }\n\n  async updateOrder(id: number, order: Partial<InsertOrder>): Promise<Order | undefined> {\n    try {\n      const result = await db.update(orders).set(order).where(eq(orders.id, id)).returning();\n      return result[0];\n    } catch (error) {\n      console.error(\"Error updating order:\", error);\n      return undefined;\n    }\n  }\n\n  async deleteOrder(id: number): Promise<boolean> {\n    try {\n      const result = await db.delete(orders).where(eq(orders.id, id));\n      return (result.rowCount || 0) > 0;\n    } catch (error) {\n      console.error(\"Error deleting order:\", error);\n      return false;\n    }\n  }\n\n  // Cart operations\n  async createCart(cart: { itemId: number, quantity: number, customizations: number[] }): Promise<{ success: boolean, id: number }> {\n    try {\n      // Validate that the item exists\n      const menuItem = await this.getMenuItemById(cart.itemId);\n      if (!menuItem) {\n        throw new Error(\"Menu item not found\");\n      }\n\n      // Validate that all customization options exist\n      for (const optionId of cart.customizations) {\n        const option = await this.getCustomizationOptionById(optionId);\n        if (!option) {\n          throw new Error(`Customization option ${optionId} not found`);\n        }\n      }\n\n      // Since we don't have a cart table yet, we'll just return success\n      return { success: true, id: Date.now() };\n    } catch (error) {\n      console.error(\"Error creating cart:\", error);\n      throw error;\n    }\n  }\n\n  // Contact operations\n  async createContactMessage(insertMessage: InsertContactMessage): Promise<ContactMessage> {\n    try {\n      const result = await db.insert(contactMessages).values(insertMessage).returning();\n      return result[0];\n    } catch (error) {\n      console.error(\"Error creating contact message:\", error);\n      throw error;\n    }\n  }\n\n  async getAllContactMessages(): Promise<ContactMessage[]> {\n    try {\n      const result = await db.select().from(contactMessages);\n      return result;\n    } catch (error) {\n      console.error(\"Error getting all contact messages:\", error);\n      return [];\n    }\n  }\n\n  async getContactMessageById(id: number): Promise<ContactMessage | undefined> {\n    try {\n      const result = await db.select().from(contactMessages).where(eq(contactMessages.id, id)).limit(1);\n      return result[0];\n    } catch (error) {\n      console.error(\"Error getting contact message by id:\", error);\n      return undefined;\n    }\n  }\n\n  async deleteContactMessage(id: number): Promise<boolean> {\n    try {\n      const result = await db.delete(contactMessages).where(eq(contactMessages.id, id));\n      return (result.rowCount || 0) > 0;\n    } catch (error) {\n      console.error(\"Error deleting contact message:\", error);\n      return false;\n    }\n  }\n\n  // Restaurant settings operations\n  async getRestaurantSettings(): Promise<RestaurantSettings | undefined> {\n    try {\n      const result = await db.select().from(restaurantSettings).limit(1);\n      return result[0];\n    } catch (error) {\n      console.error(\"Error getting restaurant settings:\", error);\n      return undefined;\n    }\n  }\n\n  async updateRestaurantSettings(settings: Partial<InsertRestaurantSettings>): Promise<RestaurantSettings | undefined> {\n    try {\n      // First get the existing settings to find the correct ID\n      const existing = await this.getRestaurantSettings();\n\n      if (existing) {\n        // Update the existing record\n        const result = await db.update(restaurantSettings)\n          .set(settings)\n          .where(eq(restaurantSettings.id, existing.id))\n          .returning();\n\n        return result[0];\n      } else {\n        // If no settings exist, create new ones\n        const insertResult = await db.insert(restaurantSettings)\n          .values(settings)\n          .returning();\n\n        return insertResult[0];\n      }\n    } catch (error) {\n      console.error(\"Error updating restaurant settings:\", error);\n      return undefined;\n    }\n  }\n}\n\n// Use DatabaseStorage for persistent storage\nexport const storage = new DatabaseStorage();\n\n// Keep MemStorage available for testing or fallback\nexport const memStorage = new MemStorage();", "path": "server/storage.ts", "language": "typescript", "prefixBegin": 0, "suffixEnd": 43}}], ["2961a67b-59e7-4d5a-9abb-ca25df2d6c7f", {"value": {"selectedCode": "", "prefix": "import { Express, Request, Response, NextFunction, Router } from 'express';\nimport http from 'http';\nimport { log } from './vite';\nimport { storage } from './storage';\nimport adminApiRouter from './admin-api';\nimport { setupAuth, isAdmin } from './auth';\n\nexport async function registerRoutes(app: Express): Promise<http.Server> {\n  const apiRouter = Router();\n\n  // Logging middleware\n  app.use((req: Request, _res: Response, next: NextFunction) => {\n    if (req.url.startsWith('/api')) {\n      log(`${req.method} ${req.url}`, 'api');\n    }\n    next();\n  });\n\n  // Reusable error handler\n  const handleError = (error: any, res: Response) => {\n    console.error(error);\n    res.status(500).json({ error: 'An unexpected error occurred' });\n  };\n\n  // Menu Items / Dishes Routes\n  apiRouter.get('/dishes', async (req: Request, res: Response) => {\n    try {\n      const dishes = await storage.getAllDishes();\n      res.json(dishes);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  apiRouter.get('/dishes/:id', async (req: Request, res: Response) => {\n    try {\n      const id = parseInt(req.params.id);\n      const dish = await storage.getDishById(id);\n\n      if (!dish) {\n        return res.status(404).json({ error: 'Dish not found' });\n      }\n\n      res.json(dish);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  // Categories Routes\n  apiRouter.get('/categories', async (req: Request, res: Response) => {\n    try {\n      const categories = await storage.getAllMenuCategories();\n      res.json(categories);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  apiRouter.post('/categories', async (req: Request, res: Response) => {\n    try {\n      if (!req.body.name) {\n        return res.status(400).json({ error: 'Category name is required' });\n      }\n\n      const newCategory = await storage.createMenuCategory(req.body);\n      res.status(201).json(newCategory);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  apiRouter.put('/categories/:id', async (req: Request, res: Response) => {\n    try {\n      const id = parseInt(req.params.id);\n      const category = await storage.getMenuCategoryById(id);\n\n      if (!category) {\n        return res.status(404).json({ error: 'Category not found' });\n      }\n\n      const updatedCategory = await storage.updateMenuCategory(id, req.body);\n      res.json(updatedCategory);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  apiRouter.delete('/categories/:id', async (req: Request, res: Response) => {\n    try {\n      const id = parseInt(req.params.id);\n      const category = await storage.getMenuCategoryById(id);\n\n      if (!category) {\n        return res.status(404).json({ error: 'Category not found' });\n      }\n\n      const deletedCategory = await storage.deleteMenuCategory(id);\n      res.json({ message: 'Category deleted successfully', category: deletedCategory });\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  // Menu Items Routes\n  apiRouter.get('/items', async (req: Request, res: Response) => {\n    try {\n      const items = await storage.getAllMenuItems();\n      res.json(items);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  apiRouter.post('/items', async (req: Request, res: Response) => {\n    try {\n      const newItem = await storage.createMenuItem(req.body);\n      res.status(201).json(newItem);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  apiRouter.put('/items/:id', async (req: Request, res: Response) => {\n    try {\n      const id = parseInt(req.params.id);\n      const item = await storage.getMenuItemById(id);\n\n      if (!item) {\n        return res.status(404).json({ error: 'Menu item not found' });\n      }\n\n      // Update logic would go here\n      res.json({ ...item, ...req.body });\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  // Order Routes\n  apiRouter.post('/orders', async (req: Request, res: Response) => {\n    try {\n      // Get restaurant settings to check if it's open\n      const adminSettings = await fetch('http://localhost:5000/api/admin/settings').then(res => res.json());\n\n      // Check if restaurant is open\n      if (!adminSettings.restaurant_open) {\n        return res.status(403).json({\n          error: 'Restaurant is currently closed. Orders cannot be placed at this time.'\n", "suffix": "        });\n      }\n\n      // If restaurant is open, proceed with order creation\n      const newOrder = await storage.createOrder(req.body);\n      res.status(201).json(newOrder);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  apiRouter.get('/orders/:id', async (req: Request, res: Response) => {\n    try {\n      const id = parseInt(req.params.id);\n      const order = await storage.getOrderById(id);\n\n      if (!order) {\n        return res.status(404).json({ error: 'Order not found' });\n      }\n\n      res.json(order);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  // Contact Form Route\n  apiRouter.post('/contact', async (req: Request, res: Response) => {\n    try {\n      const message = await storage.createContactMessage(req.body);\n      res.status(201).json({ success: true, message });\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  // Cart Routes\n  apiRouter.post('/cart', async (req: Request, res: Response) => {\n    try {\n      const result = await storage.createCart(req.body);\n      res.status(201).json(result);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  // Public Settings Route (for checkout page to get delivery fee)\n  apiRouter.get('/settings', async (req: Request, res: Response) => {\n    try {\n      const settings = await storage.getRestaurantSettings();\n\n      if (!settings) {\n        // Return default settings if none exist\n        return res.json({\n          delivery_fee: 49,\n          estimated_time: \"25-35 min\",\n          restaurant_open: true\n        });\n      }\n\n      // Return only public settings (no business hours)\n      res.json({\n        delivery_fee: settings.deliveryFee,\n        estimated_time: settings.estimatedTime,\n        restaurant_open: settings.restaurantOpen\n      });\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  // Public Customizations Route (for menu page)\n  apiRouter.get('/items/:itemId/customizations', async (req: Request, res: Response) => {\n    try {\n      const itemId = parseInt(req.params.itemId);\n      const customizations = await storage.getCustomizationOptionsForMenuItem(itemId);\n      res.json(customizations);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  // Register the API router with app\n  app.use('/api', apiRouter);\n\n  // Setup authentication\n  setupAuth(app);\n\n  // Register admin API routes with admin protection\n  app.use('/api/admin', isAdmin, adminApiRouter);\n\n  // Create and return the server\n  const server = http.createServer(app);\n  return server;\n}", "path": "server/routes.ts", "language": "typescript", "prefixBegin": 0, "suffixEnd": 1}}], ["c38ca032-355e-47b6-95d7-05d923ae5ec8", {"value": {"selectedCode": "", "prefix": "import { pgTable, text, serial, integer, boolean, jsonb, timestamp, foreignKey } from \"drizzle-orm/pg-core\";\nimport { createInsertSchema } from \"drizzle-zod\";\nimport { z } from \"zod\";\n\n// Categories Schema\nexport const categories = pgTable(\"categories\", {\n  id: serial(\"id\").primaryKey(),\n  name: text(\"name\").notNull(),\n", "suffix": "  imageUrl: text(\"image_url\").notNull(),\n});\n\nexport const insertCategorySchema = createInsertSchema(categories).omit({ id: true });\nexport type InsertCategory = z.infer<typeof insertCategorySchema>;\nexport type Category = typeof categories.$inferSelect;\n\n// Dish Schema (renamed to menu_items to match the new API structure)\nexport const menuItems = pgTable(\"menu_items\", {\n  id: serial(\"id\").primaryKey(),\n  name: text(\"name\").notNull(),\n  description: text(\"description\").notNull(),\n  price: integer(\"price\").notNull(), // Price in NOK\n  imageUrl: text(\"image_url\").notNull(),\n  categoryId: integer(\"category_id\").notNull().references(() => categories.id),\n  available: boolean(\"available\").default(true),\n  rating: integer(\"rating\").default(0),\n  reviews: integer(\"reviews\").default(0),\n});\n\nexport const insertMenuItemSchema = createInsertSchema(menuItems).omit({ id: true });\nexport type InsertMenuItem = z.infer<typeof insertMenuItemSchema>;\nexport type MenuItem = typeof menuItems.$inferSelect;\n\n// Customization Groups Schema\nexport const customizationGroups = pgTable(\"customization_groups\", {\n  id: serial(\"id\").primaryKey(),\n  title: text(\"title\").notNull(),\n});\n\nexport const insertCustomizationGroupSchema = createInsertSchema(customizationGroups).omit({ id: true });\nexport type InsertCustomizationGroup = z.infer<typeof insertCustomizationGroupSchema>;\nexport type CustomizationGroup = typeof customizationGroups.$inferSelect;\n\n// Customization Options Schema\nexport const customizationOptions = pgTable(\"customization_options\", {\n  id: serial(\"id\").primaryKey(),\n  name: text(\"name\").notNull(),\n  extraPrice: integer(\"extra_price\").default(0),\n  imageUrl: text(\"image_url\").notNull().default(\"\"),\n  groupId: integer(\"group_id\").notNull().references(() => customizationGroups.id),\n});\n\nexport const insertCustomizationOptionSchema = createInsertSchema(customizationOptions).omit({ id: true });\nexport type InsertCustomizationOption = z.infer<typeof insertCustomizationOptionSchema>;\nexport type CustomizationOption = typeof customizationOptions.$inferSelect;\n\n// Item Customization Map (many-to-many relationship)\nexport const itemCustomizationMap = pgTable(\"item_customization_map\", {\n  id: serial(\"id\").primaryKey(),\n  itemId: integer(\"item_id\").notNull().references(() => menuItems.id),\n  optionId: integer(\"option_id\").notNull().references(() => customizationOptions.id),\n});\n\nexport const insertItemCustomizationMapSchema = createInsertSchema(itemCustomizationMap).omit({ id: true });\nexport type InsertItemCustomizationMap = z.infer<typeof insertItemCustomizationMapSchema>;\nexport type ItemCustomizationMap = typeof itemCustomizationMap.$inferSelect;\n\n// For backwards compatibility with existing code\nexport const dishes = menuItems;\nexport const insertDishSchema = insertMenuItemSchema;\nexport type InsertDish = InsertMenuItem;\nexport type Dish = MenuItem;\n\n// Order Schema\nexport const orders = pgTable(\"orders\", {\n  id: serial(\"id\").primaryKey(),\n  customer: jsonb(\"customer\").notNull(),\n  items: jsonb(\"items\").notNull(),\n  orderDetails: jsonb(\"order_details\").notNull(),\n  subtotal: integer(\"subtotal\").notNull(),\n  deliveryFee: integer(\"delivery_fee\").notNull(),\n  total: integer(\"total\").notNull(),\n  status: text(\"status\").notNull().default(\"pending\"),\n  paymentMethod: text(\"payment_method\").notNull(),\n  notes: text(\"notes\"),\n  createdAt: timestamp(\"created_at\").defaultNow(),\n});\n\nexport const insertOrderSchema = createInsertSchema(orders).omit({\n  id: true,\n  createdAt: true\n});\nexport type InsertOrder = z.infer<typeof insertOrderSchema>;\nexport type Order = typeof orders.$inferSelect;\n\n// Contact Message Schema\nexport const contactMessages = pgTable(\"contact_messages\", {\n  id: serial(\"id\").primaryKey(),\n  name: text(\"name\").notNull(),\n  email: text(\"email\").notNull(),\n  subject: text(\"subject\").notNull(),\n  message: text(\"message\").notNull(),\n  createdAt: timestamp(\"created_at\").defaultNow(),\n});\n\nexport const insertContactSchema = createInsertSchema(contactMessages).omit({\n  id: true,\n  createdAt: true\n});\nexport type InsertContactMessage = z.infer<typeof insertContactSchema>;\nexport type ContactMessage = typeof contactMessages.$inferSelect;\n\n// User Schema\nexport const users = pgTable(\"users\", {\n  id: serial(\"id\").primaryKey(),\n  username: text(\"username\").notNull().unique(),\n  email: text(\"email\").notNull().unique(),\n  password: text(\"password\").notNull(),\n  first_name: text(\"first_name\"),\n  last_name: text(\"last_name\"),\n  role: text(\"role\").notNull().default(\"customer\"),\n  is_active: boolean(\"is_active\").notNull().default(true),\n  created_at: timestamp(\"created_at\").defaultNow(),\n  updated_at: timestamp(\"updated_at\").defaultNow(),\n});\n\nexport const insertUserSchema = createInsertSchema(users).omit({\n  id: true,\n  created_at: true,\n  updated_at: true,\n});\n\nexport const loginUserSchema = z.object({\n  username: z.string().min(3).max(50),\n  password: z.string().min(6),\n});\n\nexport type InsertUser = z.infer<typeof insertUserSchema>;\nexport type LoginUser = z.infer<typeof loginUserSchema>;\nexport type User = typeof users.$inferSelect;\n\n// Restaurant Settings Schema\nexport const restaurantSettings = pgTable(\"restaurant_settings\", {\n  id: serial(\"id\").primaryKey(),\n  restaurantOpen: boolean(\"restaurant_open\").default(true),\n  businessHours: jsonb(\"business_hours\").default({}),\n  deliveryFee: integer(\"delivery_fee\").default(49),\n  estimatedTime: text(\"estimated_time\").default(\"25-35 min\"),\n});\n\nexport const insertRestaurantSettingsSchema = createInsertSchema(restaurantSettings).omit({ id: true });\nexport type InsertRestaurantSettings = z.infer<typeof insertRestaurantSettingsSchema>;\nexport type RestaurantSettings = typeof restaurantSettings.$inferSelect;\n", "path": "shared/schema.ts", "language": "typescript", "prefixBegin": 0, "suffixEnd": 0}}], ["1b0dd8f1-aea9-410a-a821-33a15a882151", {"value": {"selectedCode": "", "prefix": "import { useState } from \"react\";\n", "suffix": "import { useLocation } from \"wouter\";\nimport { motion } from \"framer-motion\";\nimport { z } from \"zod\";\nimport { useForm } from \"react-hook-form\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { Eye, EyeOff, Lock, User } from \"lucide-react\";\n\n// Define validation schema\nconst loginSchema = z.object({\n  username: z.string().min(3, \"Username must be at least 3 characters\"),\n  password: z.string().min(6, \"Password must be at least 6 characters\"),\n});\n\ntype LoginFormData = z.infer<typeof loginSchema>;\n\nconst AuthPage = () => {\n  const [, setLocation] = useLocation();\n  const [showPassword, setShowPassword] = useState(false);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(\"\");\n\n  // Form handling\n  const { register, handleSubmit, formState: { errors } } = useForm<LoginFormData>({\n    resolver: zod<PERSON><PERSON>olver(loginSchema),\n    defaultValues: {\n      username: \"\",\n      password: \"\"\n    }\n  });\n\n  const togglePasswordVisibility = () => {\n    setShowPassword(!showPassword);\n  };\n\n  const onSubmit = async (data: LoginFormData) => {\n    setIsLoading(true);\n    setError(\"\");\n\n    try {\n      // Make API call to authenticate\n      const response = await fetch('/api/login', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(data),\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.message || 'Authentication failed');\n      }\n\n      const userData = await response.json();\n\n      // Secure silent redirect based on user role\n      if (userData.role === 'admin') {\n        setLocation(\"/admin/settings\");\n      } else if (userData.role === 'manager') {\n        setLocation(\"/manager\");\n      } else if (userData.role === 'driver') {\n        setLocation(\"/driver\");\n      } else {\n        // Fallback for unknown roles\n        setError(\"Authentication failed. Please contact support.\");\n      }\n    } catch (err) {\n      setError(err instanceof Error ? err.message : \"Invalid username or password\");\n      console.error(\"Login error:\", err);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-black text-white flex flex-col md:flex-row\">\n      {/* Left side - Login Form */}\n      <div className=\"w-full md:w-1/2 flex flex-col justify-center p-6 md:p-12\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.5 }}\n          className=\"max-w-md mx-auto w-full\"\n        >\n          <div className=\"text-center mb-8\">\n            <h1 className=\"text-3xl font-bold bg-gradient-to-r from-pink-500 via-purple-500 to-cyan-500 text-transparent bg-clip-text mb-2\">\n              Barbecuez Staff Portal\n            </h1>\n            <p className=\"text-gray-400\">Sign in to access your dashboard</p>\n          </div>\n\n          {error && (\n            <div className=\"bg-red-900/40 border border-red-700 text-red-400 p-4 rounded-lg mb-6\">\n              {error}\n            </div>\n          )}\n\n          <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-6\">\n            <div className=\"space-y-2\">\n              <label htmlFor=\"username\" className=\"text-sm font-medium text-gray-300 block\">\n                Username\n              </label>\n              <div className=\"relative\">\n                <div className=\"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none\">\n                  <User className=\"h-5 w-5 text-gray-500\" />\n                </div>\n                <input\n                  id=\"username\"\n                  type=\"text\"\n                  {...register(\"username\")}\n                  className={`bg-gray-900 border ${\n                    errors.username ? \"border-red-600\" : \"border-gray-700\"\n                  } text-white rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 p-3 placeholder-gray-500`}\n                  placeholder=\"Enter your username\"\n                />\n              </div>\n              {errors.username && (\n                <p className=\"text-red-500 text-sm\">{errors.username.message}</p>\n              )}\n            </div>\n\n            <div className=\"space-y-2\">\n              <label htmlFor=\"password\" className=\"text-sm font-medium text-gray-300 block\">\n                Password\n              </label>\n              <div className=\"relative\">\n                <div className=\"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none\">\n                  <Lock className=\"h-5 w-5 text-gray-500\" />\n                </div>\n                <input\n                  id=\"password\"\n                  type={showPassword ? \"text\" : \"password\"}\n                  {...register(\"password\")}\n                  className={`bg-gray-900 border ${\n                    errors.password ? \"border-red-600\" : \"border-gray-700\"\n                  } text-white rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 p-3 pr-10 placeholder-gray-500`}\n                  placeholder=\"Enter your password\"\n                />\n                <div className=\"absolute inset-y-0 right-0 flex items-center pr-3\">\n                  <button\n                    type=\"button\"\n                    onClick={togglePasswordVisibility}\n                    className=\"text-gray-500 hover:text-gray-300 focus:outline-none\"\n                  >\n                    {showPassword ? (\n                      <EyeOff className=\"h-5 w-5\" />\n                    ) : (\n                      <Eye className=\"h-5 w-5\" />\n                    )}\n                  </button>\n                </div>\n              </div>\n              {errors.password && (\n                <p className=\"text-red-500 text-sm\">{errors.password.message}</p>\n              )}\n            </div>\n\n            <motion.button\n              type=\"submit\"\n              disabled={isLoading}\n              className=\"w-full bg-gradient-to-r from-pink-600 to-purple-600 hover:from-pink-700 hover:to-purple-700 text-white font-medium rounded-lg p-3 flex items-center justify-center disabled:opacity-70 disabled:cursor-not-allowed\"\n              whileHover={{ scale: 1.02 }}\n              whileTap={{ scale: 0.98 }}\n            >\n              {isLoading ? (\n                <>\n                  <svg className=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n                    <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                    <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                  </svg>\n                  Signing in...\n                </>\n              ) : (\n                \"Sign In\"\n              )}\n            </motion.button>\n\n            <div className=\"text-sm text-center text-gray-500 mt-4\">\n              <p>Need help? Contact system administrator</p>\n            </div>\n          </form>\n\n          {/* Quick login for testing */}\n          {process.env.NODE_ENV === \"development\" && (\n            <div className=\"mt-8 p-4 border border-gray-800 rounded-lg\">\n              <h3 className=\"text-sm font-medium text-gray-400 mb-2\">Test Accounts:</h3>\n              <div className=\"grid grid-cols-3 gap-2 text-xs\">\n                <div className=\"bg-gray-900 p-2 rounded border border-gray-800\">\n                  <p className=\"font-medium text-cyan-400\">Admin</p>\n                  <p className=\"text-gray-500\">admin</p>\n                </div>\n                <div className=\"bg-gray-900 p-2 rounded border border-gray-800\">\n                  <p className=\"font-medium text-orange-400\">Manager</p>\n                  <p className=\"text-gray-500\">manager</p>\n                </div>\n                <div className=\"bg-gray-900 p-2 rounded border border-gray-800\">\n                  <p className=\"font-medium text-pink-400\">Driver</p>\n                  <p className=\"text-gray-500\">driver</p>\n                </div>\n              </div>\n              <p className=\"text-xs text-gray-500 mt-2\">Note: Passwords are stored securely in the database</p>\n            </div>\n          )}\n        </motion.div>\n      </div>\n\n      {/* Right side - Hero Section */}\n      <div className=\"hidden md:block md:w-1/2 bg-gradient-to-br from-gray-900 to-black relative overflow-hidden\">\n        <div className=\"absolute inset-0 bg-black opacity-60\"></div>\n        <div\n          className=\"absolute inset-0 bg-[url('https://images.unsplash.com/photo-1544025162-d76694265947?q=80&w=2069')] bg-cover bg-center\"\n          style={{ backgroundPosition: 'center 40%' }}\n        >\n          <div className=\"absolute inset-0 bg-gradient-to-r from-black via-black/90 to-transparent\"></div>\n        </div>\n\n        <div className=\"relative h-full flex flex-col justify-center px-12 z-10\">\n          <h2 className=\"text-4xl font-bold text-white mb-6\">\n            Welcome to the<br />\n            <span className=\"bg-gradient-to-r from-pink-500 via-purple-500 to-cyan-500 text-transparent bg-clip-text\">\n              Barbecuez Staff Portal\n            </span>\n          </h2>\n\n          <div className=\"space-y-6 max-w-md\">\n            <div className=\"flex items-start space-x-3\">\n              <div className=\"mt-1 bg-pink-600/20 p-1 rounded\">\n                <svg className=\"h-5 w-5 text-pink-500\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\" />\n                </svg>\n              </div>\n              <div>\n                <h3 className=\"text-lg font-medium text-white\">Admin Portal</h3>\n                <p className=\"text-gray-400\">Manage restaurant settings, menu items, and view analytics</p>\n              </div>\n            </div>\n\n            <div className=\"flex items-start space-x-3\">\n              <div className=\"mt-1 bg-purple-600/20 p-1 rounded\">\n                <svg className=\"h-5 w-5 text-purple-500\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\" />\n                </svg>\n              </div>\n              <div>\n                <h3 className=\"text-lg font-medium text-white\">Kitchen Manager</h3>\n                <p className=\"text-gray-400\">Process and track orders in real-time with status updates</p>\n              </div>\n            </div>\n\n            <div className=\"flex items-start space-x-3\">\n              <div className=\"mt-1 bg-cyan-600/20 p-1 rounded\">\n                <svg className=\"h-5 w-5 text-cyan-500\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\" />\n                </svg>\n              </div>\n              <div>\n                <h3 className=\"text-lg font-medium text-white\">Delivery Dashboard</h3>\n                <p className=\"text-gray-400\">Track deliveries and update customer order status</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AuthPage;", "path": "client/src/pages/auth/AuthPage.tsx", "language": "typescriptreact", "prefixBegin": 0, "suffixEnd": 24}}], ["2edfbb95-0550-4771-857d-dd013ae34c79", {"value": {"selectedCode": "", "prefix": "import { useState, useEffect } from \"react\";\nimport { useLocation, Link } from \"wouter\";\nimport { motion, AnimatePresence } from \"framer-motion\";\n\ninterface OrderConfirmationProps {\n  params?: {\n    orderId?: string;\n  };\n}\n\nconst OrderConfirmation = (props: OrderConfirmationProps) => {\n  const orderId = props.params?.orderId || \"BBC\" + Math.floor(Math.random() * 100000);\n  const [, setLocation] = useLocation();\n  const [showConfetti, setShowConfetti] = useState(true);\n  const [countdown, setCountdown] = useState(3);\n  const [isRedirecting, setIsRedirecting] = useState(false);\n\n  // Estimated delivery time calculation (30-45 minutes from now)\n  const now = new Date();\n  const deliveryTime = new Date(now.getTime() + 45 * 60000);\n  const deliveryTimeString = deliveryTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });\n\n  // Countdown timer and redirect logic\n  useEffect(() => {\n    // Start countdown after 2 seconds to let user read the confirmation\n    const startCountdownTimer = setTimeout(() => {\n      const countdownInterval = setInterval(() => {\n        setCountdown((prev) => {\n          if (prev <= 1) {\n            clearInterval(countdownInterval);\n            handleRedirectToTracking();\n            return 0;\n          }\n          return prev - 1;\n        });\n      }, 1000);\n\n      return () => clearInterval(countdownInterval);\n    }, 2000);\n\n    return () => clearTimeout(startCountdownTimer);\n  }, []);\n\n  // Function to handle redirect to order tracking\n  const handleRedirectToTracking = () => {\n    setIsRedirecting(true);\n\n    // Validate order ID before redirecting\n    if (!orderId || orderId === \"BBC\" + Math.floor(Math.random() * 100000)) {\n      // If no valid order ID, redirect to a general order lookup page\n      setLocation('/track-order');\n      return;\n    }\n\n    // Redirect to real order tracking page\n    setLocation(`/track-order/${orderId}`);\n  };\n\n  // Function to skip countdown and go directly to tracking\n  const handleSkipCountdown = () => {\n    setCountdown(0);\n    handleRedirectToTracking();\n  };\n\n  // Animation variants\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        delayChildren: 0.3,\n        staggerChildren: 0.2\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { y: 20, opacity: 0 },\n    visible: {\n      y: 0,\n      opacity: 1,\n      transition: {\n        type: \"spring\",\n        stiffness: 100,\n        damping: 10\n      }\n    }\n  };\n\n  const pulseVariants = {\n    initial: { scale: 1, boxShadow: \"0 0 0px rgba(57, 255, 20, 0)\" },\n    animate: {\n      scale: [1, 1.05, 1],\n      boxShadow: [\n        \"0 0 0px rgba(57, 255, 20, 0)\",\n        \"0 0 20px rgba(57, 255, 20, 0.7)\",\n        \"0 0 0px rgba(57, 255, 20, 0)\"\n      ],\n      transition: {\n        duration: 2,\n        repeat: Infinity,\n        repeatType: \"loop\"\n      }\n    }\n  };\n\n  // Disable confetti after a few seconds\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      setShowConfetti(false);\n    }, 5000);\n\n    return () => clearTimeout(timer);\n  }, []);\n\n  return (\n    <main className=\"min-h-screen bg-black relative overflow-hidden flex items-center justify-center py-12\">\n      {/* Animated Gradient Background */}\n      <div\n        className=\"absolute inset-0 z-0\"\n        style={{\n          background: \"radial-gradient(circle at 20% 20%, rgba(0, 255, 255, 0.03), transparent 33%), \" +\n                    \"radial-gradient(circle at 80% 80%, rgba(57, 255, 20, 0.03), transparent 33%), \" +\n                    \"radial-gradient(circle at 50% 50%, rgba(255, 0, 255, 0.03), rgba(0, 0, 0, 1) 100%)\",\n        }}\n      ></div>\n\n      {/* Neon Grid Overlay */}\n      <div className=\"absolute inset-0 z-0 opacity-[0.03]\"\n           style={{\n             backgroundImage: \"linear-gradient(to right, #39FF14 1px, transparent 1px), linear-gradient(to bottom, #39FF14 1px, transparent 1px)\",\n             backgroundSize: \"40px 40px\"\n           }}>\n      </div>\n\n      {/* Confetti Animation */}\n      {showConfetti && (\n        <div className=\"absolute inset-0 pointer-events-none z-10\">\n          {[...Array(40)].map((_, i) => (\n            <motion.div\n              key={i}\n              className=\"absolute w-3 h-3 rounded-full\"\n              style={{\n                backgroundColor:\n                  i % 3 === 0 ? 'rgba(57, 255, 20, 0.7)' :\n                  i % 3 === 1 ? 'rgba(0, 255, 255, 0.7)' :\n                  'rgba(255, 0, 255, 0.7)',\n                top: `${Math.random() * -10}%`,\n                left: `${Math.random() * 100}%`,\n              }}\n              initial={{\n                y: -20,\n                opacity: 0,\n                scale: 0\n              }}\n              animate={{\n                y: `${100 + Math.random() * 50}vh`,\n                opacity: [1, 0.8, 0],\n                scale: [1, 0.8, 0.6],\n                rotate: [0, Math.random() * 360]\n              }}\n              transition={{\n                duration: 2.5 + Math.random() * 3.5,\n                delay: Math.random() * 3,\n                ease: \"easeOut\",\n                repeat: 1,\n                repeatType: \"loop\",\n                repeatDelay: Math.random() * 2\n              }}\n            />\n          ))}\n        </div>\n      )}\n\n      <motion.div\n        className=\"container mx-auto px-4 z-10 relative\"\n        variants={containerVariants}\n        initial=\"hidden\"\n        animate=\"visible\"\n      >\n        <div className=\"max-w-xl mx-auto\">\n          <motion.div\n            className=\"bg-black/30 backdrop-blur-sm rounded-2xl p-8 md:p-12 border border-lime-800/30\n                       shadow-[0_0_30px_rgba(57,255,20,0.2)]\"\n            initial={{ opacity: 0, scale: 0.9 }}\n            animate={{ opacity: 1, scale: 1 }}\n            transition={{ duration: 0.6 }}\n          >\n            <div className=\"text-center\">\n              {/* Success Icon */}\n              <motion.div\n                className=\"w-28 h-28 mx-auto mb-8 rounded-full bg-gradient-to-br from-lime-500/20 to-green-700/20\n                         flex items-center justify-center\"\n                initial={{ scale: 0 }}\n                animate={{ scale: 1 }}\n                transition={{\n                  type: \"spring\",\n                  stiffness: 200,\n                  damping: 15,\n                  delay: 0.2\n                }}\n              >\n                <motion.div\n                  className=\"w-24 h-24 rounded-full bg-gradient-to-br from-lime-500/30 to-green-700/30\n                           flex items-center justify-center\"\n                  variants={pulseVariants}\n                  initial=\"initial\"\n                  animate=\"animate\"\n                >\n                  <svg className=\"w-16 h-16 text-lime-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <motion.path\n                      strokeLinecap=\"round\"\n                      strokeLinejoin=\"round\"\n                      strokeWidth={2.5}\n                      d=\"M5 13l4 4L19 7\"\n                      initial={{ pathLength: 0 }}\n                      animate={{ pathLength: 1 }}\n                      transition={{ duration: 0.8, delay: 0.5 }}\n                    />\n                  </svg>\n                </motion.div>\n              </motion.div>\n\n              {/* Heading */}\n              <motion.h1\n                className=\"font-playfair text-3xl md:text-4xl font-bold mb-3 text-white\"\n                variants={itemVariants}\n              >\n                Thank you for your order!\n              </motion.h1>\n\n              {/* Order ID */}\n              <motion.div\n                className=\"mb-6\"\n                variants={itemVariants}\n              >\n                <p className=\"text-gray-300 text-lg\">Order <span className=\"text-lime-400 font-medium\">#{orderId}</span> confirmed</p>\n              </motion.div>\n\n              {/* Order Summary Box */}\n              <motion.div\n                className=\"bg-black/50 rounded-xl p-6 mb-8 border border-gray-800\"\n                variants={itemVariants}\n              >\n                <div className=\"flex items-center justify-center mb-3\">\n                  <svg className=\"w-5 h-5 text-lime-400 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                  </svg>\n                  <h3 className=\"font-medium text-white\">Estimated Delivery/Pickup</h3>\n                </div>\n                <p className=\"text-lime-400 text-xl font-bold mb-1\">{deliveryTimeString}</p>\n                <p className=\"text-gray-400 text-sm\">\n                  Our chef is preparing your delicious meal\n                </p>\n              </motion.div>\n\n              {/* Countdown Timer and Track Order Section */}\n              <motion.div variants={itemVariants} className=\"space-y-4\">\n                {/* Countdown Timer */}\n                {countdown > 0 && !isRedirecting && (\n                  <motion.div\n                    className=\"bg-black/40 rounded-lg p-4 border border-lime-800/30\"\n                    initial={{ opacity: 0, y: 10 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ delay: 2 }}\n                  >\n                    <div className=\"flex items-center justify-center space-x-2 text-gray-300\">\n                      <svg className=\"w-4 h-4 text-lime-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                      </svg>\n                      <span className=\"text-sm\">\n                        Redirecting to order tracking in{' '}\n                        <motion.span\n                          className=\"text-lime-400 font-bold text-lg\"\n                          key={countdown}\n                          initial={{ scale: 1.2 }}\n                          animate={{ scale: 1 }}\n                          transition={{ duration: 0.3 }}\n                        >\n                          {countdown}\n                        </motion.span>\n                        {' '}second{countdown !== 1 ? 's' : ''}...\n                      </span>\n                    </div>\n                  </motion.div>\n                )}\n\n                {/* Redirecting State */}\n                {isRedirecting && (\n                  <motion.div\n                    className=\"bg-black/40 rounded-lg p-4 border border-lime-800/30\"\n                    initial={{ opacity: 0 }}\n                    animate={{ opacity: 1 }}\n                  >\n                    <div className=\"flex items-center justify-center space-x-2 text-lime-400\">\n                      <motion.svg\n                        className=\"w-4 h-4\"\n                        fill=\"none\"\n                        viewBox=\"0 0 24 24\"\n                        stroke=\"currentColor\"\n                        animate={{ rotate: 360 }}\n                        transition={{ duration: 1, repeat: Infinity, ease: \"linear\" }}\n                      >\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\" />\n                      </motion.svg>\n                      <span className=\"text-sm font-medium\">Redirecting to order tracking...</span>\n                    </div>\n                  </motion.div>\n                )}\n\n                {/* Track Order Now Button */}\n                <motion.button\n                  onClick={handleSkipCountdown}\n                  disabled={isRedirecting}\n                  className=\"relative overflow-hidden rounded-lg px-8 py-4 group w-full disabled:opacity-50 disabled:cursor-not-allowed\"\n", "suffix": "                  whileHover={!isRedirecting ? { scale: 1.03 } : {}}\n                  whileTap={!isRedirecting ? { scale: 0.97 } : {}}\n                  transition={{ type: \"spring\", stiffness: 400, damping: 17 }}\n                >\n                  {/* Button Background */}\n                  <span className=\"absolute inset-0 w-full h-full bg-gradient-to-r from-lime-700/70 to-lime-600/70\"></span>\n\n                  {/* Button Glow Effect */}\n                  <span className=\"absolute inset-0 w-full h-full transition-all duration-300\n                                  bg-gradient-to-r from-lime-600/50 via-lime-500/50 to-lime-600/50\n                                  opacity-0 group-hover:opacity-100 group-hover:blur-md\"></span>\n\n                  {/* Button Border */}\n                  <span className=\"absolute inset-0 w-full h-full border border-lime-500/50 rounded-lg\"></span>\n\n                  {/* Button Text */}\n                  <span className=\"relative z-10 flex items-center justify-center text-white font-medium text-lg\">\n                    <svg className=\"w-5 h-5 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2}\n                          d=\"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\" />\n                    </svg>\n                    {countdown > 0 && !isRedirecting ? 'Track Order Now' : 'Track Your Order'}\n                  </span>\n                </motion.button>\n              </motion.div>\n\n              {/* Return to Home */}\n              <motion.div\n                variants={itemVariants}\n                className=\"mt-6\"\n              >\n                <Link href=\"/\">\n                  <motion.button\n                    className=\"text-gray-400 hover:text-lime-400 transition-colors duration-300\"\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                  >\n                    Return to Home\n                  </motion.button>\n                </Link>\n              </motion.div>\n            </div>\n          </motion.div>\n        </div>\n      </motion.div>\n    </main>\n  );\n};\n\nexport default OrderConfirmation;", "path": "client/src/pages/OrderConfirmation.tsx", "language": "typescriptreact", "prefixBegin": 0, "suffixEnd": 33}}], ["969cdbb2-ad6a-4cf3-9476-f065ecdafb2f", {"value": {"selectedCode": "", "prefix": "import { useState, useEffect } from \"react\";\nimport { useLocation, Link } from \"wouter\";\nimport { motion, AnimatePresence } from \"framer-motion\";\n\ninterface OrderConfirmationProps {\n  params?: {\n    orderId?: string;\n  };\n}\n\nconst OrderConfirmation = (props: OrderConfirmationProps) => {\n  const orderId = props.params?.orderId || \"BBC\" + Math.floor(Math.random() * 100000);\n  const [, setLocation] = useLocation();\n  const [showConfetti, setShowConfetti] = useState(true);\n  const [countdown, setCountdown] = useState(3);\n  const [isRedirecting, setIsRedirecting] = useState(false);\n\n  // Estimated delivery time calculation (30-45 minutes from now)\n  const now = new Date();\n  const deliveryTime = new Date(now.getTime() + 45 * 60000);\n  const deliveryTimeString = deliveryTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });\n\n  // Countdown timer and redirect logic\n  useEffect(() => {\n    // Start countdown after 2 seconds to let user read the confirmation\n    const startCountdownTimer = setTimeout(() => {\n      const countdownInterval = setInterval(() => {\n        setCountdown((prev) => {\n          if (prev <= 1) {\n            clearInterval(countdownInterval);\n            handleRedirectToTracking();\n            return 0;\n          }\n          return prev - 1;\n        });\n      }, 1000);\n\n      return () => clearInterval(countdownInterval);\n    }, 2000);\n\n    return () => clearTimeout(startCountdownTimer);\n  }, []);\n\n  // Function to handle redirect to order tracking\n  const handleRedirectToTracking = () => {\n    setIsRedirecting(true);\n\n    // Validate order ID before redirecting\n    if (!orderId || orderId.startsWith('BBC') && orderId.length < 6) {\n      // If no valid order ID, redirect to a general order lookup page\n      setLocation('/track-order');\n      return;\n    }\n\n    // Redirect to real order tracking page\n    setLocation(`/track-order/${orderId}`);\n  };\n\n  // Function to skip countdown and go directly to tracking\n  const handleSkipCountdown = () => {\n    setCountdown(0);\n    handleRedirectToTracking();\n  };\n\n  // Animation variants\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        delayChildren: 0.3,\n        staggerChildren: 0.2\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { y: 20, opacity: 0 },\n    visible: {\n      y: 0,\n      opacity: 1,\n      transition: {\n        type: \"spring\",\n        stiffness: 100,\n        damping: 10\n      }\n    }\n  };\n\n  const pulseVariants = {\n    initial: { scale: 1, boxShadow: \"0 0 0px rgba(57, 255, 20, 0)\" },\n    animate: {\n      scale: [1, 1.05, 1],\n      boxShadow: [\n        \"0 0 0px rgba(57, 255, 20, 0)\",\n        \"0 0 20px rgba(57, 255, 20, 0.7)\",\n        \"0 0 0px rgba(57, 255, 20, 0)\"\n      ],\n      transition: {\n        duration: 2,\n        repeat: Infinity,\n        repeatType: \"loop\"\n      }\n    }\n  };\n\n  // Disable confetti after a few seconds\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      setShowConfetti(false);\n    }, 5000);\n\n    return () => clearTimeout(timer);\n  }, []);\n\n  return (\n    <main className=\"min-h-screen bg-black relative overflow-hidden flex items-center justify-center py-12\">\n      {/* Animated Gradient Background */}\n      <div\n        className=\"absolute inset-0 z-0\"\n        style={{\n          background: \"radial-gradient(circle at 20% 20%, rgba(0, 255, 255, 0.03), transparent 33%), \" +\n                    \"radial-gradient(circle at 80% 80%, rgba(57, 255, 20, 0.03), transparent 33%), \" +\n                    \"radial-gradient(circle at 50% 50%, rgba(255, 0, 255, 0.03), rgba(0, 0, 0, 1) 100%)\",\n        }}\n      ></div>\n\n      {/* Neon Grid Overlay */}\n      <div className=\"absolute inset-0 z-0 opacity-[0.03]\"\n           style={{\n             backgroundImage: \"linear-gradient(to right, #39FF14 1px, transparent 1px), linear-gradient(to bottom, #39FF14 1px, transparent 1px)\",\n             backgroundSize: \"40px 40px\"\n           }}>\n      </div>\n\n      {/* Confetti Animation */}\n      {showConfetti && (\n        <div className=\"absolute inset-0 pointer-events-none z-10\">\n          {[...Array(40)].map((_, i) => (\n            <motion.div\n              key={i}\n              className=\"absolute w-3 h-3 rounded-full\"\n              style={{\n                backgroundColor:\n                  i % 3 === 0 ? 'rgba(57, 255, 20, 0.7)' :\n                  i % 3 === 1 ? 'rgba(0, 255, 255, 0.7)' :\n                  'rgba(255, 0, 255, 0.7)',\n                top: `${Math.random() * -10}%`,\n                left: `${Math.random() * 100}%`,\n              }}\n              initial={{\n                y: -20,\n                opacity: 0,\n                scale: 0\n              }}\n              animate={{\n                y: `${100 + Math.random() * 50}vh`,\n                opacity: [1, 0.8, 0],\n                scale: [1, 0.8, 0.6],\n                rotate: [0, Math.random() * 360]\n              }}\n              transition={{\n                duration: 2.5 + Math.random() * 3.5,\n                delay: Math.random() * 3,\n                ease: \"easeOut\",\n                repeat: 1,\n                repeatType: \"loop\",\n                repeatDelay: Math.random() * 2\n              }}\n            />\n          ))}\n        </div>\n      )}\n\n      <motion.div\n        className=\"container mx-auto px-4 z-10 relative\"\n        variants={containerVariants}\n        initial=\"hidden\"\n        animate=\"visible\"\n      >\n        <div className=\"max-w-xl mx-auto\">\n          <motion.div\n            className=\"bg-black/30 backdrop-blur-sm rounded-2xl p-8 md:p-12 border border-lime-800/30\n                       shadow-[0_0_30px_rgba(57,255,20,0.2)]\"\n            initial={{ opacity: 0, scale: 0.9 }}\n            animate={{ opacity: 1, scale: 1 }}\n            transition={{ duration: 0.6 }}\n          >\n            <div className=\"text-center\">\n              {/* Success Icon */}\n              <motion.div\n                className=\"w-28 h-28 mx-auto mb-8 rounded-full bg-gradient-to-br from-lime-500/20 to-green-700/20\n                         flex items-center justify-center\"\n                initial={{ scale: 0 }}\n                animate={{ scale: 1 }}\n                transition={{\n                  type: \"spring\",\n                  stiffness: 200,\n                  damping: 15,\n                  delay: 0.2\n                }}\n              >\n                <motion.div\n                  className=\"w-24 h-24 rounded-full bg-gradient-to-br from-lime-500/30 to-green-700/30\n                           flex items-center justify-center\"\n                  variants={pulseVariants}\n                  initial=\"initial\"\n                  animate=\"animate\"\n                >\n                  <svg className=\"w-16 h-16 text-lime-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <motion.path\n                      strokeLinecap=\"round\"\n                      strokeLinejoin=\"round\"\n                      strokeWidth={2.5}\n                      d=\"M5 13l4 4L19 7\"\n                      initial={{ pathLength: 0 }}\n                      animate={{ pathLength: 1 }}\n                      transition={{ duration: 0.8, delay: 0.5 }}\n                    />\n                  </svg>\n                </motion.div>\n              </motion.div>\n\n              {/* Heading */}\n              <motion.h1\n                className=\"font-playfair text-3xl md:text-4xl font-bold mb-3 text-white\"\n                variants={itemVariants}\n              >\n                Thank you for your order!\n              </motion.h1>\n\n              {/* Order ID */}\n              <motion.div\n                className=\"mb-6\"\n                variants={itemVariants}\n              >\n                <p className=\"text-gray-300 text-lg\">Order <span className=\"text-lime-400 font-medium\">#{orderId}</span> confirmed</p>\n              </motion.div>\n\n              {/* Order Summary Box */}\n              <motion.div\n                className=\"bg-black/50 rounded-xl p-6 mb-8 border border-gray-800\"\n                variants={itemVariants}\n              >\n                <div className=\"flex items-center justify-center mb-3\">\n                  <svg className=\"w-5 h-5 text-lime-400 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                  </svg>\n                  <h3 className=\"font-medium text-white\">Estimated Delivery/Pickup</h3>\n                </div>\n                <p className=\"text-lime-400 text-xl font-bold mb-1\">{deliveryTimeString}</p>\n                <p className=\"text-gray-400 text-sm\">\n                  Our chef is preparing your delicious meal\n                </p>\n              </motion.div>\n\n              {/* Countdown Timer and Track Order Section */}\n              <motion.div variants={itemVariants} className=\"space-y-4\">\n                {/* Countdown Timer */}\n                {countdown > 0 && !isRedirecting && (\n                  <motion.div\n                    className=\"bg-black/40 rounded-lg p-4 border border-lime-800/30\"\n                    initial={{ opacity: 0, y: 10 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ delay: 2 }}\n                  >\n                    <div className=\"flex items-center justify-center space-x-2 text-gray-300\">\n                      <svg className=\"w-4 h-4 text-lime-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                      </svg>\n                      <span className=\"text-sm\">\n                        Redirecting to order tracking in{' '}\n                        <motion.span\n                          className=\"text-lime-400 font-bold text-lg\"\n                          key={countdown}\n                          initial={{ scale: 1.2 }}\n                          animate={{ scale: 1 }}\n                          transition={{ duration: 0.3 }}\n                        >\n                          {countdown}\n                        </motion.span>\n                        {' '}second{countdown !== 1 ? 's' : ''}...\n                      </span>\n                    </div>\n                  </motion.div>\n                )}\n\n                {/* Redirecting State */}\n                {isRedirecting && (\n                  <motion.div\n                    className=\"bg-black/40 rounded-lg p-4 border border-lime-800/30\"\n                    initial={{ opacity: 0 }}\n                    animate={{ opacity: 1 }}\n                  >\n                    <div className=\"flex items-center justify-center space-x-2 text-lime-400\">\n                      <motion.svg\n                        className=\"w-4 h-4\"\n                        fill=\"none\"\n                        viewBox=\"0 0 24 24\"\n                        stroke=\"currentColor\"\n                        animate={{ rotate: 360 }}\n                        transition={{ duration: 1, repeat: Infinity, ease: \"linear\" }}\n                      >\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\" />\n                      </motion.svg>\n                      <span className=\"text-sm font-medium\">Redirecting to order tracking...</span>\n                    </div>\n                  </motion.div>\n                )}\n\n                {/* Track Order Now Button */}\n                <motion.button\n                  onClick={handleSkipCountdown}\n                  disabled={isRedirecting}\n                  className=\"relative overflow-hidden rounded-lg px-8 py-4 group w-full disabled:opacity-50 disabled:cursor-not-allowed\"\n", "suffix": "                  whileHover={!isRedirecting ? { scale: 1.03 } : {}}\n                  whileTap={!isRedirecting ? { scale: 0.97 } : {}}\n                  transition={{ type: \"spring\", stiffness: 400, damping: 17 }}\n                >\n                  {/* Button Background */}\n                  <span className=\"absolute inset-0 w-full h-full bg-gradient-to-r from-lime-700/70 to-lime-600/70\"></span>\n\n                  {/* Button Glow Effect */}\n                  <span className=\"absolute inset-0 w-full h-full transition-all duration-300\n                                  bg-gradient-to-r from-lime-600/50 via-lime-500/50 to-lime-600/50\n                                  opacity-0 group-hover:opacity-100 group-hover:blur-md\"></span>\n\n                  {/* Button Border */}\n                  <span className=\"absolute inset-0 w-full h-full border border-lime-500/50 rounded-lg\"></span>\n\n                  {/* Button Text */}\n                  <span className=\"relative z-10 flex items-center justify-center text-white font-medium text-lg\">\n                    <svg className=\"w-5 h-5 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2}\n                          d=\"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\" />\n                    </svg>\n                    {countdown > 0 && !isRedirecting ? 'Track Order Now' : 'Track Your Order'}\n                  </span>\n                </motion.button>\n              </motion.div>\n\n              {/* Return to Home */}\n              <motion.div\n                variants={itemVariants}\n                className=\"mt-6\"\n              >\n                <Link href=\"/\">\n                  <motion.button\n                    className=\"text-gray-400 hover:text-lime-400 transition-colors duration-300\"\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                  >\n                    Return to Home\n                  </motion.button>\n                </Link>\n              </motion.div>\n            </div>\n          </motion.div>\n        </div>\n      </motion.div>\n    </main>\n  );\n};\n\nexport default OrderConfirmation;", "path": "client/src/pages/OrderConfirmation.tsx", "language": "typescriptreact", "prefixBegin": 0, "suffixEnd": 33}}]]