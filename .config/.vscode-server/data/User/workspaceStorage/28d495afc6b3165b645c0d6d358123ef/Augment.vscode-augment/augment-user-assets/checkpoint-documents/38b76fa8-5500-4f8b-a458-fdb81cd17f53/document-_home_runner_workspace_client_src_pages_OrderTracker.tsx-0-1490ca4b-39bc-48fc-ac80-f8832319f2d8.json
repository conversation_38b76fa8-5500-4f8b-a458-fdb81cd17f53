{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/OrderTracker.tsx"}, "originalCode": "import { useState, useEffect } from \"react\";\nimport { useLocation, Link } from \"wouter\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport TimelineStep from \"@/components/TimelineStep\";\n\n// Order status types\nconst ORDER_STATUSES = [\"confirmed\", \"preparing\", \"cooking\", \"delivery\", \"delivered\"] as const;\ntype OrderStatus = typeof ORDER_STATUSES[number];\n\ninterface OrderTrackerProps {\n  params?: {\n    orderId?: string;\n  };\n}\n\nconst OrderTracker = (props: OrderTrackerProps) => {\n  const orderId = props.params?.orderId || \"BBC\" + Math.floor(Math.random() * 100000);\n  const [currentStatus, setCurrentStatus] = useState<OrderStatus>(\"confirmed\");\n  const [statusIndex, setStatusIndex] = useState(0);\n  const [showConfetti, setShowConfetti] = useState(false);\n  const [estimatedTime, setEstimatedTime] = useState(\"--:--\");\n  const [orderTime, setOrderTime] = useState(\"--:--\");\n  \n  // Status descriptions\n  const statusDetails = {\n    confirmed: {\n      title: \"Order Received\",\n      description: \"We've got your order and will start preparing it soon.\",\n      icon: (\n        <svg className=\"w-6 h-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n        </svg>\n      )\n    },\n    preparing: {\n      title: \"Preparing\",\n      description: \"Our chefs are gathering ingredients and preparing your meal.\",\n      icon: (\n        <svg className=\"w-6 h-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n        </svg>\n      )\n    },\n    cooking: {\n      title: \"Cooking\",\n      description: \"Your food is being cooked to perfection on our BBQ grills.\",\n      icon: (\n        <svg className=\"w-6 h-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17.657 18.657A8 8 0 016.343 7.343S7 9 9 10c0-2 .5-5 2.986-7C14 5 16.09 5.777 17.656 7.343A7.975 7.975 0 0120 13a7.975 7.975 0 01-2.343 5.657z\" />\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9.879 16.121A3 3 0 1012.015 11L11 14H9c0 .768.293 1.536.879 2.121z\" />\n        </svg>\n      )\n    },\n    delivery: {\n      title: \"Out for Delivery\",\n      description: \"Your order is on its way to your location.\",\n      icon: (\n        <svg className=\"w-6 h-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n          <path d=\"M9 17a2 2 0 11-4 0 2 2 0 014 0zM19 17a2 2 0 11-4 0 2 2 0 014 0z\" />\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5} d=\"M13 16V6a1 1 0 00-1-1H4a1 1 0 00-1 1v10a1 1 0 001 1h1m8-1a1 1 0 01-1 1H9m4-1V8a1 1 0 011-1h2.586a1 1 0 01.707.293l3.414 3.414a1 1 0 01.293.707V16a1 1 0 01-1 1h-1m-6-1a1 1 0 001 1h1M5 17a2 2 0 104 0m-4 0a2 2 0 114 0m6 0a2 2 0 104 0m-4 0a2 2 0 114 0\" />\n        </svg>\n      )\n    },\n    delivered: {\n      title: \"Delivered\",\n      description: \"Your order has been delivered. Enjoy your meal!\",\n      icon: (\n        <svg className=\"w-6 h-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\n        </svg>\n      )\n    }\n  };\n  \n  // Mock times for each step\n  const statusTimes = {\n    confirmed: \"19:30\",\n    preparing: \"19:35\",\n    cooking: \"19:45\",\n    delivery: \"20:00\",\n    delivered: \"20:15\"\n  };\n  \n  // Animation variants\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1\n      }\n    }\n  };\n  \n  const itemVariants = {\n    hidden: { y: 20, opacity: 0 },\n    visible: {\n      y: 0,\n      opacity: 1,\n      transition: {\n        type: \"spring\",\n        stiffness: 100,\n        damping: 10\n      }\n    }\n  };\n  \n  // Simulating progression through order statuses\n  useEffect(() => {\n    // Set initial times\n    const now = new Date();\n    const orderTimeFormatted = now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });\n    setOrderTime(orderTimeFormatted);\n    \n    const deliveryTime = new Date(now.getTime() + 45 * 60000);\n    const deliveryTimeFormatted = deliveryTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });\n    setEstimatedTime(deliveryTimeFormatted);\n    \n    // Simulate order status updates\n    const statusUpdateInterval = setInterval(() => {\n      setStatusIndex(prevIndex => {\n        // If we're at the last status, clear the interval and show confetti\n        if (prevIndex >= ORDER_STATUSES.length - 1) {\n          clearInterval(statusUpdateInterval);\n          setShowConfetti(true);\n          return prevIndex;\n        }\n        \n        // Otherwise, progress to the next status\n        const nextIndex = prevIndex + 1;\n        setCurrentStatus(ORDER_STATUSES[nextIndex]);\n        return nextIndex;\n      });\n    }, 10000); // Update status every 10 seconds for demo\n    \n    return () => clearInterval(statusUpdateInterval);\n  }, []);\n  \n  return (\n    <main className=\"min-h-screen bg-black relative overflow-hidden py-12\">\n      {/* Animated Gradient Background */}\n      <div \n        className=\"absolute inset-0 z-0\"\n        style={{\n          background: \"radial-gradient(circle at 20% 80%, rgba(0, 255, 255, 0.03), transparent 33%), \" +\n                    \"radial-gradient(circle at 80% 20%, rgba(57, 255, 20, 0.03), transparent 33%), \" +\n                    \"radial-gradient(circle at 50% 50%, rgba(255, 0, 255, 0.03), rgba(0, 0, 0, 1) 100%)\",\n        }}\n      ></div>\n      \n      {/* Neon Grid Overlay */}\n      <div className=\"absolute inset-0 z-0 opacity-[0.02]\" \n           style={{ \n             backgroundImage: \"linear-gradient(to right, #00FFFF 1px, transparent 1px), linear-gradient(to bottom, #00FFFF 1px, transparent 1px)\", \n             backgroundSize: \"40px 40px\" \n           }}>\n      </div>\n      \n      {/* Confetti for delivered state */}\n      {showConfetti && (\n        <div className=\"absolute inset-0 pointer-events-none z-10\">\n          {[...Array(40)].map((_, i) => (\n            <motion.div \n              key={i}\n              className=\"absolute w-3 h-3 rounded-full\"\n              style={{ \n                backgroundColor: \n                  i % 3 === 0 ? 'rgba(57, 255, 20, 0.7)' : \n                  i % 3 === 1 ? 'rgba(0, 255, 255, 0.7)' : \n                  'rgba(255, 0, 255, 0.7)',\n                top: `${Math.random() * -10}%`,\n                left: `${Math.random() * 100}%`,\n              }}\n              initial={{ \n                y: -20, \n                opacity: 0,\n                scale: 0 \n              }}\n              animate={{ \n                y: `${100 + Math.random() * 50}vh`,\n                opacity: [1, 0.8, 0],\n                scale: [1, 0.8, 0.6],\n                rotate: [0, Math.random() * 360]\n              }}\n              transition={{ \n                duration: 2.5 + Math.random() * 3.5,\n                delay: Math.random() * 2,\n                ease: \"easeOut\",\n                repeat: 1,\n                repeatType: \"loop\",\n                repeatDelay: Math.random() * 2\n              }}\n            />\n          ))}\n        </div>\n      )}\n      \n      <div className=\"container mx-auto px-4 z-10 relative\">\n        <motion.div \n          className=\"max-w-4xl mx-auto\"\n          variants={containerVariants}\n          initial=\"hidden\"\n          animate=\"visible\"\n        >\n          {/* Header Section */}\n          <motion.div variants={itemVariants} className=\"mb-10 text-center\">\n            <h1 className=\"font-playfair text-3xl md:text-4xl font-bold text-white mb-2\">\n              Track Your Order\n            </h1>\n            \n            <div className=\"relative inline-block\">\n              <span className=\"block h-0.5 bg-gradient-to-r from-cyan-500 via-fuchsia-500 to-lime-500 w-full\"></span>\n              <motion.span \n                className=\"block h-0.5 bg-cyan-400 absolute inset-0\"\n                animate={{\n                  left: [\"0%\", \"100%\", \"0%\"],\n                  width: [\"0%\", \"50%\", \"0%\"]\n                }}\n                transition={{ duration: 3, repeat: Infinity }}\n              ></motion.span>\n            </div>\n            \n            <div className=\"mt-4 space-y-1\">\n              <p className=\"text-gray-300\">\n                Order <span className=\"text-cyan-400 font-medium\">#{orderId}</span> \n              </p>\n              <p className=\"text-gray-500 text-sm\">\n                Placed at {orderTime} • Estimated delivery: {estimatedTime}\n              </p>\n              <p className=\"text-xl font-medium mt-3\">\n                <span className={`\n                  ${currentStatus === 'confirmed' ? 'text-lime-400' : ''}\n                  ${currentStatus === 'preparing' ? 'text-yellow-400' : ''}\n                  ${currentStatus === 'cooking' ? 'text-orange-400' : ''}\n                  ${currentStatus === 'delivery' ? 'text-cyan-400' : ''}\n                  ${currentStatus === 'delivered' ? 'text-green-400' : ''}\n                `}>\n                  {statusDetails[currentStatus].title}\n                </span>\n              </p>\n              <p className=\"text-gray-400 text-sm max-w-lg mx-auto\">\n                {statusDetails[currentStatus].description}\n              </p>\n            </div>\n          </motion.div>\n          \n          {/* Main Content */}\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            {/* Timeline Section */}\n            <motion.div \n              variants={itemVariants}\n              className=\"md:col-span-2 bg-black/30 backdrop-blur-sm rounded-xl p-6 md:p-8 \n                        border border-gray-800 shadow-[0_4px_20px_rgba(0,0,0,0.4)]\"\n            >\n              <h2 className=\"font-playfair text-xl font-bold text-white mb-6 flex items-center\">\n                <svg className=\"w-5 h-5 mr-2 text-cyan-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\" />\n                </svg>\n                Order Progress\n              </h2>\n              \n              {/* Progress Bar */}\n              <div className=\"relative h-2 bg-gray-800 rounded-full mb-12 mt-6 overflow-hidden\">\n                <motion.div \n                  className=\"absolute h-full bg-gradient-to-r from-lime-400 via-cyan-500 to-fuchsia-500 rounded-full\"\n                  initial={{ width: \"0%\" }}\n                  animate={{ width: `${(statusIndex / (ORDER_STATUSES.length - 1)) * 100}%` }}\n                  transition={{ duration: 1, ease: \"easeInOut\" }}\n                />\n                \n                {/* Animated highlight */}\n                <motion.div \n                  className=\"absolute h-full w-20 bg-white/20\"\n                  animate={{ \n                    x: [\"-100%\", \"400%\"] \n                  }}\n                  transition={{ \n                    duration: 2,\n                    repeat: Infinity,\n                    ease: \"linear\"\n                  }}\n                />\n              </div>\n              \n              {/* Timeline Steps */}\n              <div className=\"space-y-8\">\n                {ORDER_STATUSES.map((status, index) => {\n                  const isActive = index === statusIndex;\n                  const isCompleted = index < statusIndex;\n                  \n                  return (\n                    <div key={status} className=\"relative\">\n                      {/* Connector Line */}\n                      {index < ORDER_STATUSES.length - 1 && (\n                        <div className=\"absolute left-6 top-12 bottom-0 w-0.5 bg-gray-800 -z-10\">\n                          {isCompleted && (\n                            <motion.div \n                              className=\"absolute inset-0 bg-gradient-to-b from-lime-500 to-lime-500/30\"\n                              initial={{ height: \"0%\" }}\n                              animate={{ height: \"100%\" }}\n                              transition={{ duration: 0.5 }}\n                            />\n                          )}\n                        </div>\n                      )}\n                      \n                      <TimelineStep \n                        icon={statusDetails[status].icon}\n                        title={statusDetails[status].title}\n                        time={statusTimes[status]}\n                        isActive={isActive}\n                        isCompleted={isCompleted}\n                        index={index}\n                      />\n                    </div>\n                  );\n                })}\n              </div>\n            </motion.div>\n            \n            {/* Order Summary Section */}\n            <motion.div \n              variants={itemVariants}\n              className=\"bg-black/30 backdrop-blur-sm rounded-xl p-6 md:p-8 \n                        border border-gray-800 shadow-[0_4px_20px_rgba(0,0,0,0.4)]\n                        h-fit sticky top-24\"\n            >\n              <h2 className=\"font-playfair text-xl font-bold text-white mb-6 flex items-center\">\n                <svg className=\"w-5 h-5 mr-2 text-lime-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z\" />\n                </svg>\n                Order Summary\n              </h2>\n              \n              <div className=\"space-y-4 mb-6 border-b border-gray-800 pb-4\">\n                <div className=\"flex justify-between text-sm\">\n                  <span className=\"text-gray-400\">Order ID:</span>\n                  <span className=\"text-white font-medium\">{orderId}</span>\n                </div>\n                <div className=\"flex justify-between text-sm\">\n                  <span className=\"text-gray-400\">Order Time:</span>\n                  <span className=\"text-white\">{orderTime}</span>\n                </div>\n                <div className=\"flex justify-between text-sm\">\n                  <span className=\"text-gray-400\">Estimated Delivery:</span>\n                  <span className=\"text-white\">{estimatedTime}</span>\n                </div>\n                <div className=\"flex justify-between text-sm\">\n                  <span className=\"text-gray-400\">Payment Method:</span>\n                  <span className=\"text-white\">Credit Card</span>\n                </div>\n              </div>\n              \n              {/* CTA Buttons */}\n              <div className=\"space-y-3\">\n                <Link href=\"/\">\n                  <motion.button\n                    className=\"relative overflow-hidden rounded-lg px-4 py-3 w-full group\"\n                    whileHover={{ scale: 1.02 }}\n                    whileTap={{ scale: 0.98 }}\n                    transition={{ type: \"spring\", stiffness: 400, damping: 17 }}\n                  >\n                    {/* Button Background */}\n                    <span className=\"absolute inset-0 w-full h-full bg-gradient-to-r from-cyan-800/70 to-cyan-600/70\"></span>\n                    \n                    {/* Button Glow Effect */}\n                    <span className=\"absolute inset-0 w-full h-full transition-all duration-300 \n                                    bg-gradient-to-r from-cyan-600/50 via-cyan-500/50 to-cyan-600/50\n                                    opacity-0 group-hover:opacity-100 group-hover:blur-sm\"></span>\n                    \n                    {/* Button Border */}\n                    <span className=\"absolute inset-0 w-full h-full border border-cyan-500/50 rounded-lg\"></span>\n                    \n                    {/* Button Text */}\n                    <span className=\"relative z-10 flex items-center justify-center text-white font-medium\">\n                      <svg className=\"w-5 h-5 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6\" />\n                      </svg>\n                      Return to Home\n                    </span>\n                  </motion.button>\n                </Link>\n                \n                <Link href=\"/menu\">\n                  <motion.button\n                    className=\"relative overflow-hidden rounded-lg px-4 py-3 w-full group\"\n                    whileHover={{ scale: 1.02 }}\n                    whileTap={{ scale: 0.98 }}\n                    transition={{ type: \"spring\", stiffness: 400, damping: 17 }}\n                  >\n                    {/* Button Background */}\n                    <span className=\"absolute inset-0 w-full h-full bg-gradient-to-r from-gray-800/70 to-gray-700/70\"></span>\n                    \n                    {/* Button Border */}\n                    <span className=\"absolute inset-0 w-full h-full border border-gray-600/50 rounded-lg\"></span>\n                    \n                    {/* Button Text */}\n                    <span className=\"relative z-10 flex items-center justify-center text-white font-medium\">\n                      <svg className=\"w-5 h-5 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253\" />\n                      </svg>\n                      Order Again\n                    </span>\n                  </motion.button>\n                </Link>\n              </div>\n              \n              {/* Help text */}\n              <p className=\"text-gray-500 text-xs text-center mt-4\">\n                Need help with your order? <br />\n                Contact us at <span className=\"text-cyan-400\"><EMAIL></span>\n              </p>\n            </motion.div>\n          </div>\n        </motion.div>\n      </div>\n    </main>\n  );\n};\n\nexport default OrderTracker;", "modifiedCode": "import { useState, useEffect } from \"react\";\nimport { useLocation, Link } from \"wouter\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport TimelineStep from \"@/components/TimelineStep\";\n\n// Order status types\nconst ORDER_STATUSES = [\"confirmed\", \"preparing\", \"cooking\", \"delivery\", \"delivered\"] as const;\ntype OrderStatus = typeof ORDER_STATUSES[number];\n\ninterface OrderTrackerProps {\n  params?: {\n    orderId?: string;\n  };\n}\n\nconst OrderTracker = (props: OrderTrackerProps) => {\n  const orderId = props.params?.orderId || \"BBC\" + Math.floor(Math.random() * 100000);\n  const [currentStatus, setCurrentStatus] = useState<OrderStatus>(\"confirmed\");\n  const [statusIndex, setStatusIndex] = useState(0);\n  const [showConfetti, setShowConfetti] = useState(false);\n  const [estimatedTime, setEstimatedTime] = useState(\"--:--\");\n  const [orderTime, setOrderTime] = useState(\"--:--\");\n  \n  // Status descriptions\n  const statusDetails = {\n    confirmed: {\n      title: \"Order Received\",\n      description: \"We've got your order and will start preparing it soon.\",\n      icon: (\n        <svg className=\"w-6 h-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n        </svg>\n      )\n    },\n    preparing: {\n      title: \"Preparing\",\n      description: \"Our chefs are gathering ingredients and preparing your meal.\",\n      icon: (\n        <svg className=\"w-6 h-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n        </svg>\n      )\n    },\n    cooking: {\n      title: \"Cooking\",\n      description: \"Your food is being cooked to perfection on our BBQ grills.\",\n      icon: (\n        <svg className=\"w-6 h-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17.657 18.657A8 8 0 016.343 7.343S7 9 9 10c0-2 .5-5 2.986-7C14 5 16.09 5.777 17.656 7.343A7.975 7.975 0 0120 13a7.975 7.975 0 01-2.343 5.657z\" />\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9.879 16.121A3 3 0 1012.015 11L11 14H9c0 .768.293 1.536.879 2.121z\" />\n        </svg>\n      )\n    },\n    delivery: {\n      title: \"Out for Delivery\",\n      description: \"Your order is on its way to your location.\",\n      icon: (\n        <svg className=\"w-6 h-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n          <path d=\"M9 17a2 2 0 11-4 0 2 2 0 014 0zM19 17a2 2 0 11-4 0 2 2 0 014 0z\" />\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5} d=\"M13 16V6a1 1 0 00-1-1H4a1 1 0 00-1 1v10a1 1 0 001 1h1m8-1a1 1 0 01-1 1H9m4-1V8a1 1 0 011-1h2.586a1 1 0 01.707.293l3.414 3.414a1 1 0 01.293.707V16a1 1 0 01-1 1h-1m-6-1a1 1 0 001 1h1M5 17a2 2 0 104 0m-4 0a2 2 0 114 0m6 0a2 2 0 104 0m-4 0a2 2 0 114 0\" />\n        </svg>\n      )\n    },\n    delivered: {\n      title: \"Delivered\",\n      description: \"Your order has been delivered. Enjoy your meal!\",\n      icon: (\n        <svg className=\"w-6 h-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\n        </svg>\n      )\n    }\n  };\n  \n  // Mock times for each step\n  const statusTimes = {\n    confirmed: \"19:30\",\n    preparing: \"19:35\",\n    cooking: \"19:45\",\n    delivery: \"20:00\",\n    delivered: \"20:15\"\n  };\n  \n  // Animation variants\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1\n      }\n    }\n  };\n  \n  const itemVariants = {\n    hidden: { y: 20, opacity: 0 },\n    visible: {\n      y: 0,\n      opacity: 1,\n      transition: {\n        type: \"spring\",\n        stiffness: 100,\n        damping: 10\n      }\n    }\n  };\n  \n  // Simulating progression through order statuses\n  useEffect(() => {\n    // Set initial times\n    const now = new Date();\n    const orderTimeFormatted = now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });\n    setOrderTime(orderTimeFormatted);\n    \n    const deliveryTime = new Date(now.getTime() + 45 * 60000);\n    const deliveryTimeFormatted = deliveryTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });\n    setEstimatedTime(deliveryTimeFormatted);\n    \n    // Simulate order status updates\n    const statusUpdateInterval = setInterval(() => {\n      setStatusIndex(prevIndex => {\n        // If we're at the last status, clear the interval and show confetti\n        if (prevIndex >= ORDER_STATUSES.length - 1) {\n          clearInterval(statusUpdateInterval);\n          setShowConfetti(true);\n          return prevIndex;\n        }\n        \n        // Otherwise, progress to the next status\n        const nextIndex = prevIndex + 1;\n        setCurrentStatus(ORDER_STATUSES[nextIndex]);\n        return nextIndex;\n      });\n    }, 10000); // Update status every 10 seconds for demo\n    \n    return () => clearInterval(statusUpdateInterval);\n  }, []);\n  \n  return (\n    <main className=\"min-h-screen bg-black relative overflow-hidden py-12\">\n      {/* Animated Gradient Background */}\n      <div \n        className=\"absolute inset-0 z-0\"\n        style={{\n          background: \"radial-gradient(circle at 20% 80%, rgba(0, 255, 255, 0.03), transparent 33%), \" +\n                    \"radial-gradient(circle at 80% 20%, rgba(57, 255, 20, 0.03), transparent 33%), \" +\n                    \"radial-gradient(circle at 50% 50%, rgba(255, 0, 255, 0.03), rgba(0, 0, 0, 1) 100%)\",\n        }}\n      ></div>\n      \n      {/* Neon Grid Overlay */}\n      <div className=\"absolute inset-0 z-0 opacity-[0.02]\" \n           style={{ \n             backgroundImage: \"linear-gradient(to right, #00FFFF 1px, transparent 1px), linear-gradient(to bottom, #00FFFF 1px, transparent 1px)\", \n             backgroundSize: \"40px 40px\" \n           }}>\n      </div>\n      \n      {/* Confetti for delivered state */}\n      {showConfetti && (\n        <div className=\"absolute inset-0 pointer-events-none z-10\">\n          {[...Array(40)].map((_, i) => (\n            <motion.div \n              key={i}\n              className=\"absolute w-3 h-3 rounded-full\"\n              style={{ \n                backgroundColor: \n                  i % 3 === 0 ? 'rgba(57, 255, 20, 0.7)' : \n                  i % 3 === 1 ? 'rgba(0, 255, 255, 0.7)' : \n                  'rgba(255, 0, 255, 0.7)',\n                top: `${Math.random() * -10}%`,\n                left: `${Math.random() * 100}%`,\n              }}\n              initial={{ \n                y: -20, \n                opacity: 0,\n                scale: 0 \n              }}\n              animate={{ \n                y: `${100 + Math.random() * 50}vh`,\n                opacity: [1, 0.8, 0],\n                scale: [1, 0.8, 0.6],\n                rotate: [0, Math.random() * 360]\n              }}\n              transition={{ \n                duration: 2.5 + Math.random() * 3.5,\n                delay: Math.random() * 2,\n                ease: \"easeOut\",\n                repeat: 1,\n                repeatType: \"loop\",\n                repeatDelay: Math.random() * 2\n              }}\n            />\n          ))}\n        </div>\n      )}\n      \n      <div className=\"container mx-auto px-4 z-10 relative\">\n        <motion.div \n          className=\"max-w-4xl mx-auto\"\n          variants={containerVariants}\n          initial=\"hidden\"\n          animate=\"visible\"\n        >\n          {/* Header Section */}\n          <motion.div variants={itemVariants} className=\"mb-10 text-center\">\n            <h1 className=\"font-playfair text-3xl md:text-4xl font-bold text-white mb-2\">\n              Track Your Order\n            </h1>\n            \n            <div className=\"relative inline-block\">\n              <span className=\"block h-0.5 bg-gradient-to-r from-cyan-500 via-fuchsia-500 to-lime-500 w-full\"></span>\n              <motion.span \n                className=\"block h-0.5 bg-cyan-400 absolute inset-0\"\n                animate={{\n                  left: [\"0%\", \"100%\", \"0%\"],\n                  width: [\"0%\", \"50%\", \"0%\"]\n                }}\n                transition={{ duration: 3, repeat: Infinity }}\n              ></motion.span>\n            </div>\n            \n            <div className=\"mt-4 space-y-1\">\n              <p className=\"text-gray-300\">\n                Order <span className=\"text-cyan-400 font-medium\">#{orderId}</span> \n              </p>\n              <p className=\"text-gray-500 text-sm\">\n                Placed at {orderTime} • Estimated delivery: {estimatedTime}\n              </p>\n              <p className=\"text-xl font-medium mt-3\">\n                <span className={`\n                  ${currentStatus === 'confirmed' ? 'text-lime-400' : ''}\n                  ${currentStatus === 'preparing' ? 'text-yellow-400' : ''}\n                  ${currentStatus === 'cooking' ? 'text-orange-400' : ''}\n                  ${currentStatus === 'delivery' ? 'text-cyan-400' : ''}\n                  ${currentStatus === 'delivered' ? 'text-green-400' : ''}\n                `}>\n                  {statusDetails[currentStatus].title}\n                </span>\n              </p>\n              <p className=\"text-gray-400 text-sm max-w-lg mx-auto\">\n                {statusDetails[currentStatus].description}\n              </p>\n            </div>\n          </motion.div>\n          \n          {/* Main Content */}\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            {/* Timeline Section */}\n            <motion.div \n              variants={itemVariants}\n              className=\"md:col-span-2 bg-black/30 backdrop-blur-sm rounded-xl p-6 md:p-8 \n                        border border-gray-800 shadow-[0_4px_20px_rgba(0,0,0,0.4)]\"\n            >\n              <h2 className=\"font-playfair text-xl font-bold text-white mb-6 flex items-center\">\n                <svg className=\"w-5 h-5 mr-2 text-cyan-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\" />\n                </svg>\n                Order Progress\n              </h2>\n              \n              {/* Progress Bar */}\n              <div className=\"relative h-2 bg-gray-800 rounded-full mb-12 mt-6 overflow-hidden\">\n                <motion.div \n                  className=\"absolute h-full bg-gradient-to-r from-lime-400 via-cyan-500 to-fuchsia-500 rounded-full\"\n                  initial={{ width: \"0%\" }}\n                  animate={{ width: `${(statusIndex / (ORDER_STATUSES.length - 1)) * 100}%` }}\n                  transition={{ duration: 1, ease: \"easeInOut\" }}\n                />\n                \n                {/* Animated highlight */}\n                <motion.div \n                  className=\"absolute h-full w-20 bg-white/20\"\n                  animate={{ \n                    x: [\"-100%\", \"400%\"] \n                  }}\n                  transition={{ \n                    duration: 2,\n                    repeat: Infinity,\n                    ease: \"linear\"\n                  }}\n                />\n              </div>\n              \n              {/* Timeline Steps */}\n              <div className=\"space-y-8\">\n                {ORDER_STATUSES.map((status, index) => {\n                  const isActive = index === statusIndex;\n                  const isCompleted = index < statusIndex;\n                  \n                  return (\n                    <div key={status} className=\"relative\">\n                      {/* Connector Line */}\n                      {index < ORDER_STATUSES.length - 1 && (\n                        <div className=\"absolute left-6 top-12 bottom-0 w-0.5 bg-gray-800 -z-10\">\n                          {isCompleted && (\n                            <motion.div \n                              className=\"absolute inset-0 bg-gradient-to-b from-lime-500 to-lime-500/30\"\n                              initial={{ height: \"0%\" }}\n                              animate={{ height: \"100%\" }}\n                              transition={{ duration: 0.5 }}\n                            />\n                          )}\n                        </div>\n                      )}\n                      \n                      <TimelineStep \n                        icon={statusDetails[status].icon}\n                        title={statusDetails[status].title}\n                        time={statusTimes[status]}\n                        isActive={isActive}\n                        isCompleted={isCompleted}\n                        index={index}\n                      />\n                    </div>\n                  );\n                })}\n              </div>\n            </motion.div>\n            \n            {/* Order Summary Section */}\n            <motion.div \n              variants={itemVariants}\n              className=\"bg-black/30 backdrop-blur-sm rounded-xl p-6 md:p-8 \n                        border border-gray-800 shadow-[0_4px_20px_rgba(0,0,0,0.4)]\n                        h-fit sticky top-24\"\n            >\n              <h2 className=\"font-playfair text-xl font-bold text-white mb-6 flex items-center\">\n                <svg className=\"w-5 h-5 mr-2 text-lime-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z\" />\n                </svg>\n                Order Summary\n              </h2>\n              \n              <div className=\"space-y-4 mb-6 border-b border-gray-800 pb-4\">\n                <div className=\"flex justify-between text-sm\">\n                  <span className=\"text-gray-400\">Order ID:</span>\n                  <span className=\"text-white font-medium\">{orderId}</span>\n                </div>\n                <div className=\"flex justify-between text-sm\">\n                  <span className=\"text-gray-400\">Order Time:</span>\n                  <span className=\"text-white\">{orderTime}</span>\n                </div>\n                <div className=\"flex justify-between text-sm\">\n                  <span className=\"text-gray-400\">Estimated Delivery:</span>\n                  <span className=\"text-white\">{estimatedTime}</span>\n                </div>\n                <div className=\"flex justify-between text-sm\">\n                  <span className=\"text-gray-400\">Payment Method:</span>\n                  <span className=\"text-white\">Credit Card</span>\n                </div>\n              </div>\n              \n              {/* CTA Buttons */}\n              <div className=\"space-y-3\">\n                <Link href=\"/\">\n                  <motion.button\n                    className=\"relative overflow-hidden rounded-lg px-4 py-3 w-full group\"\n                    whileHover={{ scale: 1.02 }}\n                    whileTap={{ scale: 0.98 }}\n                    transition={{ type: \"spring\", stiffness: 400, damping: 17 }}\n                  >\n                    {/* Button Background */}\n                    <span className=\"absolute inset-0 w-full h-full bg-gradient-to-r from-cyan-800/70 to-cyan-600/70\"></span>\n                    \n                    {/* Button Glow Effect */}\n                    <span className=\"absolute inset-0 w-full h-full transition-all duration-300 \n                                    bg-gradient-to-r from-cyan-600/50 via-cyan-500/50 to-cyan-600/50\n                                    opacity-0 group-hover:opacity-100 group-hover:blur-sm\"></span>\n                    \n                    {/* Button Border */}\n                    <span className=\"absolute inset-0 w-full h-full border border-cyan-500/50 rounded-lg\"></span>\n                    \n                    {/* Button Text */}\n                    <span className=\"relative z-10 flex items-center justify-center text-white font-medium\">\n                      <svg className=\"w-5 h-5 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6\" />\n                      </svg>\n                      Return to Home\n                    </span>\n                  </motion.button>\n                </Link>\n                \n                <Link href=\"/menu\">\n                  <motion.button\n                    className=\"relative overflow-hidden rounded-lg px-4 py-3 w-full group\"\n                    whileHover={{ scale: 1.02 }}\n                    whileTap={{ scale: 0.98 }}\n                    transition={{ type: \"spring\", stiffness: 400, damping: 17 }}\n                  >\n                    {/* Button Background */}\n                    <span className=\"absolute inset-0 w-full h-full bg-gradient-to-r from-gray-800/70 to-gray-700/70\"></span>\n                    \n                    {/* Button Border */}\n                    <span className=\"absolute inset-0 w-full h-full border border-gray-600/50 rounded-lg\"></span>\n                    \n                    {/* Button Text */}\n                    <span className=\"relative z-10 flex items-center justify-center text-white font-medium\">\n                      <svg className=\"w-5 h-5 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253\" />\n                      </svg>\n                      Order Again\n                    </span>\n                  </motion.button>\n                </Link>\n              </div>\n              \n              {/* Help text */}\n              <p className=\"text-gray-500 text-xs text-center mt-4\">\n                Need help with your order? <br />\n                Contact us at <span className=\"text-cyan-400\"><EMAIL></span>\n              </p>\n            </motion.div>\n          </div>\n        </motion.div>\n      </div>\n    </main>\n  );\n};\n\nexport default OrderTracker;"}