{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/OrderLookup.tsx"}, "modifiedCode": "import { useState } from \"react\";\nimport { useLocation } from \"wouter\";\nimport { motion } from \"framer-motion\";\nimport Button from \"@/components/Button\";\n\nconst OrderLookup = () => {\n  const [, setLocation] = useLocation();\n  const [orderId, setOrderId] = useState(\"\");\n  const [isSearching, setIsSearching] = useState(false);\n\n  const handleSearch = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!orderId.trim()) {\n      return;\n    }\n\n    setIsSearching(true);\n    \n    // Validate order ID format (should be a number)\n    const numericOrderId = parseInt(orderId.trim());\n    if (isNaN(numericOrderId)) {\n      alert(\"Please enter a valid order ID (numbers only)\");\n      setIsSearching(false);\n      return;\n    }\n\n    // Check if order exists\n    try {\n      const response = await fetch(`/api/orders/${numericOrderId}`);\n      if (response.ok) {\n        // Order exists, redirect to tracking page\n        setLocation(`/track-order/${numericOrderId}`);\n      } else {\n        alert(\"Order not found. Please check your order ID and try again.\");\n        setIsSearching(false);\n      }\n    } catch (error) {\n      console.error(\"Error checking order:\", error);\n      alert(\"Error checking order. Please try again.\");\n      setIsSearching(false);\n    }\n  };\n\n  return (\n    <main className=\"min-h-screen bg-black relative overflow-hidden flex items-center justify-center py-12\">\n      {/* Animated Gradient Background */}\n      <div \n        className=\"absolute inset-0 z-0\"\n        style={{\n          background: \"radial-gradient(circle at 20% 20%, rgba(0, 255, 255, 0.03), transparent 33%), \" +\n                    \"radial-gradient(circle at 80% 80%, rgba(57, 255, 20, 0.03), transparent 33%), \" +\n                    \"radial-gradient(circle at 50% 50%, rgba(255, 0, 255, 0.03), rgba(0, 0, 0, 1) 100%)\",\n        }}\n      ></div>\n      \n      {/* Neon Grid Overlay */}\n      <div className=\"absolute inset-0 z-0 opacity-[0.03]\" \n           style={{ \n             backgroundImage: \"linear-gradient(to right, #39FF14 1px, transparent 1px), linear-gradient(to bottom, #39FF14 1px, transparent 1px)\", \n             backgroundSize: \"40px 40px\" \n           }}>\n      </div>\n\n      <div className=\"container mx-auto px-4 z-10 relative max-w-md\">\n        <motion.div \n          className=\"bg-black/30 backdrop-blur-sm rounded-2xl p-8 border border-lime-800/30\n                     shadow-[0_0_30px_rgba(57,255,20,0.2)]\"\n          initial={{ opacity: 0, scale: 0.9 }}\n          animate={{ opacity: 1, scale: 1 }}\n          transition={{ duration: 0.6 }}\n        >\n          <div className=\"text-center mb-8\">\n            <motion.div \n              className=\"w-16 h-16 mx-auto mb-6 rounded-full bg-gradient-to-br from-lime-500/20 to-green-700/20 \n                         flex items-center justify-center\"\n              initial={{ scale: 0 }}\n              animate={{ scale: 1 }}\n              transition={{ \n                type: \"spring\",\n                stiffness: 200,\n                damping: 15,\n                delay: 0.2\n              }}\n            >\n              <svg className=\"w-8 h-8 text-lime-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n              </svg>\n            </motion.div>\n            \n            <h1 className=\"font-playfair text-2xl md:text-3xl font-bold mb-4 text-white\">\n              Track Your Order\n            </h1>\n            \n            <p className=\"text-gray-300 mb-6\">\n              Enter your order ID to track your delicious BBQ order\n            </p>\n          </div>\n\n          <form onSubmit={handleSearch} className=\"space-y-6\">\n            <div>\n              <label htmlFor=\"orderId\" className=\"block text-sm font-medium text-gray-300 mb-2\">\n                Order ID\n              </label>\n              <input\n                type=\"text\"\n                id=\"orderId\"\n                value={orderId}\n                onChange={(e) => setOrderId(e.target.value)}\n                placeholder=\"Enter your order ID (e.g., 42)\"\n                className=\"w-full px-4 py-3 bg-black/50 border border-gray-700 rounded-lg \n                         text-white placeholder-gray-500 focus:outline-none focus:border-lime-500\n                         focus:ring-1 focus:ring-lime-500 transition-colors\"\n                disabled={isSearching}\n                required\n              />\n            </div>\n\n            <Button\n              type=\"submit\"\n              disabled={isSearching || !orderId.trim()}\n              className=\"w-full\"\n            >\n              {isSearching ? (\n                <div className=\"flex items-center justify-center space-x-2\">\n                  <motion.div\n                    className=\"w-4 h-4 border-2 border-white border-t-transparent rounded-full\"\n                    animate={{ rotate: 360 }}\n                    transition={{ duration: 1, repeat: Infinity, ease: \"linear\" }}\n                  />\n                  <span>Searching...</span>\n                </div>\n              ) : (\n                <div className=\"flex items-center justify-center space-x-2\">\n                  <svg className=\"w-5 h-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\" />\n                  </svg>\n                  <span>Track Order</span>\n                </div>\n              )}\n            </Button>\n          </form>\n\n          <div className=\"mt-6 text-center\">\n            <a \n              href=\"/\" \n              className=\"text-gray-400 hover:text-lime-400 transition-colors duration-300 text-sm\"\n            >\n              ← Return to Home\n            </a>\n          </div>\n        </motion.div>\n      </div>\n    </main>\n  );\n};\n\nexport default OrderLookup;\n"}