{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "server/create-missing-tables.ts"}, "originalCode": "import { db } from \"./db\";\n\nasync function createMissingTables() {\n  try {\n    console.log(\"Creating missing database tables...\");\n\n    // Create contact_messages table\n    try {\n      await db.execute(`\n        CREATE TABLE IF NOT EXISTS contact_messages (\n          id SERIAL PRIMARY KEY,\n          name TEXT NOT NULL,\n          email TEXT NOT NULL,\n          subject TEXT NOT NULL,\n          message TEXT NOT NULL,\n          created_at TIMESTAMP DEFAULT NOW()\n        )\n      `);\n      console.log(\"✓ Created contact_messages table\");\n    } catch (error) {\n      console.log(`Error creating contact_messages: ${error.message}`);\n    }\n\n    // Create customization_groups table\n    try {\n      await db.execute(`\n        CREATE TABLE IF NOT EXISTS customization_groups (\n          id SERIAL PRIMARY KEY,\n          title TEXT NOT NULL\n        )\n      `);\n      console.log(\"✓ Created customization_groups table\");\n    } catch (error) {\n      console.log(`Error creating customization_groups: ${error.message}`);\n    }\n\n    // Create customization_options table\n    try {\n      await db.execute(`\n        CREATE TABLE IF NOT EXISTS customization_options (\n          id SERIAL PRIMARY KEY,\n          name TEXT NOT NULL,\n          extra_price INTEGER DEFAULT 0,\n          image_url TEXT NOT NULL DEFAULT '',\n          group_id INTEGER NOT NULL REFERENCES customization_groups(id)\n        )\n      `);\n      console.log(\"✓ Created customization_options table\");\n    } catch (error) {\n      console.log(`Error creating customization_options: ${error.message}`);\n    }\n\n    // Create item_customization_map table\n    try {\n      await db.execute(`\n        CREATE TABLE IF NOT EXISTS item_customization_map (\n          id SERIAL PRIMARY KEY,\n          item_id INTEGER NOT NULL REFERENCES menu_items(id),\n          option_id INTEGER NOT NULL REFERENCES customization_options(id)\n        )\n      `);\n      console.log(\"✓ Created item_customization_map table\");\n    } catch (error) {\n      console.log(`Error creating item_customization_map: ${error.message}`);\n    }\n\n    console.log(\"\\nAll missing tables created successfully!\");\n\n  } catch (error) {\n    console.error(\"Error creating tables:\", error);\n    throw error;\n  }\n}\n\n// Run if this file is executed directly\nif (import.meta.url === `file://${process.argv[1]}`) {\n  createMissingTables()\n    .then(() => {\n      console.log(\"Table creation completed!\");\n      process.exit(0);\n    })\n    .catch((error) => {\n      console.error(\"Table creation failed:\", error);\n      process.exit(1);\n    });\n}\n\nexport { createMissingTables };\n", "modifiedCode": "import { db } from \"./db\";\n\nasync function createMissingTables() {\n  try {\n    console.log(\"Creating missing database tables...\");\n\n    // Create contact_messages table\n    try {\n      await db.execute(`\n        CREATE TABLE IF NOT EXISTS contact_messages (\n          id SERIAL PRIMARY KEY,\n          name TEXT NOT NULL,\n          email TEXT NOT NULL,\n          subject TEXT NOT NULL,\n          message TEXT NOT NULL,\n          created_at TIMESTAMP DEFAULT NOW()\n        )\n      `);\n      console.log(\"✓ Created contact_messages table\");\n    } catch (error) {\n      console.log(`Error creating contact_messages: ${error.message}`);\n    }\n\n    // Create customization_groups table\n    try {\n      await db.execute(`\n        CREATE TABLE IF NOT EXISTS customization_groups (\n          id SERIAL PRIMARY KEY,\n          title TEXT NOT NULL\n        )\n      `);\n      console.log(\"✓ Created customization_groups table\");\n    } catch (error) {\n      console.log(`Error creating customization_groups: ${error.message}`);\n    }\n\n    // Create customization_options table\n    try {\n      await db.execute(`\n        CREATE TABLE IF NOT EXISTS customization_options (\n          id SERIAL PRIMARY KEY,\n          name TEXT NOT NULL,\n          extra_price INTEGER DEFAULT 0,\n          image_url TEXT NOT NULL DEFAULT '',\n          group_id INTEGER NOT NULL REFERENCES customization_groups(id)\n        )\n      `);\n      console.log(\"✓ Created customization_options table\");\n    } catch (error) {\n      console.log(`Error creating customization_options: ${error.message}`);\n    }\n\n    // Create item_customization_map table\n    try {\n      await db.execute(`\n        CREATE TABLE IF NOT EXISTS item_customization_map (\n          id SERIAL PRIMARY KEY,\n          item_id INTEGER NOT NULL REFERENCES menu_items(id),\n          option_id INTEGER NOT NULL REFERENCES customization_options(id)\n        )\n      `);\n      console.log(\"✓ Created item_customization_map table\");\n    } catch (error) {\n      console.log(`Error creating item_customization_map: ${error.message}`);\n    }\n\n    console.log(\"\\nAll missing tables created successfully!\");\n\n  } catch (error) {\n    console.error(\"Error creating tables:\", error);\n    throw error;\n  }\n}\n\n// Run if this file is executed directly\nif (import.meta.url === `file://${process.argv[1]}`) {\n  createMissingTables()\n    .then(() => {\n      console.log(\"Table creation completed!\");\n      process.exit(0);\n    })\n    .catch((error) => {\n      console.error(\"Table creation failed:\", error);\n      process.exit(1);\n    });\n}\n\nexport { createMissingTables };\n"}