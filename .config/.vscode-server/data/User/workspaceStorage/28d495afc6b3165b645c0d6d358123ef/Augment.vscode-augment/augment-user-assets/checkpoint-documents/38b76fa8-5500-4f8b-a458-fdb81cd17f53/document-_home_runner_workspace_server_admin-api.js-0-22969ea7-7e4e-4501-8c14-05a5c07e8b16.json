{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "server/admin-api.js"}, "originalCode": "require('dotenv').config();\nconst express = require('express');\nconst cors = require('cors');\nconst db = require('./db');\n\nconst app = express();\nconst PORT = process.env.PORT || 3001;\n\n// Middleware\napp.use(cors());\napp.use(express.json());\n\n// Log incoming requests\napp.use((req, res, next) => {\n  console.log(`[${new Date().toISOString()}] ${req.method} ${req.url}`);\n  next();\n});\n\n// Admin Settings Routes\n// GET settings\napp.get('/api/admin/settings', async (req, res) => {\n  try {\n    const result = await db.query('SELECT * FROM restaurant_settings ORDER BY id LIMIT 1');\n    if (result.rows.length === 0) {\n      return res.status(404).json({ error: 'Settings not found' });\n    }\n    \n    res.json(result.rows[0]);\n  } catch (error) {\n    console.error('Error fetching settings:', error);\n    res.status(500).json({ error: 'Internal server error' });\n  }\n});\n\n// UPDATE settings\napp.put('/api/admin/settings', async (req, res) => {\n  const { restaurant_open, business_hours, delivery_fee, estimated_time } = req.body;\n  \n  try {\n    const result = await db.query(\n      'UPDATE restaurant_settings SET restaurant_open = $1, business_hours = $2, delivery_fee = $3, estimated_time = $4 WHERE id = 1 RETURNING *',\n      [restaurant_open, business_hours, delivery_fee, estimated_time]\n    );\n    \n    if (result.rows.length === 0) {\n      // If no row was updated, insert a new one\n      const insertResult = await db.query(\n        'INSERT INTO restaurant_settings (restaurant_open, business_hours, delivery_fee, estimated_time) VALUES ($1, $2, $3, $4) RETURNING *',\n        [restaurant_open, business_hours, delivery_fee, estimated_time]\n      );\n      return res.json(insertResult.rows[0]);\n    }\n    \n    res.json(result.rows[0]);\n  } catch (error) {\n    console.error('Error updating settings:', error);\n    res.status(500).json({ error: 'Internal server error' });\n  }\n});\n\n// Categories Routes\n// GET all categories\napp.get('/api/categories', async (req, res) => {\n  try {\n    const result = await db.query('SELECT * FROM categories ORDER BY name');\n    res.json(result.rows);\n  } catch (error) {\n    console.error('Error fetching categories:', error);\n    res.status(500).json({ error: 'Internal server error' });\n  }\n});\n\n// CREATE category\napp.post('/api/categories', async (req, res) => {\n  const { name, image_url } = req.body;\n  \n  if (!name) {\n    return res.status(400).json({ error: 'Category name is required' });\n  }\n  \n  try {\n    const result = await db.query(\n      'INSERT INTO categories (name, image_url) VALUES ($1, $2) RETURNING *',\n      [name, image_url]\n    );\n    \n    res.status(201).json(result.rows[0]);\n  } catch (error) {\n    console.error('Error creating category:', error);\n    res.status(500).json({ error: 'Internal server error' });\n  }\n});\n\n// UPDATE category\napp.put('/api/categories/:id', async (req, res) => {\n  const { id } = req.params;\n  const { name, image_url } = req.body;\n  \n  if (!name) {\n    return res.status(400).json({ error: 'Category name is required' });\n  }\n  \n  try {\n    const result = await db.query(\n      'UPDATE categories SET name = $1, image_url = $2 WHERE id = $3 RETURNING *',\n      [name, image_url, id]\n    );\n    \n    if (result.rows.length === 0) {\n      return res.status(404).json({ error: 'Category not found' });\n    }\n    \n    res.json(result.rows[0]);\n  } catch (error) {\n    console.error('Error updating category:', error);\n    res.status(500).json({ error: 'Internal server error' });\n  }\n});\n\n// DELETE category\napp.delete('/api/categories/:id', async (req, res) => {\n  const { id } = req.params;\n  \n  try {\n    const result = await db.query('DELETE FROM categories WHERE id = $1 RETURNING *', [id]);\n    \n    if (result.rows.length === 0) {\n      return res.status(404).json({ error: 'Category not found' });\n    }\n    \n    res.json({ message: 'Category deleted successfully', category: result.rows[0] });\n  } catch (error) {\n    console.error('Error deleting category:', error);\n    res.status(500).json({ error: 'Internal server error' });\n  }\n});\n\n// Menu Items Routes\n// GET all menu items\napp.get('/api/items', async (req, res) => {\n  try {\n    const result = await db.query(`\n      SELECT m.*, c.name as category_name\n      FROM menu_items m\n      LEFT JOIN categories c ON m.category_id = c.id\n      ORDER BY m.name\n    `);\n    \n    res.json(result.rows);\n  } catch (error) {\n    console.error('Error fetching menu items:', error);\n    res.status(500).json({ error: 'Internal server error' });\n  }\n});\n\n// CREATE menu item\napp.post('/api/items', async (req, res) => {\n  const { name, description, price, image_url, category_id, available } = req.body;\n  \n  if (!name || !price) {\n    return res.status(400).json({ error: 'Name and price are required' });\n  }\n  \n  try {\n    const result = await db.query(\n      'INSERT INTO menu_items (name, description, price, image_url, category_id, available) VALUES ($1, $2, $3, $4, $5, $6) RETURNING *',\n      [name, description, price, image_url, category_id, available]\n    );\n    \n    res.status(201).json(result.rows[0]);\n  } catch (error) {\n    console.error('Error creating menu item:', error);\n    res.status(500).json({ error: 'Internal server error' });\n  }\n});\n\n// UPDATE menu item\napp.put('/api/items/:id', async (req, res) => {\n  const { id } = req.params;\n  const { name, description, price, image_url, category_id, available } = req.body;\n  \n  if (!name || !price) {\n    return res.status(400).json({ error: 'Name and price are required' });\n  }\n  \n  try {\n    const result = await db.query(\n      'UPDATE menu_items SET name = $1, description = $2, price = $3, image_url = $4, category_id = $5, available = $6 WHERE id = $7 RETURNING *',\n      [name, description, price, image_url, category_id, available, id]\n    );\n    \n    if (result.rows.length === 0) {\n      return res.status(404).json({ error: 'Menu item not found' });\n    }\n    \n    res.json(result.rows[0]);\n  } catch (error) {\n    console.error('Error updating menu item:', error);\n    res.status(500).json({ error: 'Internal server error' });\n  }\n});\n\n// DELETE menu item\napp.delete('/api/items/:id', async (req, res) => {\n  const { id } = req.params;\n  \n  try {\n    const result = await db.query('DELETE FROM menu_items WHERE id = $1 RETURNING *', [id]);\n    \n    if (result.rows.length === 0) {\n      return res.status(404).json({ error: 'Menu item not found' });\n    }\n    \n    res.json({ message: 'Menu item deleted successfully', item: result.rows[0] });\n  } catch (error) {\n    console.error('Error deleting menu item:', error);\n    res.status(500).json({ error: 'Internal server error' });\n  }\n});\n\n// Analytics Routes\n// GET summary statistics\napp.get('/api/analytics/summary', async (req, res) => {\n  try {\n    // Today's revenue\n    const todayResult = await db.query(`\n      SELECT COALESCE(SUM(total), 0) as today\n      FROM orders\n      WHERE DATE(created_at) = CURRENT_DATE\n    `);\n    \n    // Week's revenue\n    const weekResult = await db.query(`\n      SELECT COALESCE(SUM(total), 0) as week\n      FROM orders\n      WHERE created_at >= CURRENT_DATE - INTERVAL '7 days'\n    `);\n    \n    // Month's revenue\n    const monthResult = await db.query(`\n      SELECT COALESCE(SUM(total), 0) as month\n      FROM orders\n      WHERE created_at >= CURRENT_DATE - INTERVAL '30 days'\n    `);\n    \n    // Order count\n    const orderCountResult = await db.query(`\n      SELECT COUNT(*) as order_count\n      FROM orders\n      WHERE created_at >= CURRENT_DATE - INTERVAL '30 days'\n    `);\n    \n    res.json({\n      today: parseInt(todayResult.rows[0].today),\n      week: parseInt(weekResult.rows[0].week),\n      month: parseInt(monthResult.rows[0].month),\n      orderCount: parseInt(orderCountResult.rows[0].order_count)\n    });\n  } catch (error) {\n    console.error('Error fetching analytics summary:', error);\n    res.status(500).json({ error: 'Internal server error' });\n  }\n});\n\n// GET daily revenue\napp.get('/api/analytics/daily', async (req, res) => {\n  try {\n    const result = await db.query(`\n      SELECT \n        DATE(created_at) as date,\n        COALESCE(SUM(total), 0) as total\n      FROM orders\n      WHERE created_at >= CURRENT_DATE - INTERVAL '7 days'\n      GROUP BY DATE(created_at)\n      ORDER BY date\n    `);\n    \n    res.json(result.rows);\n  } catch (error) {\n    console.error('Error fetching daily revenue:', error);\n    res.status(500).json({ error: 'Internal server error' });\n  }\n});\n\n// GET revenue by category\napp.get('/api/analytics/by-category', async (req, res) => {\n  try {\n    // This is an approximation since we store items as JSONB\n    // In a real system, we'd likely want a dedicated order_items table\n    const result = await db.query(`\n      WITH ordered_items AS (\n        SELECT \n          o.id as order_id,\n          o.total,\n          jsonb_array_elements(o.items) as item_data\n        FROM orders o\n      ),\n      item_categories AS (\n        SELECT \n          oi.order_id,\n          m.category_id,\n          c.name as category_name,\n          (oi.item_data->>'price')::integer * (oi.item_data->>'quantity')::integer as item_total\n        FROM ordered_items oi\n        LEFT JOIN menu_items m ON (oi.item_data->>'id')::integer = m.id\n        LEFT JOIN categories c ON m.category_id = c.id\n        WHERE m.id IS NOT NULL\n      )\n      SELECT \n        COALESCE(category_name, 'Uncategorized') as category,\n        SUM(item_total) as total\n      FROM item_categories\n      GROUP BY category_name\n      ORDER BY total DESC\n    `);\n    \n    res.json(result.rows);\n  } catch (error) {\n    console.error('Error fetching category revenue:', error);\n    res.status(500).json({ error: 'Internal server error' });\n  }\n});\n\n// Error handling middleware\napp.use((err, req, res, next) => {\n  console.error(err.stack);\n  res.status(500).json({ error: 'Something went wrong!' });\n});\n\n// Start server\napp.listen(PORT, () => {\n  console.log(`Admin API Server running on port ${PORT}`);\n});\n\nmodule.exports = app;", "modifiedCode": "require('dotenv').config();\nconst express = require('express');\nconst cors = require('cors');\nconst db = require('./db');\n\nconst app = express();\nconst PORT = process.env.PORT || 3001;\n\n// Middleware\napp.use(cors());\napp.use(express.json());\n\n// Log incoming requests\napp.use((req, res, next) => {\n  console.log(`[${new Date().toISOString()}] ${req.method} ${req.url}`);\n  next();\n});\n\n// Admin Settings Routes\n// GET settings\napp.get('/api/admin/settings', async (req, res) => {\n  try {\n    const result = await db.query('SELECT * FROM restaurant_settings ORDER BY id LIMIT 1');\n    if (result.rows.length === 0) {\n      return res.status(404).json({ error: 'Settings not found' });\n    }\n    \n    res.json(result.rows[0]);\n  } catch (error) {\n    console.error('Error fetching settings:', error);\n    res.status(500).json({ error: 'Internal server error' });\n  }\n});\n\n// UPDATE settings\napp.put('/api/admin/settings', async (req, res) => {\n  const { restaurant_open, business_hours, delivery_fee, estimated_time } = req.body;\n  \n  try {\n    const result = await db.query(\n      'UPDATE restaurant_settings SET restaurant_open = $1, business_hours = $2, delivery_fee = $3, estimated_time = $4 WHERE id = 1 RETURNING *',\n      [restaurant_open, business_hours, delivery_fee, estimated_time]\n    );\n    \n    if (result.rows.length === 0) {\n      // If no row was updated, insert a new one\n      const insertResult = await db.query(\n        'INSERT INTO restaurant_settings (restaurant_open, business_hours, delivery_fee, estimated_time) VALUES ($1, $2, $3, $4) RETURNING *',\n        [restaurant_open, business_hours, delivery_fee, estimated_time]\n      );\n      return res.json(insertResult.rows[0]);\n    }\n    \n    res.json(result.rows[0]);\n  } catch (error) {\n    console.error('Error updating settings:', error);\n    res.status(500).json({ error: 'Internal server error' });\n  }\n});\n\n// Categories Routes\n// GET all categories\napp.get('/api/categories', async (req, res) => {\n  try {\n    const result = await db.query('SELECT * FROM categories ORDER BY name');\n    res.json(result.rows);\n  } catch (error) {\n    console.error('Error fetching categories:', error);\n    res.status(500).json({ error: 'Internal server error' });\n  }\n});\n\n// CREATE category\napp.post('/api/categories', async (req, res) => {\n  const { name, image_url } = req.body;\n  \n  if (!name) {\n    return res.status(400).json({ error: 'Category name is required' });\n  }\n  \n  try {\n    const result = await db.query(\n      'INSERT INTO categories (name, image_url) VALUES ($1, $2) RETURNING *',\n      [name, image_url]\n    );\n    \n    res.status(201).json(result.rows[0]);\n  } catch (error) {\n    console.error('Error creating category:', error);\n    res.status(500).json({ error: 'Internal server error' });\n  }\n});\n\n// UPDATE category\napp.put('/api/categories/:id', async (req, res) => {\n  const { id } = req.params;\n  const { name, image_url } = req.body;\n  \n  if (!name) {\n    return res.status(400).json({ error: 'Category name is required' });\n  }\n  \n  try {\n    const result = await db.query(\n      'UPDATE categories SET name = $1, image_url = $2 WHERE id = $3 RETURNING *',\n      [name, image_url, id]\n    );\n    \n    if (result.rows.length === 0) {\n      return res.status(404).json({ error: 'Category not found' });\n    }\n    \n    res.json(result.rows[0]);\n  } catch (error) {\n    console.error('Error updating category:', error);\n    res.status(500).json({ error: 'Internal server error' });\n  }\n});\n\n// DELETE category\napp.delete('/api/categories/:id', async (req, res) => {\n  const { id } = req.params;\n  \n  try {\n    const result = await db.query('DELETE FROM categories WHERE id = $1 RETURNING *', [id]);\n    \n    if (result.rows.length === 0) {\n      return res.status(404).json({ error: 'Category not found' });\n    }\n    \n    res.json({ message: 'Category deleted successfully', category: result.rows[0] });\n  } catch (error) {\n    console.error('Error deleting category:', error);\n    res.status(500).json({ error: 'Internal server error' });\n  }\n});\n\n// Menu Items Routes\n// GET all menu items\napp.get('/api/items', async (req, res) => {\n  try {\n    const result = await db.query(`\n      SELECT m.*, c.name as category_name\n      FROM menu_items m\n      LEFT JOIN categories c ON m.category_id = c.id\n      ORDER BY m.name\n    `);\n    \n    res.json(result.rows);\n  } catch (error) {\n    console.error('Error fetching menu items:', error);\n    res.status(500).json({ error: 'Internal server error' });\n  }\n});\n\n// CREATE menu item\napp.post('/api/items', async (req, res) => {\n  const { name, description, price, image_url, category_id, available } = req.body;\n  \n  if (!name || !price) {\n    return res.status(400).json({ error: 'Name and price are required' });\n  }\n  \n  try {\n    const result = await db.query(\n      'INSERT INTO menu_items (name, description, price, image_url, category_id, available) VALUES ($1, $2, $3, $4, $5, $6) RETURNING *',\n      [name, description, price, image_url, category_id, available]\n    );\n    \n    res.status(201).json(result.rows[0]);\n  } catch (error) {\n    console.error('Error creating menu item:', error);\n    res.status(500).json({ error: 'Internal server error' });\n  }\n});\n\n// UPDATE menu item\napp.put('/api/items/:id', async (req, res) => {\n  const { id } = req.params;\n  const { name, description, price, image_url, category_id, available } = req.body;\n  \n  if (!name || !price) {\n    return res.status(400).json({ error: 'Name and price are required' });\n  }\n  \n  try {\n    const result = await db.query(\n      'UPDATE menu_items SET name = $1, description = $2, price = $3, image_url = $4, category_id = $5, available = $6 WHERE id = $7 RETURNING *',\n      [name, description, price, image_url, category_id, available, id]\n    );\n    \n    if (result.rows.length === 0) {\n      return res.status(404).json({ error: 'Menu item not found' });\n    }\n    \n    res.json(result.rows[0]);\n  } catch (error) {\n    console.error('Error updating menu item:', error);\n    res.status(500).json({ error: 'Internal server error' });\n  }\n});\n\n// DELETE menu item\napp.delete('/api/items/:id', async (req, res) => {\n  const { id } = req.params;\n  \n  try {\n    const result = await db.query('DELETE FROM menu_items WHERE id = $1 RETURNING *', [id]);\n    \n    if (result.rows.length === 0) {\n      return res.status(404).json({ error: 'Menu item not found' });\n    }\n    \n    res.json({ message: 'Menu item deleted successfully', item: result.rows[0] });\n  } catch (error) {\n    console.error('Error deleting menu item:', error);\n    res.status(500).json({ error: 'Internal server error' });\n  }\n});\n\n// Analytics Routes\n// GET summary statistics\napp.get('/api/analytics/summary', async (req, res) => {\n  try {\n    // Today's revenue\n    const todayResult = await db.query(`\n      SELECT COALESCE(SUM(total), 0) as today\n      FROM orders\n      WHERE DATE(created_at) = CURRENT_DATE\n    `);\n    \n    // Week's revenue\n    const weekResult = await db.query(`\n      SELECT COALESCE(SUM(total), 0) as week\n      FROM orders\n      WHERE created_at >= CURRENT_DATE - INTERVAL '7 days'\n    `);\n    \n    // Month's revenue\n    const monthResult = await db.query(`\n      SELECT COALESCE(SUM(total), 0) as month\n      FROM orders\n      WHERE created_at >= CURRENT_DATE - INTERVAL '30 days'\n    `);\n    \n    // Order count\n    const orderCountResult = await db.query(`\n      SELECT COUNT(*) as order_count\n      FROM orders\n      WHERE created_at >= CURRENT_DATE - INTERVAL '30 days'\n    `);\n    \n    res.json({\n      today: parseInt(todayResult.rows[0].today),\n      week: parseInt(weekResult.rows[0].week),\n      month: parseInt(monthResult.rows[0].month),\n      orderCount: parseInt(orderCountResult.rows[0].order_count)\n    });\n  } catch (error) {\n    console.error('Error fetching analytics summary:', error);\n    res.status(500).json({ error: 'Internal server error' });\n  }\n});\n\n// GET daily revenue\napp.get('/api/analytics/daily', async (req, res) => {\n  try {\n    const result = await db.query(`\n      SELECT \n        DATE(created_at) as date,\n        COALESCE(SUM(total), 0) as total\n      FROM orders\n      WHERE created_at >= CURRENT_DATE - INTERVAL '7 days'\n      GROUP BY DATE(created_at)\n      ORDER BY date\n    `);\n    \n    res.json(result.rows);\n  } catch (error) {\n    console.error('Error fetching daily revenue:', error);\n    res.status(500).json({ error: 'Internal server error' });\n  }\n});\n\n// GET revenue by category\napp.get('/api/analytics/by-category', async (req, res) => {\n  try {\n    // This is an approximation since we store items as JSONB\n    // In a real system, we'd likely want a dedicated order_items table\n    const result = await db.query(`\n      WITH ordered_items AS (\n        SELECT \n          o.id as order_id,\n          o.total,\n          jsonb_array_elements(o.items) as item_data\n        FROM orders o\n      ),\n      item_categories AS (\n        SELECT \n          oi.order_id,\n          m.category_id,\n          c.name as category_name,\n          (oi.item_data->>'price')::integer * (oi.item_data->>'quantity')::integer as item_total\n        FROM ordered_items oi\n        LEFT JOIN menu_items m ON (oi.item_data->>'id')::integer = m.id\n        LEFT JOIN categories c ON m.category_id = c.id\n        WHERE m.id IS NOT NULL\n      )\n      SELECT \n        COALESCE(category_name, 'Uncategorized') as category,\n        SUM(item_total) as total\n      FROM item_categories\n      GROUP BY category_name\n      ORDER BY total DESC\n    `);\n    \n    res.json(result.rows);\n  } catch (error) {\n    console.error('Error fetching category revenue:', error);\n    res.status(500).json({ error: 'Internal server error' });\n  }\n});\n\n// Error handling middleware\napp.use((err, req, res, next) => {\n  console.error(err.stack);\n  res.status(500).json({ error: 'Something went wrong!' });\n});\n\n// Start server\napp.listen(PORT, () => {\n  console.log(`Admin API Server running on port ${PORT}`);\n});\n\nmodule.exports = app;"}