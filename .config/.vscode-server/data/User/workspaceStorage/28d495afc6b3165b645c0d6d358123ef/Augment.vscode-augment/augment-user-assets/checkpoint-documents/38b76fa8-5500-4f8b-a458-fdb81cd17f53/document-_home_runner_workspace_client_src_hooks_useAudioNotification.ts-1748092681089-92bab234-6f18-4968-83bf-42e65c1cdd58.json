{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/hooks/useAudioNotification.ts"}, "originalCode": "import { useCallback, useRef } from 'react';\nimport { useNotifications } from '@/context/NotificationContext';\n\ninterface AudioNotificationOptions {\n  volume?: number;\n  fallbackToBeep?: boolean;\n}\n\nexport const useAudioNotification = (options: AudioNotificationOptions = {}) => {\n  const { soundEnabled } = useNotifications();\n  const { volume = 0.5 } = options;\n\n  const playSystemBeep = useCallback(async (soundType: 'new_order' | 'status_update' | 'error' = 'new_order') => {\n    const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();\n    const oscillator = audioContext.createOscillator();\n    const gainNode = audioContext.createGain();\n\n    oscillator.connect(gainNode);\n    gainNode.connect(audioContext.destination);\n\n    // Different frequencies and patterns for different notification types\n    let frequency = 800;\n    let duration = 0.3;\n\n    switch (soundType) {\n      case 'new_order':\n        frequency = 880; // A5 note - pleasant and attention-grabbing\n        duration = 0.4;\n        break;\n      case 'status_update':\n        frequency = 660; // E5 note - softer\n        duration = 0.2;\n        break;\n      case 'error':\n        frequency = 440; // A4 note - lower, more serious\n        duration = 0.5;\n        break;\n    }\n\n    oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);\n    oscillator.type = 'sine';\n\n    gainNode.gain.setValueAtTime(0, audioContext.currentTime);\n    gainNode.gain.linearRampToValueAtTime(volume * 0.3, audioContext.currentTime + 0.01);\n    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration);\n\n    oscillator.start(audioContext.currentTime);\n    oscillator.stop(audioContext.currentTime + duration);\n\n    // Clean up\n    setTimeout(() => {\n      audioContext.close();\n    }, (duration + 0.2) * 1000);\n  }, [volume]);\n\n  const playNotificationSound = useCallback(async (soundType: 'new_order' | 'status_update' | 'error' = 'new_order') => {\n    if (!soundEnabled) return;\n\n    try {\n      // For now, use programmatically generated sounds since we don't have audio files yet\n      // In production, replace this with actual sound files\n      await playSystemBeep(soundType);\n    } catch (error) {\n      console.warn('Could not play notification sound:', error);\n    }\n  }, [soundEnabled, playSystemBeep]);\n\n  const preloadSounds = useCallback(() => {\n    const soundFiles = [\n      '/sounds/new-order.mp3',\n      '/sounds/status-update.mp3',\n      '/sounds/error.mp3'\n    ];\n\n    soundFiles.forEach(soundFile => {\n      const audio = new Audio();\n      audio.preload = 'auto';\n      audio.src = soundFile;\n      // Don't store these, just preload them\n    });\n  }, []);\n\n  return {\n    playNotificationSound,\n    playSystemBeep,\n    preloadSounds,\n    soundEnabled\n  };\n};\n", "modifiedCode": "import { useCallback } from 'react';\nimport { useNotifications } from '@/context/NotificationContext';\n\ninterface AudioNotificationOptions {\n  volume?: number;\n}\n\nexport const useAudioNotification = (options: AudioNotificationOptions = {}) => {\n  const { soundEnabled } = useNotifications();\n  const { volume = 0.5 } = options;\n\n  const playSystemBeep = useCallback(async (soundType: 'new_order' | 'status_update' | 'error' = 'new_order') => {\n    const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();\n    const oscillator = audioContext.createOscillator();\n    const gainNode = audioContext.createGain();\n\n    oscillator.connect(gainNode);\n    gainNode.connect(audioContext.destination);\n\n    // Different frequencies and patterns for different notification types\n    let frequency = 800;\n    let duration = 0.3;\n\n    switch (soundType) {\n      case 'new_order':\n        frequency = 880; // A5 note - pleasant and attention-grabbing\n        duration = 0.4;\n        break;\n      case 'status_update':\n        frequency = 660; // E5 note - softer\n        duration = 0.2;\n        break;\n      case 'error':\n        frequency = 440; // A4 note - lower, more serious\n        duration = 0.5;\n        break;\n    }\n\n    oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);\n    oscillator.type = 'sine';\n\n    gainNode.gain.setValueAtTime(0, audioContext.currentTime);\n    gainNode.gain.linearRampToValueAtTime(volume * 0.3, audioContext.currentTime + 0.01);\n    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration);\n\n    oscillator.start(audioContext.currentTime);\n    oscillator.stop(audioContext.currentTime + duration);\n\n    // Clean up\n    setTimeout(() => {\n      audioContext.close();\n    }, (duration + 0.2) * 1000);\n  }, [volume]);\n\n  const playNotificationSound = useCallback(async (soundType: 'new_order' | 'status_update' | 'error' = 'new_order') => {\n    if (!soundEnabled) return;\n\n    try {\n      // For now, use programmatically generated sounds since we don't have audio files yet\n      // In production, replace this with actual sound files\n      await playSystemBeep(soundType);\n    } catch (error) {\n      console.warn('Could not play notification sound:', error);\n    }\n  }, [soundEnabled, playSystemBeep]);\n\n  const preloadSounds = useCallback(() => {\n    const soundFiles = [\n      '/sounds/new-order.mp3',\n      '/sounds/status-update.mp3',\n      '/sounds/error.mp3'\n    ];\n\n    soundFiles.forEach(soundFile => {\n      const audio = new Audio();\n      audio.preload = 'auto';\n      audio.src = soundFile;\n      // Don't store these, just preload them\n    });\n  }, []);\n\n  return {\n    playNotificationSound,\n    playSystemBeep,\n    preloadSounds,\n    soundEnabled\n  };\n};\n"}