{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/context/NotificationContext.tsx"}, "modifiedCode": "import { createContext, useContext, useState, ReactNode, useCallback } from \"react\";\n\nexport interface Notification {\n  id: string;\n  type: 'new_order' | 'status_update' | 'system';\n  title: string;\n  message: string;\n  orderId?: number;\n  customerName?: string;\n  timestamp: Date;\n  read: boolean;\n  priority: 'low' | 'medium' | 'high';\n}\n\ninterface NotificationContextType {\n  notifications: Notification[];\n  unreadCount: number;\n  soundEnabled: boolean;\n  addNotification: (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => void;\n  markAsRead: (notificationId: string) => void;\n  markAllAsRead: () => void;\n  removeNotification: (notificationId: string) => void;\n  clearAllNotifications: () => void;\n  toggleSound: () => void;\n}\n\nconst NotificationContext = createContext<NotificationContextType | undefined>(undefined);\n\nexport const useNotifications = () => {\n  const context = useContext(NotificationContext);\n  if (!context) {\n    throw new Error(\"useNotifications must be used within a NotificationProvider\");\n  }\n  return context;\n};\n\ninterface NotificationProviderProps {\n  children: ReactNode;\n}\n\nexport const NotificationProvider = ({ children }: NotificationProviderProps) => {\n  const [notifications, setNotifications] = useState<Notification[]>([]);\n  const [soundEnabled, setSoundEnabled] = useState(() => {\n    // Get sound preference from localStorage, default to true\n    const saved = localStorage.getItem('notificationSoundEnabled');\n    return saved !== null ? JSON.parse(saved) : true;\n  });\n\n  const addNotification = useCallback((notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => {\n    const newNotification: Notification = {\n      ...notification,\n      id: `notification-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,\n      timestamp: new Date(),\n      read: false,\n    };\n\n    setNotifications(prev => [newNotification, ...prev]);\n\n    // Auto-remove low priority notifications after 30 seconds\n    if (notification.priority === 'low') {\n      setTimeout(() => {\n        setNotifications(prev => prev.filter(n => n.id !== newNotification.id));\n      }, 30000);\n    }\n  }, []);\n\n  const markAsRead = useCallback((notificationId: string) => {\n    setNotifications(prev =>\n      prev.map(notification =>\n        notification.id === notificationId\n          ? { ...notification, read: true }\n          : notification\n      )\n    );\n  }, []);\n\n  const markAllAsRead = useCallback(() => {\n    setNotifications(prev =>\n      prev.map(notification => ({ ...notification, read: true }))\n    );\n  }, []);\n\n  const removeNotification = useCallback((notificationId: string) => {\n    setNotifications(prev => prev.filter(n => n.id !== notificationId));\n  }, []);\n\n  const clearAllNotifications = useCallback(() => {\n    setNotifications([]);\n  }, []);\n\n  const toggleSound = useCallback(() => {\n    const newSoundEnabled = !soundEnabled;\n    setSoundEnabled(newSoundEnabled);\n    localStorage.setItem('notificationSoundEnabled', JSON.stringify(newSoundEnabled));\n  }, [soundEnabled]);\n\n  const unreadCount = notifications.filter(n => !n.read).length;\n\n  const value: NotificationContextType = {\n    notifications,\n    unreadCount,\n    soundEnabled,\n    addNotification,\n    markAsRead,\n    markAllAsRead,\n    removeNotification,\n    clearAllNotifications,\n    toggleSound,\n  };\n\n  return (\n    <NotificationContext.Provider value={value}>\n      {children}\n    </NotificationContext.Provider>\n  );\n};\n"}