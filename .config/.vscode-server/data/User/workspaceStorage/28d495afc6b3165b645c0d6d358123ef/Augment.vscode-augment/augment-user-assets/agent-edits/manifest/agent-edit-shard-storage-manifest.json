{"version": 1, "lastUpdated": 1748095714857, "shards": {"shard-19b0ac25-acfd-4590-b4a9-8bea57fe3117": {"checkpointDocumentIds": ["19b0ac25-acfd-4590-b4a9-8bea57fe3117:/home/<USER>/workspace/client/src/components/Header.tsx", "19b0ac25-acfd-4590-b4a9-8bea57fe3117:/home/<USER>/workspace/server/schema.sql", "19b0ac25-acfd-4590-b4a9-8bea57fe3117:/home/<USER>/workspace/client/src/pages/auth/AuthPage.tsx", "19b0ac25-acfd-4590-b4a9-8bea57fe3117:/home/<USER>/workspace/server/auth/auth-routes.ts", "19b0ac25-acfd-4590-b4a9-8bea57fe3117:/home/<USER>/workspace/server/auth/auth-middleware.ts", "19b0ac25-acfd-4590-b4a9-8bea57fe3117:/home/<USER>/workspace/server/routes.ts", "19b0ac25-acfd-4590-b4a9-8bea57fe3117:/home/<USER>/workspace/shared/schema.ts", "19b0ac25-acfd-4590-b4a9-8bea57fe3117:/home/<USER>/workspace/server/storage.ts"], "size": 348226, "checkpointCount": 21, "lastModified": 1748038969867}, "shard-c04c3efc-7efa-462c-b184-42299fe25be8": {"checkpointDocumentIds": ["c04c3efc-7efa-462c-b184-42299fe25be8:/home/<USER>/workspace/shared/schema.ts", "c04c3efc-7efa-462c-b184-42299fe25be8:/home/<USER>/workspace/server/schema.sql", "c04c3efc-7efa-462c-b184-42299fe25be8:/home/<USER>/workspace/server/auth/password-utils.ts", "c04c3efc-7efa-462c-b184-42299fe25be8:/home/<USER>/workspace/server/auth/auth-service.ts", "c04c3efc-7efa-462c-b184-42299fe25be8:/home/<USER>/workspace/server/auth/passport-config.ts", "c04c3efc-7efa-462c-b184-42299fe25be8:/home/<USER>/workspace/server/auth/auth-middleware.ts", "c04c3efc-7efa-462c-b184-42299fe25be8:/home/<USER>/workspace/server/auth/auth-routes.ts", "c04c3efc-7efa-462c-b184-42299fe25be8:/home/<USER>/workspace/server/index.ts", "c04c3efc-7efa-462c-b184-42299fe25be8:/home/<USER>/workspace/server/routes.ts", "c04c3efc-7efa-462c-b184-42299fe25be8:/home/<USER>/workspace/client/src/context/AuthContext.tsx", "c04c3efc-7efa-462c-b184-42299fe25be8:/home/<USER>/workspace/client/src/components/auth/ProtectedRoute.tsx", "c04c3efc-7efa-462c-b184-42299fe25be8:/home/<USER>/workspace/client/src/pages/auth/Login.tsx", "c04c3efc-7efa-462c-b184-42299fe25be8:/home/<USER>/workspace/client/src/pages/auth/Register.tsx", "c04c3efc-7efa-462c-b184-42299fe25be8:/home/<USER>/workspace/client/src/pages/admin/AdminDashboard.tsx", "c04c3efc-7efa-462c-b184-42299fe25be8:/home/<USER>/workspace/client/src/pages/manager/ManagerDashboard.tsx", "c04c3efc-7efa-462c-b184-42299fe25be8:/home/<USER>/workspace/client/src/pages/driver/DriverDashboard.tsx", "c04c3efc-7efa-462c-b184-42299fe25be8:/home/<USER>/workspace/client/src/App.tsx"], "size": 329854, "checkpointCount": 57, "lastModified": 1748037901195}, "shard-83848ed4-911c-441e-b270-f1f5a7ae95f9": {"checkpointDocumentIds": ["83848ed4-911c-441e-b270-f1f5a7ae95f9:/home/<USER>/workspace/client/src/context/RestaurantStatusContext.tsx", "83848ed4-911c-441e-b270-f1f5a7ae95f9:/home/<USER>/workspace/client/src/App.tsx", "83848ed4-911c-441e-b270-f1f5a7ae95f9:/home/<USER>/workspace/client/src/components/RestaurantStatusBanner.tsx", "83848ed4-911c-441e-b270-f1f5a7ae95f9:/home/<USER>/workspace/client/src/components/Header.tsx", "83848ed4-911c-441e-b270-f1f5a7ae95f9:/home/<USER>/workspace/client/src/components/DishCard.tsx", "83848ed4-911c-441e-b270-f1f5a7ae95f9:/home/<USER>/workspace/client/src/pages/Checkout.tsx", "83848ed4-911c-441e-b270-f1f5a7ae95f9:/home/<USER>/workspace/server/routes.ts", "83848ed4-911c-441e-b270-f1f5a7ae95f9:/home/<USER>/workspace/client/src/pages/admin/AdminLayout.tsx", "83848ed4-911c-441e-b270-f1f5a7ae95f9:/home/<USER>/workspace/client/src/pages/admin/Settings.tsx", "83848ed4-911c-441e-b270-f1f5a7ae95f9:/home/<USER>/workspace/server/storage.ts", "83848ed4-911c-441e-b270-f1f5a7ae95f9:/home/<USER>/workspace/client/src/api/adminApi.ts", "83848ed4-911c-441e-b270-f1f5a7ae95f9:/home/<USER>/workspace/client/src/pages/admin/Menu.tsx", "83848ed4-911c-441e-b270-f1f5a7ae95f9:/home/<USER>/workspace/client/src/pages/Menu.tsx", "83848ed4-911c-441e-b270-f1f5a7ae95f9:/home/<USER>/workspace/client/src/components/MenuItemCard.tsx", "83848ed4-911c-441e-b270-f1f5a7ae95f9:/home/<USER>/workspace/client/src/components/CustomizeModal.tsx", "83848ed4-911c-441e-b270-f1f5a7ae95f9:/home/<USER>/workspace/server/migrate.ts", "83848ed4-911c-441e-b270-f1f5a7ae95f9:/home/<USER>/workspace/server/init-db.ts", "83848ed4-911c-441e-b270-f1f5a7ae95f9:/home/<USER>/workspace/server/index.ts", "83848ed4-911c-441e-b270-f1f5a7ae95f9:/home/<USER>/workspace/package.json", "83848ed4-911c-441e-b270-f1f5a7ae95f9:/home/<USER>/workspace/server/admin-api.ts", "83848ed4-911c-441e-b270-f1f5a7ae95f9:/home/<USER>/workspace/server/test-db.ts", "83848ed4-911c-441e-b270-f1f5a7ae95f9:/home/<USER>/workspace/DATABASE.md", "83848ed4-911c-441e-b270-f1f5a7ae95f9:/home/<USER>/workspace/server/create-tables.ts", "83848ed4-911c-441e-b270-f1f5a7ae95f9:/home/<USER>/workspace/server/create-missing-tables.ts", "83848ed4-911c-441e-b270-f1f5a7ae95f9:/home/<USER>/workspace/server/seed-customizations.ts", "83848ed4-911c-441e-b270-f1f5a7ae95f9:/home/<USER>/workspace/shared/schema.ts", "83848ed4-911c-441e-b270-f1f5a7ae95f9:/home/<USER>/workspace/client/src/pages/Cart.tsx", "83848ed4-911c-441e-b270-f1f5a7ae95f9:/home/<USER>/workspace/test-db.js", "83848ed4-911c-441e-b270-f1f5a7ae95f9:/home/<USER>/workspace/create-settings-table.js"], "size": 4874038, "checkpointCount": 121, "lastModified": 1748048289123}, "shard-8cd18cae-af48-4628-881a-aae2b3ddde1c": {"checkpointDocumentIds": ["8cd18cae-af48-4628-881a-aae2b3ddde1c:/home/<USER>/workspace/server/test-db.ts", "8cd18cae-af48-4628-881a-aae2b3ddde1c:/home/<USER>/workspace/server/seed-customizations.ts", "8cd18cae-af48-4628-881a-aae2b3ddde1c:/home/<USER>/workspace/server/mock-orders.ts", "8cd18cae-af48-4628-881a-aae2b3ddde1c:/home/<USER>/workspace/scripts/run_seed.js", "8cd18cae-af48-4628-881a-aae2b3ddde1c:/home/<USER>/workspace/scripts/run_seed.cjs", "8cd18cae-af48-4628-881a-aae2b3ddde1c:/home/<USER>/workspace/scripts/seed_database.sql", "8cd18cae-af48-4628-881a-aae2b3ddde1c:/home/<USER>/workspace/scripts/seed_users.ts", "8cd18cae-af48-4628-881a-aae2b3ddde1c:/home/<USER>/workspace/scripts/setup_auth_db.ts", "8cd18cae-af48-4628-881a-aae2b3ddde1c:/home/<USER>/workspace/replit.md", "8cd18cae-af48-4628-881a-aae2b3ddde1c:/home/<USER>/workspace/generated-icon.png", "8cd18cae-af48-4628-881a-aae2b3ddde1c:/home/<USER>/workspace/server.log", "8cd18cae-af48-4628-881a-aae2b3ddde1c:/home/<USER>/workspace/DATABASE.md", "8cd18cae-af48-4628-881a-aae2b3ddde1c:/home/<USER>/workspace/attached_assets/IMG_7388.png", "8cd18cae-af48-4628-881a-aae2b3ddde1c:/home/<USER>/workspace/attached_assets/Pasted--Build-Admin-Panel-for-Restaurant-Settings-Menu-Management-Revenue-Analytics-React-TailwindCSS-1747871652816.txt", "8cd18cae-af48-4628-881a-aae2b3ddde1c:/home/<USER>/workspace/attached_assets/Pasted-Build-Order-Confirmation-Order-Tracker-Page-React-TailwindCSS-Framer-Motion-Objective-Cr-1747870703232.txt", "8cd18cae-af48-4628-881a-aae2b3ddde1c:/home/<USER>/workspace/attached_assets/Pasted-Build-Order-Confirmation-Order-Tracker-Page-React-TailwindCSS-Framer-Motion-Objective-Cr-1747871115566.txt", "8cd18cae-af48-4628-881a-aae2b3ddde1c:/home/<USER>/workspace/attached_assets/Pasted-Build-Order-Confirmation-Order-Tracker-Page-React-TailwindCSS-Framer-Motion-Objective-Cr-1747871623392.txt", "8cd18cae-af48-4628-881a-aae2b3ddde1c:/home/<USER>/workspace/attached_assets/Pasted-Build-a-Backend-API-with-Express-PostgreSQL-for-Admin-Panel-Settings-Menu-Analytics-Objective--1747871722377.txt", "8cd18cae-af48-4628-881a-aae2b3ddde1c:/home/<USER>/workspace/attached_assets/Pasted-Build-a-visually-luxurious-responsive-and-interactive-Cart-page-using-React-TailwindCSS-with-ani-1747870351144.txt", "8cd18cae-af48-4628-881a-aae2b3ddde1c:/home/<USER>/workspace/attached_assets/Pasted-Build-the-Driver-Order-Page-for-Delivery-Control-React-TailwindCSS-Framer-Motion-Objective--1747874211427.txt", "8cd18cae-af48-4628-881a-aae2b3ddde1c:/home/<USER>/workspace/attached_assets/Pasted-Build-the-Manager-Kitchen-Page-for-Order-Status-Control-React-TailwindCSS-Framer-Motion-Obj-1747873346833.txt", "8cd18cae-af48-4628-881a-aae2b3ddde1c:/home/<USER>/workspace/attached_assets/Pasted-Build-the-Manager-Kitchen-Page-for-Order-Status-Control-React-TailwindCSS-Framer-Motion-Obj-1747873980911.txt", "8cd18cae-af48-4628-881a-aae2b3ddde1c:/home/<USER>/workspace/attached_assets/Pasted-Build-the-Manager-Kitchen-Page-for-Order-Status-Control-React-TailwindCSS-Framer-Motion-Obj-1747874192450.txt", "8cd18cae-af48-4628-881a-aae2b3ddde1c:/home/<USER>/workspace/attached_assets/Pasted-Build-the-Manager-Kitchen-Page-for-Order-Status-Control-React-TailwindCSS-Framer-Motion-Obj-1747874559621.txt", "8cd18cae-af48-4628-881a-aae2b3ddde1c:/home/<USER>/workspace/attached_assets/Pasted-Create-a-complete-dynamic-Menu-page-menu-for-a-luxurious-restaurant-website-using-React-Tail-1747869064381.txt", "8cd18cae-af48-4628-881a-aae2b3ddde1c:/home/<USER>/workspace/attached_assets/Pasted-Create-a-full-frontend-project-using-React-and-TailwindCSS-for-a-high-end-restaurant-website-1747868088702.txt", "8cd18cae-af48-4628-881a-aae2b3ddde1c:/home/<USER>/workspace/attached_assets/Pasted-Create-a-scalable-REST-API-using-Node-js-Express-and-PostgreSQL-for-a-restaurant-website-This-API-1747869135315.txt", "8cd18cae-af48-4628-881a-aae2b3ddde1c:/home/<USER>/workspace/attached_assets/Pasted-Generate-Seeder-Script-for-PostgreSQL-Menu-Categories-Settings-Orders-Prompt-csharp-Copy-Edit-1747872099827.txt", "8cd18cae-af48-4628-881a-aae2b3ddde1c:/home/<USER>/workspace/attached_assets/Pasted-Generate-Seeder-Script-for-PostgreSQL-Menu-Categories-Settings-Orders-Prompt-csharp-Copy-Edit-1747873285345.txt", "8cd18cae-af48-4628-881a-aae2b3ddde1c:/home/<USER>/workspace/attached_assets/Pasted-Redesign-the-Home-page-of-the-Barbecuez-Restaurant-website-using-React-TailwindCSS-to-re-1747869566148.txt", "8cd18cae-af48-4628-881a-aae2b3ddde1c:/home/<USER>/workspace/attached_assets/Pasted-Redesign-the-Menu-Page-to-provide-a-luxurious-animated-interactive-food-ordering-experience-that-a-1747869938419.txt", "8cd18cae-af48-4628-881a-aae2b3ddde1c:/home/<USER>/workspace/attached_assets/image_1747873176091.png", "8cd18cae-af48-4628-881a-aae2b3ddde1c:/home/<USER>/workspace/attached_assets/image_1747874186898.png", "8cd18cae-af48-4628-881a-aae2b3ddde1c:/home/<USER>/workspace/package.json", "8cd18cae-af48-4628-881a-aae2b3ddde1c:/home/<USER>/workspace/server/admin-api.ts", "8cd18cae-af48-4628-881a-aae2b3ddde1c:/home/<USER>/workspace/shared/schema.ts", "8cd18cae-af48-4628-881a-aae2b3ddde1c:/home/<USER>/workspace/migrations/003_add_order_details.sql", "8cd18cae-af48-4628-881a-aae2b3ddde1c:/home/<USER>/workspace/client/src/pages/admin/OrderManager.tsx", "8cd18cae-af48-4628-881a-aae2b3ddde1c:/home/<USER>/workspace/client/src/pages/driver/DriverPage.tsx", "8cd18cae-af48-4628-881a-aae2b3ddde1c:/home/<USER>/workspace/client/src/pages/manager/ManagerPage.tsx", "8cd18cae-af48-4628-881a-aae2b3ddde1c:/home/<USER>/workspace/server/storage.ts", "8cd18cae-af48-4628-881a-aae2b3ddde1c:/home/<USER>/workspace/client/src/api/customizationApi.ts", "8cd18cae-af48-4628-881a-aae2b3ddde1c:/home/<USER>/workspace/client/src/pages/admin/CustomizationManager.tsx", "8cd18cae-af48-4628-881a-aae2b3ddde1c:/home/<USER>/workspace/client/src/pages/admin/AdminLayout.tsx", "8cd18cae-af48-4628-881a-aae2b3ddde1c:/home/<USER>/workspace/client/src/App.tsx", "8cd18cae-af48-4628-881a-aae2b3ddde1c:/home/<USER>/workspace/server/routes.ts", "8cd18cae-af48-4628-881a-aae2b3ddde1c:/home/<USER>/workspace/client/src/components/CustomizeModal.tsx", "8cd18cae-af48-4628-881a-aae2b3ddde1c:/home/<USER>/workspace/server/auth.ts", "8cd18cae-af48-4628-881a-aae2b3ddde1c:/home/<USER>/workspace/test-customizations.js", "8cd18cae-af48-4628-881a-aae2b3ddde1c:/home/<USER>/workspace/fix-orders-table.js", "8cd18cae-af48-4628-881a-aae2b3ddde1c:/home/<USER>/workspace/server/index.ts", "8cd18cae-af48-4628-881a-aae2b3ddde1c:/home/<USER>/workspace/test-orders-fix.js"], "size": 3685474, "checkpointCount": 172, "lastModified": 1748083725804}, "shard-b5078cc1-8262-4251-ac4a-ee911cf891ed": {"checkpointDocumentIds": ["b5078cc1-8262-4251-ac4a-ee911cf891ed:/home/<USER>/workspace/server/storage.ts", "b5078cc1-8262-4251-ac4a-ee911cf891ed:/home/<USER>/workspace/client/src/api/adminApi.ts", "b5078cc1-8262-4251-ac4a-ee911cf891ed:/home/<USER>/workspace/client/src/api/api.ts", "b5078cc1-8262-4251-ac4a-ee911cf891ed:/home/<USER>/workspace/client/src/pages/Menu.tsx", "b5078cc1-8262-4251-ac4a-ee911cf891ed:/home/<USER>/workspace/client/src/pages/admin/Menu.tsx"], "size": 1537020, "checkpointCount": 27, "lastModified": 1748087062316}, "shard-171f58de-c287-4210-9a22-3dd69a12fcea": {"checkpointDocumentIds": ["171f58de-c287-4210-9a22-3dd69a12fcea:/home/<USER>/workspace/client/src/pages/Checkout.tsx", "171f58de-c287-4210-9a22-3dd69a12fcea:/home/<USER>/workspace/client/src/context/RestaurantStatusContext.tsx"], "size": 5103924, "checkpointCount": 31, "lastModified": 1748091141227}, "shard-38b76fa8-5500-4f8b-a458-fdb81cd17f53": {"checkpointDocumentIds": ["38b76fa8-5500-4f8b-a458-fdb81cd17f53:/home/<USER>/workspace/server/auth.ts", "38b76fa8-5500-4f8b-a458-fdb81cd17f53:/home/<USER>/workspace/server/routes.ts", "38b76fa8-5500-4f8b-a458-fdb81cd17f53:/home/<USER>/workspace/server/admin-api.ts", "38b76fa8-5500-4f8b-a458-fdb81cd17f53:/home/<USER>/workspace/client/src/pages/auth/AuthPage.tsx", "38b76fa8-5500-4f8b-a458-fdb81cd17f53:/home/<USER>/workspace/client/src/pages/auth/LogoutPage.tsx", "38b76fa8-5500-4f8b-a458-fdb81cd17f53:/home/<USER>/workspace/client/src/context/NotificationContext.tsx", "38b76fa8-5500-4f8b-a458-fdb81cd17f53:/home/<USER>/workspace/client/src/hooks/useAudioNotification.ts", "38b76fa8-5500-4f8b-a458-fdb81cd17f53:/home/<USER>/workspace/client/src/components/NotificationDropdown.tsx", "38b76fa8-5500-4f8b-a458-fdb81cd17f53:/home/<USER>/workspace/client/src/pages/manager/ManagerLayout.tsx", "38b76fa8-5500-4f8b-a458-fdb81cd17f53:/home/<USER>/workspace/client/src/pages/manager/ManagerPage.tsx", "38b76fa8-5500-4f8b-a458-fdb81cd17f53:/home/<USER>/workspace/client/src/App.tsx", "38b76fa8-5500-4f8b-a458-fdb81cd17f53:/home/<USER>/workspace/public/sounds/README.md", "38b76fa8-5500-4f8b-a458-fdb81cd17f53:/home/<USER>/workspace/client/src/utils/orderStatusWorkflow.ts", "38b76fa8-5500-4f8b-a458-fdb81cd17f53:/home/<USER>/workspace/client/src/pages/driver/DriverPage.tsx", "38b76fa8-5500-4f8b-a458-fdb81cd17f53:/home/<USER>/workspace/client/src/pages/driver/DriverLayout.tsx", "38b76fa8-5500-4f8b-a458-fdb81cd17f53:/home/<USER>/workspace/client/src/pages/RealOrderTracker.tsx"], "size": 1531841, "checkpointCount": 66, "lastModified": 1748095710755}}}