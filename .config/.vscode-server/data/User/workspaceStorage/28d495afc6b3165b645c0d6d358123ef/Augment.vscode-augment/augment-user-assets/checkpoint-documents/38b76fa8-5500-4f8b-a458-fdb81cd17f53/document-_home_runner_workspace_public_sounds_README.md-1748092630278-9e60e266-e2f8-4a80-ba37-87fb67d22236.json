{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "public/sounds/README.md"}, "modifiedCode": "# Notification Sounds\n\nThis directory contains audio files for the restaurant management system notifications.\n\n## Required Sound Files\n\n1. **new-order.mp3** - Played when a new order is received\n   - Should be a pleasant, attention-grabbing sound (like a gentle chime or bell)\n   - Duration: 1-3 seconds\n   - Volume: Moderate (not too loud or jarring)\n\n2. **status-update.mp3** - Played when an order status is updated\n   - Should be a subtle notification sound (like a soft ding)\n   - Duration: 0.5-2 seconds\n   - Volume: Softer than new-order sound\n\n3. **error.mp3** - Played when an error occurs\n   - Should be a distinctive but not alarming sound\n   - Duration: 1-2 seconds\n   - Volume: Moderate\n\n## Sound Requirements\n\n- Format: MP3 (for best browser compatibility)\n- Sample Rate: 44.1 kHz or 48 kHz\n- Bit Rate: 128-320 kbps\n- Mono or Stereo (mono preferred for smaller file size)\n\n## Recommended Sources\n\n- Free sounds: freesound.org, zapsplat.com\n- Royalty-free: audiojungle.net, pond5.com\n- Generate your own: audacity.org, online tone generators\n\n## Browser Compatibility\n\nThe audio notification system includes fallbacks:\n1. Primary: MP3 file playback\n2. Fallback: Programmatically generated beep sound\n3. Graceful degradation: Silent operation if audio fails\n\n## Volume Control\n\nUsers can toggle sound notifications on/off through the notification dropdown in the manager interface.\n"}