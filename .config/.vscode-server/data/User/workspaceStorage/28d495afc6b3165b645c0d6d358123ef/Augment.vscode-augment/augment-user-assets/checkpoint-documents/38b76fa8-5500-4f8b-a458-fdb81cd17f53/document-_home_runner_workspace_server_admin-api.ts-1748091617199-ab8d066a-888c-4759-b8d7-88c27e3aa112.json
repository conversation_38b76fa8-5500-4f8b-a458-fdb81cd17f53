{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "server/admin-api.ts"}, "originalCode": "import { Request, Response, Router } from 'express';\nimport { storage } from './storage';\n\nconst adminApiRouter = Router();\n\n// Admin Settings Endpoints\nadminApiRouter.get('/settings', async (req: Request, res: Response) => {\n  try {\n    const settings = await storage.getRestaurantSettings();\n\n    if (!settings) {\n      // Return default settings if none exist\n      const defaultSettings = {\n        id: 1,\n        restaurant_open: true,\n        business_hours: {\n          \"monday\": { \"open\": \"10:00\", \"close\": \"22:00\", \"delivery\": true },\n          \"tuesday\": { \"open\": \"10:00\", \"close\": \"22:00\", \"delivery\": true },\n          \"wednesday\": { \"open\": \"10:00\", \"close\": \"22:00\", \"delivery\": true },\n          \"thursday\": { \"open\": \"10:00\", \"close\": \"22:00\", \"delivery\": true },\n          \"friday\": { \"open\": \"10:00\", \"close\": \"23:00\", \"delivery\": true },\n          \"saturday\": { \"open\": \"10:00\", \"close\": \"23:00\", \"delivery\": true },\n          \"sunday\": { \"open\": \"12:00\", \"close\": \"21:00\", \"delivery\": true }\n        },\n        delivery_fee: 49,\n        estimated_time: \"25-35 min\"\n      };\n      return res.json(defaultSettings);\n    }\n\n    // Convert database format to API format\n    const apiSettings = {\n      id: settings.id,\n      restaurant_open: settings.restaurantOpen,\n      business_hours: settings.businessHours,\n      delivery_fee: settings.deliveryFee,\n      estimated_time: settings.estimatedTime\n    };\n\n    res.json(apiSettings);\n  } catch (error) {\n    console.error('Error fetching admin settings:', error);\n    res.status(500).json({ error: 'Failed to fetch admin settings' });\n  }\n});\n\nadminApiRouter.put('/settings', async (req: Request, res: Response) => {\n  try {\n    const { restaurant_open, business_hours, delivery_fee, estimated_time } = req.body;\n\n    // Convert API format to database format\n    const dbSettings = {\n      restaurantOpen: restaurant_open,\n      businessHours: business_hours,\n      deliveryFee: delivery_fee,\n      estimatedTime: estimated_time\n    };\n\n    const updatedSettings = await storage.updateRestaurantSettings(dbSettings);\n\n    if (!updatedSettings) {\n      return res.status(500).json({ error: 'Failed to update settings' });\n    }\n\n    // Convert back to API format for response\n    const apiSettings = {\n      id: updatedSettings.id,\n      restaurant_open: updatedSettings.restaurantOpen,\n      business_hours: updatedSettings.businessHours,\n      delivery_fee: updatedSettings.deliveryFee,\n      estimated_time: updatedSettings.estimatedTime\n    };\n\n    res.json(apiSettings);\n  } catch (error) {\n    console.error('Error updating admin settings:', error);\n    res.status(500).json({ error: 'Failed to update admin settings' });\n  }\n});\n\n// Analytics Endpoints\nadminApiRouter.get('/analytics/summary', async (req: Request, res: Response) => {\n  try {\n    const summary = {\n      today: 12500,\n      week: 87230,\n      month: 245890,\n      orderCount: 198\n    };\n\n    res.json(summary);\n  } catch (error) {\n    console.error('Error fetching analytics summary:', error);\n    res.status(500).json({ error: 'Failed to fetch analytics summary' });\n  }\n});\n\nadminApiRouter.get('/analytics/daily', async (req: Request, res: Response) => {\n  try {\n    const dailyRevenue = [\n      { date: '2023-05-15', total: 10200 },\n      { date: '2023-05-16', total: 11450 },\n      { date: '2023-05-17', total: 9870 },\n      { date: '2023-05-18', total: 12340 },\n      { date: '2023-05-19', total: 14560 },\n      { date: '2023-05-20', total: 15780 },\n      { date: '2023-05-21', total: 13030 }\n    ];\n\n    res.json(dailyRevenue);\n  } catch (error) {\n    console.error('Error fetching daily revenue:', error);\n    res.status(500).json({ error: 'Failed to fetch daily revenue' });\n  }\n});\n\nadminApiRouter.get('/analytics/categories', async (req: Request, res: Response) => {\n  try {\n    const categoryRevenue = [\n      { category: 'BBQ Mains', total: 98450 },\n      { category: 'Sides', total: 45670 },\n      { category: 'Burgers', total: 67890 },\n      { category: 'Drinks', total: 23450 },\n      { category: 'Desserts', total: 10430 }\n    ];\n\n    res.json(categoryRevenue);\n  } catch (error) {\n    console.error('Error fetching category revenue:', error);\n    res.status(500).json({ error: 'Failed to fetch category revenue' });\n  }\n});\n\n// Order Management API Routes\nadminApiRouter.get('/orders', async (req: Request, res: Response) => {\n  try {\n    const statusFilter = req.query.status as string;\n    // Get orders from database\n    let orders = await storage.getAllOrders();\n\n    // Apply status filtering if applicable\n    if (statusFilter && statusFilter !== 'all') {\n      if (statusFilter === 'active') {\n        // Active orders are those that are not completed or cancelled\n        orders = orders.filter(order =>\n          !['completed', 'cancelled'].includes(order.status)\n        );\n      } else {\n        orders = orders.filter(order => order.status === statusFilter);\n      }\n    }\n\n    res.json(orders);\n  } catch (error) {\n    console.error('Error fetching orders:', error);\n    res.status(500).json({ error: 'Failed to fetch orders' });\n  }\n});\n\nadminApiRouter.get('/orders/:id', async (req: Request, res: Response) => {\n  try {\n    const orderId = parseInt(req.params.id);\n    const order = await storage.getOrderById(orderId);\n\n    if (!order) {\n      return res.status(404).json({ error: 'Order not found' });\n    }\n\n    res.json(order);\n  } catch (error) {\n    console.error(`Error fetching order ${req.params.id}:`, error);\n    res.status(500).json({ error: 'Failed to fetch order' });\n  }\n});\n\nadminApiRouter.put('/orders/:id/status', async (req: Request, res: Response) => {\n  try {\n    const orderId = parseInt(req.params.id);\n    const { newStatus } = req.body;\n\n    if (!newStatus) {\n      return res.status(400).json({ error: 'New status is required' });\n    }\n\n    // Update the order status in the database\n    const updatedOrder = await storage.updateOrder(orderId, { status: newStatus });\n\n    if (!updatedOrder) {\n      return res.status(404).json({ error: 'Order not found' });\n    }\n\n    res.json({\n      success: true,\n      message: `Order ${orderId} status updated to ${newStatus}`,\n      order: updatedOrder\n    });\n  } catch (error) {\n    console.error(`Error updating order status for ${req.params.id}:`, error);\n    res.status(500).json({ error: 'Failed to update order status' });\n  }\n});\n\nadminApiRouter.post('/dispatch/to-driver', async (req: Request, res: Response) => {\n  try {\n    const { orderId } = req.body;\n\n    if (!orderId) {\n      return res.status(400).json({ error: 'Order ID is required' });\n    }\n\n    // In a real app, this would dispatch to a driver system\n    // For now, we just acknowledge receipt\n    res.json({\n      success: true,\n      message: `Order #${orderId} has been dispatched to a driver`,\n      timestamp: new Date().toISOString()\n    });\n  } catch (error) {\n    console.error('Error dispatching order to driver:', error);\n    res.status(500).json({ error: 'Failed to dispatch order to driver' });\n  }\n});\n\n// Category Management API Routes\nadminApiRouter.get('/categories', async (req: Request, res: Response) => {\n  try {\n    const categories = await storage.getAllMenuCategories();\n    res.json(categories);\n  } catch (error) {\n    console.error('Error fetching categories:', error);\n    res.status(500).json({ error: 'Failed to fetch categories' });\n  }\n});\n\nadminApiRouter.post('/categories', async (req: Request, res: Response) => {\n  try {\n    const { name, imageUrl } = req.body;\n\n    if (!name) {\n      return res.status(400).json({ error: 'Category name is required' });\n    }\n\n    const category = await storage.createMenuCategory({ name, imageUrl });\n    res.status(201).json(category);\n  } catch (error) {\n    console.error('Error creating category:', error);\n    res.status(500).json({ error: 'Failed to create category' });\n  }\n});\n\nadminApiRouter.put('/categories/:id', async (req: Request, res: Response) => {\n  try {\n    const id = parseInt(req.params.id);\n    const { name, imageUrl } = req.body;\n\n    if (!name) {\n      return res.status(400).json({ error: 'Category name is required' });\n    }\n\n    const category = await storage.updateMenuCategory(id, { name, imageUrl });\n\n    if (!category) {\n      return res.status(404).json({ error: 'Category not found' });\n    }\n\n    res.json(category);\n  } catch (error) {\n    console.error('Error updating category:', error);\n    res.status(500).json({ error: 'Failed to update category' });\n  }\n});\n\nadminApiRouter.delete('/categories/:id', async (req: Request, res: Response) => {\n  try {\n    const id = parseInt(req.params.id);\n    const success = await storage.deleteMenuCategory(id);\n\n    if (!success) {\n      return res.status(404).json({ error: 'Category not found' });\n    }\n\n    res.json({ message: 'Category deleted successfully' });\n  } catch (error) {\n    console.error('Error deleting category:', error);\n    res.status(500).json({ error: 'Failed to delete category' });\n  }\n});\n\n// Menu Item Management API Routes\nadminApiRouter.get('/items', async (req: Request, res: Response) => {\n  try {\n    const items = await storage.getAllMenuItems();\n    res.json(items);\n  } catch (error) {\n    console.error('Error fetching menu items:', error);\n    res.status(500).json({ error: 'Failed to fetch menu items' });\n  }\n});\n\nadminApiRouter.post('/items', async (req: Request, res: Response) => {\n  try {\n    const { name, description, price, imageUrl, categoryId, available } = req.body;\n\n    if (!name || !price) {\n      return res.status(400).json({ error: 'Name and price are required' });\n    }\n\n    const item = await storage.createMenuItem({\n      name,\n      description,\n      price,\n      imageUrl,\n      categoryId,\n      available: available !== undefined ? available : true,\n      rating: 0,\n      reviews: 0\n    });\n\n    res.status(201).json(item);\n  } catch (error) {\n    console.error('Error creating menu item:', error);\n    res.status(500).json({ error: 'Failed to create menu item' });\n  }\n});\n\nadminApiRouter.put('/items/:id', async (req: Request, res: Response) => {\n  try {\n    const id = parseInt(req.params.id);\n    const { name, description, price, imageUrl, categoryId, available } = req.body;\n\n    if (!name || !price) {\n      return res.status(400).json({ error: 'Name and price are required' });\n    }\n\n    const item = await storage.updateMenuItem(id, {\n      name,\n      description,\n      price,\n      imageUrl,\n      categoryId,\n      available\n    });\n\n    if (!item) {\n      return res.status(404).json({ error: 'Menu item not found' });\n    }\n\n    res.json(item);\n  } catch (error) {\n    console.error('Error updating menu item:', error);\n    res.status(500).json({ error: 'Failed to update menu item' });\n  }\n});\n\nadminApiRouter.delete('/items/:id', async (req: Request, res: Response) => {\n  try {\n    const id = parseInt(req.params.id);\n    const success = await storage.deleteMenuItem(id);\n\n    if (!success) {\n      return res.status(404).json({ error: 'Menu item not found' });\n    }\n\n    res.json({ message: 'Menu item deleted successfully' });\n  } catch (error) {\n    console.error('Error deleting menu item:', error);\n    res.status(500).json({ error: 'Failed to delete menu item' });\n  }\n});\n\n// Customization Groups API Routes\nadminApiRouter.get('/customization-groups', async (req: Request, res: Response) => {\n  try {\n    const groups = await storage.getAllCustomizationGroups();\n    res.json(groups);\n  } catch (error) {\n    console.error('Error fetching customization groups:', error);\n    res.status(500).json({ error: 'Failed to fetch customization groups' });\n  }\n});\n\nadminApiRouter.post('/customization-groups', async (req: Request, res: Response) => {\n  try {\n    const { title } = req.body;\n\n    if (!title) {\n      return res.status(400).json({ error: 'Group title is required' });\n    }\n\n    const newGroup = await storage.createCustomizationGroup({ title });\n    res.status(201).json(newGroup);\n  } catch (error) {\n    console.error('Error creating customization group:', error);\n    res.status(500).json({ error: 'Failed to create customization group' });\n  }\n});\n\nadminApiRouter.put('/customization-groups/:id', async (req: Request, res: Response) => {\n  try {\n    const id = parseInt(req.params.id);\n    const { title } = req.body;\n\n    if (!title) {\n      return res.status(400).json({ error: 'Group title is required' });\n    }\n\n    const updatedGroup = await storage.updateCustomizationGroup(id, { title });\n\n    if (!updatedGroup) {\n      return res.status(404).json({ error: 'Customization group not found' });\n    }\n\n    res.json(updatedGroup);\n  } catch (error) {\n    console.error('Error updating customization group:', error);\n    res.status(500).json({ error: 'Failed to update customization group' });\n  }\n});\n\nadminApiRouter.delete('/customization-groups/:id', async (req: Request, res: Response) => {\n  try {\n    const id = parseInt(req.params.id);\n    const success = await storage.deleteCustomizationGroup(id);\n\n    if (!success) {\n      return res.status(404).json({ error: 'Customization group not found' });\n    }\n\n    res.json({ message: 'Customization group deleted successfully' });\n  } catch (error) {\n    console.error('Error deleting customization group:', error);\n    res.status(500).json({ error: 'Failed to delete customization group' });\n  }\n});\n\n// Customization Options API Routes\nadminApiRouter.get('/customization-options', async (req: Request, res: Response) => {\n  try {\n    const options = await storage.getAllCustomizationOptions();\n    res.json(options);\n  } catch (error) {\n    console.error('Error fetching customization options:', error);\n    res.status(500).json({ error: 'Failed to fetch customization options' });\n  }\n});\n\nadminApiRouter.get('/customization-options/group/:groupId', async (req: Request, res: Response) => {\n  try {\n    const groupId = parseInt(req.params.groupId);\n    const options = await storage.getCustomizationOptionsByGroup(groupId);\n    res.json(options);\n  } catch (error) {\n    console.error('Error fetching customization options for group:', error);\n    res.status(500).json({ error: 'Failed to fetch customization options for group' });\n  }\n});\n\nadminApiRouter.post('/customization-options', async (req: Request, res: Response) => {\n  try {\n    const { name, extraPrice, imageUrl, groupId } = req.body;\n\n    if (!name || !groupId) {\n      return res.status(400).json({ error: 'Option name and group ID are required' });\n    }\n\n    const newOption = await storage.createCustomizationOption({\n      name,\n      extraPrice: extraPrice || 0,\n      imageUrl: imageUrl || \"\",\n      groupId\n    });\n\n    res.status(201).json(newOption);\n  } catch (error) {\n    console.error('Error creating customization option:', error);\n    res.status(500).json({ error: 'Failed to create customization option' });\n  }\n});\n\nadminApiRouter.put('/customization-options/:id', async (req: Request, res: Response) => {\n  try {\n    const id = parseInt(req.params.id);\n    const { name, extraPrice, imageUrl, groupId } = req.body;\n\n    const updatedOption = await storage.updateCustomizationOption(id, {\n      name,\n      extraPrice,\n      imageUrl,\n      groupId\n    });\n\n    if (!updatedOption) {\n      return res.status(404).json({ error: 'Customization option not found' });\n    }\n\n    res.json(updatedOption);\n  } catch (error) {\n    console.error('Error updating customization option:', error);\n    res.status(500).json({ error: 'Failed to update customization option' });\n  }\n});\n\nadminApiRouter.delete('/customization-options/:id', async (req: Request, res: Response) => {\n  try {\n    const id = parseInt(req.params.id);\n    const success = await storage.deleteCustomizationOption(id);\n\n    if (!success) {\n      return res.status(404).json({ error: 'Customization option not found' });\n    }\n\n    res.json({ message: 'Customization option deleted successfully' });\n  } catch (error) {\n    console.error('Error deleting customization option:', error);\n    res.status(500).json({ error: 'Failed to delete customization option' });\n  }\n});\n\n// Item Customization Mapping API Routes\nadminApiRouter.get('/items/:itemId/customizations', async (req: Request, res: Response) => {\n  try {\n    const itemId = parseInt(req.params.itemId);\n    const customizations = await storage.getCustomizationOptionsForMenuItem(itemId);\n    res.json(customizations);\n  } catch (error) {\n    console.error('Error fetching customizations for menu item:', error);\n    res.status(500).json({ error: 'Failed to fetch customizations for menu item' });\n  }\n});\n\nadminApiRouter.post('/items/:itemId/customizations/:optionId', async (req: Request, res: Response) => {\n  try {\n    const itemId = parseInt(req.params.itemId);\n    const optionId = parseInt(req.params.optionId);\n\n    const mapping = await storage.mapCustomizationOptionToMenuItem(itemId, optionId);\n    res.status(201).json(mapping);\n  } catch (error) {\n    console.error('Error mapping customization to menu item:', error);\n    res.status(500).json({ error: 'Failed to map customization to menu item' });\n  }\n});\n\nadminApiRouter.delete('/items/:itemId/customizations/:optionId', async (req: Request, res: Response) => {\n  try {\n    const itemId = parseInt(req.params.itemId);\n    const optionId = parseInt(req.params.optionId);\n\n    const success = await storage.unmapCustomizationOptionFromMenuItem(itemId, optionId);\n\n    if (!success) {\n      return res.status(404).json({ error: 'Customization mapping not found' });\n    }\n\n    res.json({ message: 'Customization mapping removed successfully' });\n  } catch (error) {\n    console.error('Error removing customization mapping:', error);\n    res.status(500).json({ error: 'Failed to remove customization mapping' });\n  }\n});\n\nexport default adminApiRouter;", "modifiedCode": "import { Request, Response, Router } from 'express';\nimport { storage } from './storage';\nimport { isStaff } from './auth';\n\nconst adminApiRouter = Router();\n\n// Admin Settings Endpoints\nadminApiRouter.get('/settings', async (req: Request, res: Response) => {\n  try {\n    const settings = await storage.getRestaurantSettings();\n\n    if (!settings) {\n      // Return default settings if none exist\n      const defaultSettings = {\n        id: 1,\n        restaurant_open: true,\n        business_hours: {\n          \"monday\": { \"open\": \"10:00\", \"close\": \"22:00\", \"delivery\": true },\n          \"tuesday\": { \"open\": \"10:00\", \"close\": \"22:00\", \"delivery\": true },\n          \"wednesday\": { \"open\": \"10:00\", \"close\": \"22:00\", \"delivery\": true },\n          \"thursday\": { \"open\": \"10:00\", \"close\": \"22:00\", \"delivery\": true },\n          \"friday\": { \"open\": \"10:00\", \"close\": \"23:00\", \"delivery\": true },\n          \"saturday\": { \"open\": \"10:00\", \"close\": \"23:00\", \"delivery\": true },\n          \"sunday\": { \"open\": \"12:00\", \"close\": \"21:00\", \"delivery\": true }\n        },\n        delivery_fee: 49,\n        estimated_time: \"25-35 min\"\n      };\n      return res.json(defaultSettings);\n    }\n\n    // Convert database format to API format\n    const apiSettings = {\n      id: settings.id,\n      restaurant_open: settings.restaurantOpen,\n      business_hours: settings.businessHours,\n      delivery_fee: settings.deliveryFee,\n      estimated_time: settings.estimatedTime\n    };\n\n    res.json(apiSettings);\n  } catch (error) {\n    console.error('Error fetching admin settings:', error);\n    res.status(500).json({ error: 'Failed to fetch admin settings' });\n  }\n});\n\nadminApiRouter.put('/settings', async (req: Request, res: Response) => {\n  try {\n    const { restaurant_open, business_hours, delivery_fee, estimated_time } = req.body;\n\n    // Convert API format to database format\n    const dbSettings = {\n      restaurantOpen: restaurant_open,\n      businessHours: business_hours,\n      deliveryFee: delivery_fee,\n      estimatedTime: estimated_time\n    };\n\n    const updatedSettings = await storage.updateRestaurantSettings(dbSettings);\n\n    if (!updatedSettings) {\n      return res.status(500).json({ error: 'Failed to update settings' });\n    }\n\n    // Convert back to API format for response\n    const apiSettings = {\n      id: updatedSettings.id,\n      restaurant_open: updatedSettings.restaurantOpen,\n      business_hours: updatedSettings.businessHours,\n      delivery_fee: updatedSettings.deliveryFee,\n      estimated_time: updatedSettings.estimatedTime\n    };\n\n    res.json(apiSettings);\n  } catch (error) {\n    console.error('Error updating admin settings:', error);\n    res.status(500).json({ error: 'Failed to update admin settings' });\n  }\n});\n\n// Analytics Endpoints\nadminApiRouter.get('/analytics/summary', async (req: Request, res: Response) => {\n  try {\n    const summary = {\n      today: 12500,\n      week: 87230,\n      month: 245890,\n      orderCount: 198\n    };\n\n    res.json(summary);\n  } catch (error) {\n    console.error('Error fetching analytics summary:', error);\n    res.status(500).json({ error: 'Failed to fetch analytics summary' });\n  }\n});\n\nadminApiRouter.get('/analytics/daily', async (req: Request, res: Response) => {\n  try {\n    const dailyRevenue = [\n      { date: '2023-05-15', total: 10200 },\n      { date: '2023-05-16', total: 11450 },\n      { date: '2023-05-17', total: 9870 },\n      { date: '2023-05-18', total: 12340 },\n      { date: '2023-05-19', total: 14560 },\n      { date: '2023-05-20', total: 15780 },\n      { date: '2023-05-21', total: 13030 }\n    ];\n\n    res.json(dailyRevenue);\n  } catch (error) {\n    console.error('Error fetching daily revenue:', error);\n    res.status(500).json({ error: 'Failed to fetch daily revenue' });\n  }\n});\n\nadminApiRouter.get('/analytics/categories', async (req: Request, res: Response) => {\n  try {\n    const categoryRevenue = [\n      { category: 'BBQ Mains', total: 98450 },\n      { category: 'Sides', total: 45670 },\n      { category: 'Burgers', total: 67890 },\n      { category: 'Drinks', total: 23450 },\n      { category: 'Desserts', total: 10430 }\n    ];\n\n    res.json(categoryRevenue);\n  } catch (error) {\n    console.error('Error fetching category revenue:', error);\n    res.status(500).json({ error: 'Failed to fetch category revenue' });\n  }\n});\n\n// Order Management API Routes\nadminApiRouter.get('/orders', async (req: Request, res: Response) => {\n  try {\n    const statusFilter = req.query.status as string;\n    // Get orders from database\n    let orders = await storage.getAllOrders();\n\n    // Apply status filtering if applicable\n    if (statusFilter && statusFilter !== 'all') {\n      if (statusFilter === 'active') {\n        // Active orders are those that are not completed or cancelled\n        orders = orders.filter(order =>\n          !['completed', 'cancelled'].includes(order.status)\n        );\n      } else {\n        orders = orders.filter(order => order.status === statusFilter);\n      }\n    }\n\n    res.json(orders);\n  } catch (error) {\n    console.error('Error fetching orders:', error);\n    res.status(500).json({ error: 'Failed to fetch orders' });\n  }\n});\n\nadminApiRouter.get('/orders/:id', async (req: Request, res: Response) => {\n  try {\n    const orderId = parseInt(req.params.id);\n    const order = await storage.getOrderById(orderId);\n\n    if (!order) {\n      return res.status(404).json({ error: 'Order not found' });\n    }\n\n    res.json(order);\n  } catch (error) {\n    console.error(`Error fetching order ${req.params.id}:`, error);\n    res.status(500).json({ error: 'Failed to fetch order' });\n  }\n});\n\nadminApiRouter.put('/orders/:id/status', async (req: Request, res: Response) => {\n  try {\n    const orderId = parseInt(req.params.id);\n    const { newStatus } = req.body;\n\n    if (!newStatus) {\n      return res.status(400).json({ error: 'New status is required' });\n    }\n\n    // Update the order status in the database\n    const updatedOrder = await storage.updateOrder(orderId, { status: newStatus });\n\n    if (!updatedOrder) {\n      return res.status(404).json({ error: 'Order not found' });\n    }\n\n    res.json({\n      success: true,\n      message: `Order ${orderId} status updated to ${newStatus}`,\n      order: updatedOrder\n    });\n  } catch (error) {\n    console.error(`Error updating order status for ${req.params.id}:`, error);\n    res.status(500).json({ error: 'Failed to update order status' });\n  }\n});\n\nadminApiRouter.post('/dispatch/to-driver', async (req: Request, res: Response) => {\n  try {\n    const { orderId } = req.body;\n\n    if (!orderId) {\n      return res.status(400).json({ error: 'Order ID is required' });\n    }\n\n    // In a real app, this would dispatch to a driver system\n    // For now, we just acknowledge receipt\n    res.json({\n      success: true,\n      message: `Order #${orderId} has been dispatched to a driver`,\n      timestamp: new Date().toISOString()\n    });\n  } catch (error) {\n    console.error('Error dispatching order to driver:', error);\n    res.status(500).json({ error: 'Failed to dispatch order to driver' });\n  }\n});\n\n// Category Management API Routes\nadminApiRouter.get('/categories', async (req: Request, res: Response) => {\n  try {\n    const categories = await storage.getAllMenuCategories();\n    res.json(categories);\n  } catch (error) {\n    console.error('Error fetching categories:', error);\n    res.status(500).json({ error: 'Failed to fetch categories' });\n  }\n});\n\nadminApiRouter.post('/categories', async (req: Request, res: Response) => {\n  try {\n    const { name, imageUrl } = req.body;\n\n    if (!name) {\n      return res.status(400).json({ error: 'Category name is required' });\n    }\n\n    const category = await storage.createMenuCategory({ name, imageUrl });\n    res.status(201).json(category);\n  } catch (error) {\n    console.error('Error creating category:', error);\n    res.status(500).json({ error: 'Failed to create category' });\n  }\n});\n\nadminApiRouter.put('/categories/:id', async (req: Request, res: Response) => {\n  try {\n    const id = parseInt(req.params.id);\n    const { name, imageUrl } = req.body;\n\n    if (!name) {\n      return res.status(400).json({ error: 'Category name is required' });\n    }\n\n    const category = await storage.updateMenuCategory(id, { name, imageUrl });\n\n    if (!category) {\n      return res.status(404).json({ error: 'Category not found' });\n    }\n\n    res.json(category);\n  } catch (error) {\n    console.error('Error updating category:', error);\n    res.status(500).json({ error: 'Failed to update category' });\n  }\n});\n\nadminApiRouter.delete('/categories/:id', async (req: Request, res: Response) => {\n  try {\n    const id = parseInt(req.params.id);\n    const success = await storage.deleteMenuCategory(id);\n\n    if (!success) {\n      return res.status(404).json({ error: 'Category not found' });\n    }\n\n    res.json({ message: 'Category deleted successfully' });\n  } catch (error) {\n    console.error('Error deleting category:', error);\n    res.status(500).json({ error: 'Failed to delete category' });\n  }\n});\n\n// Menu Item Management API Routes\nadminApiRouter.get('/items', async (req: Request, res: Response) => {\n  try {\n    const items = await storage.getAllMenuItems();\n    res.json(items);\n  } catch (error) {\n    console.error('Error fetching menu items:', error);\n    res.status(500).json({ error: 'Failed to fetch menu items' });\n  }\n});\n\nadminApiRouter.post('/items', async (req: Request, res: Response) => {\n  try {\n    const { name, description, price, imageUrl, categoryId, available } = req.body;\n\n    if (!name || !price) {\n      return res.status(400).json({ error: 'Name and price are required' });\n    }\n\n    const item = await storage.createMenuItem({\n      name,\n      description,\n      price,\n      imageUrl,\n      categoryId,\n      available: available !== undefined ? available : true,\n      rating: 0,\n      reviews: 0\n    });\n\n    res.status(201).json(item);\n  } catch (error) {\n    console.error('Error creating menu item:', error);\n    res.status(500).json({ error: 'Failed to create menu item' });\n  }\n});\n\nadminApiRouter.put('/items/:id', async (req: Request, res: Response) => {\n  try {\n    const id = parseInt(req.params.id);\n    const { name, description, price, imageUrl, categoryId, available } = req.body;\n\n    if (!name || !price) {\n      return res.status(400).json({ error: 'Name and price are required' });\n    }\n\n    const item = await storage.updateMenuItem(id, {\n      name,\n      description,\n      price,\n      imageUrl,\n      categoryId,\n      available\n    });\n\n    if (!item) {\n      return res.status(404).json({ error: 'Menu item not found' });\n    }\n\n    res.json(item);\n  } catch (error) {\n    console.error('Error updating menu item:', error);\n    res.status(500).json({ error: 'Failed to update menu item' });\n  }\n});\n\nadminApiRouter.delete('/items/:id', async (req: Request, res: Response) => {\n  try {\n    const id = parseInt(req.params.id);\n    const success = await storage.deleteMenuItem(id);\n\n    if (!success) {\n      return res.status(404).json({ error: 'Menu item not found' });\n    }\n\n    res.json({ message: 'Menu item deleted successfully' });\n  } catch (error) {\n    console.error('Error deleting menu item:', error);\n    res.status(500).json({ error: 'Failed to delete menu item' });\n  }\n});\n\n// Customization Groups API Routes\nadminApiRouter.get('/customization-groups', async (req: Request, res: Response) => {\n  try {\n    const groups = await storage.getAllCustomizationGroups();\n    res.json(groups);\n  } catch (error) {\n    console.error('Error fetching customization groups:', error);\n    res.status(500).json({ error: 'Failed to fetch customization groups' });\n  }\n});\n\nadminApiRouter.post('/customization-groups', async (req: Request, res: Response) => {\n  try {\n    const { title } = req.body;\n\n    if (!title) {\n      return res.status(400).json({ error: 'Group title is required' });\n    }\n\n    const newGroup = await storage.createCustomizationGroup({ title });\n    res.status(201).json(newGroup);\n  } catch (error) {\n    console.error('Error creating customization group:', error);\n    res.status(500).json({ error: 'Failed to create customization group' });\n  }\n});\n\nadminApiRouter.put('/customization-groups/:id', async (req: Request, res: Response) => {\n  try {\n    const id = parseInt(req.params.id);\n    const { title } = req.body;\n\n    if (!title) {\n      return res.status(400).json({ error: 'Group title is required' });\n    }\n\n    const updatedGroup = await storage.updateCustomizationGroup(id, { title });\n\n    if (!updatedGroup) {\n      return res.status(404).json({ error: 'Customization group not found' });\n    }\n\n    res.json(updatedGroup);\n  } catch (error) {\n    console.error('Error updating customization group:', error);\n    res.status(500).json({ error: 'Failed to update customization group' });\n  }\n});\n\nadminApiRouter.delete('/customization-groups/:id', async (req: Request, res: Response) => {\n  try {\n    const id = parseInt(req.params.id);\n    const success = await storage.deleteCustomizationGroup(id);\n\n    if (!success) {\n      return res.status(404).json({ error: 'Customization group not found' });\n    }\n\n    res.json({ message: 'Customization group deleted successfully' });\n  } catch (error) {\n    console.error('Error deleting customization group:', error);\n    res.status(500).json({ error: 'Failed to delete customization group' });\n  }\n});\n\n// Customization Options API Routes\nadminApiRouter.get('/customization-options', async (req: Request, res: Response) => {\n  try {\n    const options = await storage.getAllCustomizationOptions();\n    res.json(options);\n  } catch (error) {\n    console.error('Error fetching customization options:', error);\n    res.status(500).json({ error: 'Failed to fetch customization options' });\n  }\n});\n\nadminApiRouter.get('/customization-options/group/:groupId', async (req: Request, res: Response) => {\n  try {\n    const groupId = parseInt(req.params.groupId);\n    const options = await storage.getCustomizationOptionsByGroup(groupId);\n    res.json(options);\n  } catch (error) {\n    console.error('Error fetching customization options for group:', error);\n    res.status(500).json({ error: 'Failed to fetch customization options for group' });\n  }\n});\n\nadminApiRouter.post('/customization-options', async (req: Request, res: Response) => {\n  try {\n    const { name, extraPrice, imageUrl, groupId } = req.body;\n\n    if (!name || !groupId) {\n      return res.status(400).json({ error: 'Option name and group ID are required' });\n    }\n\n    const newOption = await storage.createCustomizationOption({\n      name,\n      extraPrice: extraPrice || 0,\n      imageUrl: imageUrl || \"\",\n      groupId\n    });\n\n    res.status(201).json(newOption);\n  } catch (error) {\n    console.error('Error creating customization option:', error);\n    res.status(500).json({ error: 'Failed to create customization option' });\n  }\n});\n\nadminApiRouter.put('/customization-options/:id', async (req: Request, res: Response) => {\n  try {\n    const id = parseInt(req.params.id);\n    const { name, extraPrice, imageUrl, groupId } = req.body;\n\n    const updatedOption = await storage.updateCustomizationOption(id, {\n      name,\n      extraPrice,\n      imageUrl,\n      groupId\n    });\n\n    if (!updatedOption) {\n      return res.status(404).json({ error: 'Customization option not found' });\n    }\n\n    res.json(updatedOption);\n  } catch (error) {\n    console.error('Error updating customization option:', error);\n    res.status(500).json({ error: 'Failed to update customization option' });\n  }\n});\n\nadminApiRouter.delete('/customization-options/:id', async (req: Request, res: Response) => {\n  try {\n    const id = parseInt(req.params.id);\n    const success = await storage.deleteCustomizationOption(id);\n\n    if (!success) {\n      return res.status(404).json({ error: 'Customization option not found' });\n    }\n\n    res.json({ message: 'Customization option deleted successfully' });\n  } catch (error) {\n    console.error('Error deleting customization option:', error);\n    res.status(500).json({ error: 'Failed to delete customization option' });\n  }\n});\n\n// Item Customization Mapping API Routes\nadminApiRouter.get('/items/:itemId/customizations', async (req: Request, res: Response) => {\n  try {\n    const itemId = parseInt(req.params.itemId);\n    const customizations = await storage.getCustomizationOptionsForMenuItem(itemId);\n    res.json(customizations);\n  } catch (error) {\n    console.error('Error fetching customizations for menu item:', error);\n    res.status(500).json({ error: 'Failed to fetch customizations for menu item' });\n  }\n});\n\nadminApiRouter.post('/items/:itemId/customizations/:optionId', async (req: Request, res: Response) => {\n  try {\n    const itemId = parseInt(req.params.itemId);\n    const optionId = parseInt(req.params.optionId);\n\n    const mapping = await storage.mapCustomizationOptionToMenuItem(itemId, optionId);\n    res.status(201).json(mapping);\n  } catch (error) {\n    console.error('Error mapping customization to menu item:', error);\n    res.status(500).json({ error: 'Failed to map customization to menu item' });\n  }\n});\n\nadminApiRouter.delete('/items/:itemId/customizations/:optionId', async (req: Request, res: Response) => {\n  try {\n    const itemId = parseInt(req.params.itemId);\n    const optionId = parseInt(req.params.optionId);\n\n    const success = await storage.unmapCustomizationOptionFromMenuItem(itemId, optionId);\n\n    if (!success) {\n      return res.status(404).json({ error: 'Customization mapping not found' });\n    }\n\n    res.json({ message: 'Customization mapping removed successfully' });\n  } catch (error) {\n    console.error('Error removing customization mapping:', error);\n    res.status(500).json({ error: 'Failed to remove customization mapping' });\n  }\n});\n\nexport default adminApiRouter;"}