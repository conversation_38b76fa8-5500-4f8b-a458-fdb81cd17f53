{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/OrderConfirmation.tsx"}, "originalCode": "import { useState, useEffect } from \"react\";\nimport { useLocation, Link } from \"wouter\";\nimport { motion, AnimatePresence } from \"framer-motion\";\n\ninterface OrderConfirmationProps {\n  params?: {\n    orderId?: string;\n  };\n}\n\nconst OrderConfirmation = (props: OrderConfirmationProps) => {\n  const orderId = props.params?.orderId || \"BBC\" + Math.floor(Math.random() * 100000);\n  const [, setLocation] = useLocation();\n  const [showConfetti, setShowConfetti] = useState(true);\n  const [countdown, setCountdown] = useState(3);\n  const [isRedirecting, setIsRedirecting] = useState(false);\n\n  // Estimated delivery time calculation (30-45 minutes from now)\n  const now = new Date();\n  const deliveryTime = new Date(now.getTime() + 45 * 60000);\n  const deliveryTimeString = deliveryTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });\n\n  // Countdown timer and redirect logic\n  useEffect(() => {\n    // Start countdown after 2 seconds to let user read the confirmation\n    const startCountdownTimer = setTimeout(() => {\n      const countdownInterval = setInterval(() => {\n        setCountdown((prev) => {\n          if (prev <= 1) {\n            clearInterval(countdownInterval);\n            handleRedirectToTracking();\n            return 0;\n          }\n          return prev - 1;\n        });\n      }, 1000);\n\n      return () => clearInterval(countdownInterval);\n    }, 2000);\n\n    return () => clearTimeout(startCountdownTimer);\n  }, []);\n\n  // Function to handle redirect to order tracking\n  const handleRedirectToTracking = () => {\n    setIsRedirecting(true);\n\n    // Validate order ID before redirecting\n    if (!orderId || orderId === \"BBC\" + Math.floor(Math.random() * 100000)) {\n      // If no valid order ID, redirect to a general order lookup page\n      setLocation('/track-order');\n      return;\n    }\n\n    // Redirect to real order tracking page\n    setLocation(`/track-order/${orderId}`);\n  };\n\n  // Function to skip countdown and go directly to tracking\n  const handleSkipCountdown = () => {\n    setCountdown(0);\n    handleRedirectToTracking();\n  };\n\n  // Animation variants\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        delayChildren: 0.3,\n        staggerChildren: 0.2\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { y: 20, opacity: 0 },\n    visible: {\n      y: 0,\n      opacity: 1,\n      transition: {\n        type: \"spring\",\n        stiffness: 100,\n        damping: 10\n      }\n    }\n  };\n\n  const pulseVariants = {\n    initial: { scale: 1, boxShadow: \"0 0 0px rgba(57, 255, 20, 0)\" },\n    animate: {\n      scale: [1, 1.05, 1],\n      boxShadow: [\n        \"0 0 0px rgba(57, 255, 20, 0)\",\n        \"0 0 20px rgba(57, 255, 20, 0.7)\",\n        \"0 0 0px rgba(57, 255, 20, 0)\"\n      ],\n      transition: {\n        duration: 2,\n        repeat: Infinity,\n        repeatType: \"loop\"\n      }\n    }\n  };\n\n  // Disable confetti after a few seconds\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      setShowConfetti(false);\n    }, 5000);\n\n    return () => clearTimeout(timer);\n  }, []);\n\n  return (\n    <main className=\"min-h-screen bg-black relative overflow-hidden flex items-center justify-center py-12\">\n      {/* Animated Gradient Background */}\n      <div\n        className=\"absolute inset-0 z-0\"\n        style={{\n          background: \"radial-gradient(circle at 20% 20%, rgba(0, 255, 255, 0.03), transparent 33%), \" +\n                    \"radial-gradient(circle at 80% 80%, rgba(57, 255, 20, 0.03), transparent 33%), \" +\n                    \"radial-gradient(circle at 50% 50%, rgba(255, 0, 255, 0.03), rgba(0, 0, 0, 1) 100%)\",\n        }}\n      ></div>\n\n      {/* Neon Grid Overlay */}\n      <div className=\"absolute inset-0 z-0 opacity-[0.03]\"\n           style={{\n             backgroundImage: \"linear-gradient(to right, #39FF14 1px, transparent 1px), linear-gradient(to bottom, #39FF14 1px, transparent 1px)\",\n             backgroundSize: \"40px 40px\"\n           }}>\n      </div>\n\n      {/* Confetti Animation */}\n      {showConfetti && (\n        <div className=\"absolute inset-0 pointer-events-none z-10\">\n          {[...Array(40)].map((_, i) => (\n            <motion.div\n              key={i}\n              className=\"absolute w-3 h-3 rounded-full\"\n              style={{\n                backgroundColor:\n                  i % 3 === 0 ? 'rgba(57, 255, 20, 0.7)' :\n                  i % 3 === 1 ? 'rgba(0, 255, 255, 0.7)' :\n                  'rgba(255, 0, 255, 0.7)',\n                top: `${Math.random() * -10}%`,\n                left: `${Math.random() * 100}%`,\n              }}\n              initial={{\n                y: -20,\n                opacity: 0,\n                scale: 0\n              }}\n              animate={{\n                y: `${100 + Math.random() * 50}vh`,\n                opacity: [1, 0.8, 0],\n                scale: [1, 0.8, 0.6],\n                rotate: [0, Math.random() * 360]\n              }}\n              transition={{\n                duration: 2.5 + Math.random() * 3.5,\n                delay: Math.random() * 3,\n                ease: \"easeOut\",\n                repeat: 1,\n                repeatType: \"loop\",\n                repeatDelay: Math.random() * 2\n              }}\n            />\n          ))}\n        </div>\n      )}\n\n      <motion.div\n        className=\"container mx-auto px-4 z-10 relative\"\n        variants={containerVariants}\n        initial=\"hidden\"\n        animate=\"visible\"\n      >\n        <div className=\"max-w-xl mx-auto\">\n          <motion.div\n            className=\"bg-black/30 backdrop-blur-sm rounded-2xl p-8 md:p-12 border border-lime-800/30\n                       shadow-[0_0_30px_rgba(57,255,20,0.2)]\"\n            initial={{ opacity: 0, scale: 0.9 }}\n            animate={{ opacity: 1, scale: 1 }}\n            transition={{ duration: 0.6 }}\n          >\n            <div className=\"text-center\">\n              {/* Success Icon */}\n              <motion.div\n                className=\"w-28 h-28 mx-auto mb-8 rounded-full bg-gradient-to-br from-lime-500/20 to-green-700/20\n                         flex items-center justify-center\"\n                initial={{ scale: 0 }}\n                animate={{ scale: 1 }}\n                transition={{\n                  type: \"spring\",\n                  stiffness: 200,\n                  damping: 15,\n                  delay: 0.2\n                }}\n              >\n                <motion.div\n                  className=\"w-24 h-24 rounded-full bg-gradient-to-br from-lime-500/30 to-green-700/30\n                           flex items-center justify-center\"\n                  variants={pulseVariants}\n                  initial=\"initial\"\n                  animate=\"animate\"\n                >\n                  <svg className=\"w-16 h-16 text-lime-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <motion.path\n                      strokeLinecap=\"round\"\n                      strokeLinejoin=\"round\"\n                      strokeWidth={2.5}\n                      d=\"M5 13l4 4L19 7\"\n                      initial={{ pathLength: 0 }}\n                      animate={{ pathLength: 1 }}\n                      transition={{ duration: 0.8, delay: 0.5 }}\n                    />\n                  </svg>\n                </motion.div>\n              </motion.div>\n\n              {/* Heading */}\n              <motion.h1\n                className=\"font-playfair text-3xl md:text-4xl font-bold mb-3 text-white\"\n                variants={itemVariants}\n              >\n                Thank you for your order!\n              </motion.h1>\n\n              {/* Order ID */}\n              <motion.div\n                className=\"mb-6\"\n                variants={itemVariants}\n              >\n                <p className=\"text-gray-300 text-lg\">Order <span className=\"text-lime-400 font-medium\">#{orderId}</span> confirmed</p>\n              </motion.div>\n\n              {/* Order Summary Box */}\n              <motion.div\n                className=\"bg-black/50 rounded-xl p-6 mb-8 border border-gray-800\"\n                variants={itemVariants}\n              >\n                <div className=\"flex items-center justify-center mb-3\">\n                  <svg className=\"w-5 h-5 text-lime-400 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                  </svg>\n                  <h3 className=\"font-medium text-white\">Estimated Delivery/Pickup</h3>\n                </div>\n                <p className=\"text-lime-400 text-xl font-bold mb-1\">{deliveryTimeString}</p>\n                <p className=\"text-gray-400 text-sm\">\n                  Our chef is preparing your delicious meal\n                </p>\n              </motion.div>\n\n              {/* Countdown Timer and Track Order Section */}\n              <motion.div variants={itemVariants} className=\"space-y-4\">\n                {/* Countdown Timer */}\n                {countdown > 0 && !isRedirecting && (\n                  <motion.div\n                    className=\"bg-black/40 rounded-lg p-4 border border-lime-800/30\"\n                    initial={{ opacity: 0, y: 10 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ delay: 2 }}\n                  >\n                    <div className=\"flex items-center justify-center space-x-2 text-gray-300\">\n                      <svg className=\"w-4 h-4 text-lime-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                      </svg>\n                      <span className=\"text-sm\">\n                        Redirecting to order tracking in{' '}\n                        <motion.span\n                          className=\"text-lime-400 font-bold text-lg\"\n                          key={countdown}\n                          initial={{ scale: 1.2 }}\n                          animate={{ scale: 1 }}\n                          transition={{ duration: 0.3 }}\n                        >\n                          {countdown}\n                        </motion.span>\n                        {' '}second{countdown !== 1 ? 's' : ''}...\n                      </span>\n                    </div>\n                  </motion.div>\n                )}\n\n                {/* Redirecting State */}\n                {isRedirecting && (\n                  <motion.div\n                    className=\"bg-black/40 rounded-lg p-4 border border-lime-800/30\"\n                    initial={{ opacity: 0 }}\n                    animate={{ opacity: 1 }}\n                  >\n                    <div className=\"flex items-center justify-center space-x-2 text-lime-400\">\n                      <motion.svg\n                        className=\"w-4 h-4\"\n                        fill=\"none\"\n                        viewBox=\"0 0 24 24\"\n                        stroke=\"currentColor\"\n                        animate={{ rotate: 360 }}\n                        transition={{ duration: 1, repeat: Infinity, ease: \"linear\" }}\n                      >\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\" />\n                      </motion.svg>\n                      <span className=\"text-sm font-medium\">Redirecting to order tracking...</span>\n                    </div>\n                  </motion.div>\n                )}\n\n                {/* Track Order Now Button */}\n                <motion.button\n                  onClick={handleSkipCountdown}\n                  disabled={isRedirecting}\n                  className=\"relative overflow-hidden rounded-lg px-8 py-4 group w-full disabled:opacity-50 disabled:cursor-not-allowed\"\n                  whileHover={!isRedirecting ? { scale: 1.03 } : {}}\n                  whileTap={!isRedirecting ? { scale: 0.97 } : {}}\n                  transition={{ type: \"spring\", stiffness: 400, damping: 17 }}\n                >\n                  {/* Button Background */}\n                  <span className=\"absolute inset-0 w-full h-full bg-gradient-to-r from-lime-700/70 to-lime-600/70\"></span>\n\n                  {/* Button Glow Effect */}\n                  <span className=\"absolute inset-0 w-full h-full transition-all duration-300\n                                  bg-gradient-to-r from-lime-600/50 via-lime-500/50 to-lime-600/50\n                                  opacity-0 group-hover:opacity-100 group-hover:blur-md\"></span>\n\n                  {/* Button Border */}\n                  <span className=\"absolute inset-0 w-full h-full border border-lime-500/50 rounded-lg\"></span>\n\n                  {/* Button Text */}\n                  <span className=\"relative z-10 flex items-center justify-center text-white font-medium text-lg\">\n                    <svg className=\"w-5 h-5 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2}\n                          d=\"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\" />\n                    </svg>\n                    {countdown > 0 && !isRedirecting ? 'Track Order Now' : 'Track Your Order'}\n                  </span>\n                </motion.button>\n              </motion.div>\n\n              {/* Return to Home */}\n              <motion.div\n                variants={itemVariants}\n                className=\"mt-6\"\n              >\n                <Link href=\"/\">\n                  <motion.button\n                    className=\"text-gray-400 hover:text-lime-400 transition-colors duration-300\"\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                  >\n                    Return to Home\n                  </motion.button>\n                </Link>\n              </motion.div>\n            </div>\n          </motion.div>\n        </div>\n      </motion.div>\n    </main>\n  );\n};\n\nexport default OrderConfirmation;", "modifiedCode": "import { useState, useEffect } from \"react\";\nimport { useLocation, Link } from \"wouter\";\nimport { motion, AnimatePresence } from \"framer-motion\";\n\ninterface OrderConfirmationProps {\n  params?: {\n    orderId?: string;\n  };\n}\n\nconst OrderConfirmation = (props: OrderConfirmationProps) => {\n  const orderId = props.params?.orderId || \"BBC\" + Math.floor(Math.random() * 100000);\n  const [, setLocation] = useLocation();\n  const [showConfetti, setShowConfetti] = useState(true);\n  const [countdown, setCountdown] = useState(3);\n  const [isRedirecting, setIsRedirecting] = useState(false);\n\n  // Estimated delivery time calculation (30-45 minutes from now)\n  const now = new Date();\n  const deliveryTime = new Date(now.getTime() + 45 * 60000);\n  const deliveryTimeString = deliveryTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });\n\n  // Countdown timer and redirect logic\n  useEffect(() => {\n    // Start countdown after 2 seconds to let user read the confirmation\n    const startCountdownTimer = setTimeout(() => {\n      const countdownInterval = setInterval(() => {\n        setCountdown((prev) => {\n          if (prev <= 1) {\n            clearInterval(countdownInterval);\n            handleRedirectToTracking();\n            return 0;\n          }\n          return prev - 1;\n        });\n      }, 1000);\n\n      return () => clearInterval(countdownInterval);\n    }, 2000);\n\n    return () => clearTimeout(startCountdownTimer);\n  }, []);\n\n  // Function to handle redirect to order tracking\n  const handleRedirectToTracking = () => {\n    setIsRedirecting(true);\n\n    // Validate order ID before redirecting\n    if (!orderId || orderId.startsWith('BBC') && orderId.length < 6) {\n      // If no valid order ID, redirect to a general order lookup page\n      setLocation('/track-order');\n      return;\n    }\n\n    // Redirect to real order tracking page\n    setLocation(`/track-order/${orderId}`);\n  };\n\n  // Function to skip countdown and go directly to tracking\n  const handleSkipCountdown = () => {\n    setCountdown(0);\n    handleRedirectToTracking();\n  };\n\n  // Animation variants\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        delayChildren: 0.3,\n        staggerChildren: 0.2\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { y: 20, opacity: 0 },\n    visible: {\n      y: 0,\n      opacity: 1,\n      transition: {\n        type: \"spring\",\n        stiffness: 100,\n        damping: 10\n      }\n    }\n  };\n\n  const pulseVariants = {\n    initial: { scale: 1, boxShadow: \"0 0 0px rgba(57, 255, 20, 0)\" },\n    animate: {\n      scale: [1, 1.05, 1],\n      boxShadow: [\n        \"0 0 0px rgba(57, 255, 20, 0)\",\n        \"0 0 20px rgba(57, 255, 20, 0.7)\",\n        \"0 0 0px rgba(57, 255, 20, 0)\"\n      ],\n      transition: {\n        duration: 2,\n        repeat: Infinity,\n        repeatType: \"loop\"\n      }\n    }\n  };\n\n  // Disable confetti after a few seconds\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      setShowConfetti(false);\n    }, 5000);\n\n    return () => clearTimeout(timer);\n  }, []);\n\n  return (\n    <main className=\"min-h-screen bg-black relative overflow-hidden flex items-center justify-center py-12\">\n      {/* Animated Gradient Background */}\n      <div\n        className=\"absolute inset-0 z-0\"\n        style={{\n          background: \"radial-gradient(circle at 20% 20%, rgba(0, 255, 255, 0.03), transparent 33%), \" +\n                    \"radial-gradient(circle at 80% 80%, rgba(57, 255, 20, 0.03), transparent 33%), \" +\n                    \"radial-gradient(circle at 50% 50%, rgba(255, 0, 255, 0.03), rgba(0, 0, 0, 1) 100%)\",\n        }}\n      ></div>\n\n      {/* Neon Grid Overlay */}\n      <div className=\"absolute inset-0 z-0 opacity-[0.03]\"\n           style={{\n             backgroundImage: \"linear-gradient(to right, #39FF14 1px, transparent 1px), linear-gradient(to bottom, #39FF14 1px, transparent 1px)\",\n             backgroundSize: \"40px 40px\"\n           }}>\n      </div>\n\n      {/* Confetti Animation */}\n      {showConfetti && (\n        <div className=\"absolute inset-0 pointer-events-none z-10\">\n          {[...Array(40)].map((_, i) => (\n            <motion.div\n              key={i}\n              className=\"absolute w-3 h-3 rounded-full\"\n              style={{\n                backgroundColor:\n                  i % 3 === 0 ? 'rgba(57, 255, 20, 0.7)' :\n                  i % 3 === 1 ? 'rgba(0, 255, 255, 0.7)' :\n                  'rgba(255, 0, 255, 0.7)',\n                top: `${Math.random() * -10}%`,\n                left: `${Math.random() * 100}%`,\n              }}\n              initial={{\n                y: -20,\n                opacity: 0,\n                scale: 0\n              }}\n              animate={{\n                y: `${100 + Math.random() * 50}vh`,\n                opacity: [1, 0.8, 0],\n                scale: [1, 0.8, 0.6],\n                rotate: [0, Math.random() * 360]\n              }}\n              transition={{\n                duration: 2.5 + Math.random() * 3.5,\n                delay: Math.random() * 3,\n                ease: \"easeOut\",\n                repeat: 1,\n                repeatType: \"loop\",\n                repeatDelay: Math.random() * 2\n              }}\n            />\n          ))}\n        </div>\n      )}\n\n      <motion.div\n        className=\"container mx-auto px-4 z-10 relative\"\n        variants={containerVariants}\n        initial=\"hidden\"\n        animate=\"visible\"\n      >\n        <div className=\"max-w-xl mx-auto\">\n          <motion.div\n            className=\"bg-black/30 backdrop-blur-sm rounded-2xl p-8 md:p-12 border border-lime-800/30\n                       shadow-[0_0_30px_rgba(57,255,20,0.2)]\"\n            initial={{ opacity: 0, scale: 0.9 }}\n            animate={{ opacity: 1, scale: 1 }}\n            transition={{ duration: 0.6 }}\n          >\n            <div className=\"text-center\">\n              {/* Success Icon */}\n              <motion.div\n                className=\"w-28 h-28 mx-auto mb-8 rounded-full bg-gradient-to-br from-lime-500/20 to-green-700/20\n                         flex items-center justify-center\"\n                initial={{ scale: 0 }}\n                animate={{ scale: 1 }}\n                transition={{\n                  type: \"spring\",\n                  stiffness: 200,\n                  damping: 15,\n                  delay: 0.2\n                }}\n              >\n                <motion.div\n                  className=\"w-24 h-24 rounded-full bg-gradient-to-br from-lime-500/30 to-green-700/30\n                           flex items-center justify-center\"\n                  variants={pulseVariants}\n                  initial=\"initial\"\n                  animate=\"animate\"\n                >\n                  <svg className=\"w-16 h-16 text-lime-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <motion.path\n                      strokeLinecap=\"round\"\n                      strokeLinejoin=\"round\"\n                      strokeWidth={2.5}\n                      d=\"M5 13l4 4L19 7\"\n                      initial={{ pathLength: 0 }}\n                      animate={{ pathLength: 1 }}\n                      transition={{ duration: 0.8, delay: 0.5 }}\n                    />\n                  </svg>\n                </motion.div>\n              </motion.div>\n\n              {/* Heading */}\n              <motion.h1\n                className=\"font-playfair text-3xl md:text-4xl font-bold mb-3 text-white\"\n                variants={itemVariants}\n              >\n                Thank you for your order!\n              </motion.h1>\n\n              {/* Order ID */}\n              <motion.div\n                className=\"mb-6\"\n                variants={itemVariants}\n              >\n                <p className=\"text-gray-300 text-lg\">Order <span className=\"text-lime-400 font-medium\">#{orderId}</span> confirmed</p>\n              </motion.div>\n\n              {/* Order Summary Box */}\n              <motion.div\n                className=\"bg-black/50 rounded-xl p-6 mb-8 border border-gray-800\"\n                variants={itemVariants}\n              >\n                <div className=\"flex items-center justify-center mb-3\">\n                  <svg className=\"w-5 h-5 text-lime-400 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                  </svg>\n                  <h3 className=\"font-medium text-white\">Estimated Delivery/Pickup</h3>\n                </div>\n                <p className=\"text-lime-400 text-xl font-bold mb-1\">{deliveryTimeString}</p>\n                <p className=\"text-gray-400 text-sm\">\n                  Our chef is preparing your delicious meal\n                </p>\n              </motion.div>\n\n              {/* Countdown Timer and Track Order Section */}\n              <motion.div variants={itemVariants} className=\"space-y-4\">\n                {/* Countdown Timer */}\n                {countdown > 0 && !isRedirecting && (\n                  <motion.div\n                    className=\"bg-black/40 rounded-lg p-4 border border-lime-800/30\"\n                    initial={{ opacity: 0, y: 10 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ delay: 2 }}\n                  >\n                    <div className=\"flex items-center justify-center space-x-2 text-gray-300\">\n                      <svg className=\"w-4 h-4 text-lime-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                      </svg>\n                      <span className=\"text-sm\">\n                        Redirecting to order tracking in{' '}\n                        <motion.span\n                          className=\"text-lime-400 font-bold text-lg\"\n                          key={countdown}\n                          initial={{ scale: 1.2 }}\n                          animate={{ scale: 1 }}\n                          transition={{ duration: 0.3 }}\n                        >\n                          {countdown}\n                        </motion.span>\n                        {' '}second{countdown !== 1 ? 's' : ''}...\n                      </span>\n                    </div>\n                  </motion.div>\n                )}\n\n                {/* Redirecting State */}\n                {isRedirecting && (\n                  <motion.div\n                    className=\"bg-black/40 rounded-lg p-4 border border-lime-800/30\"\n                    initial={{ opacity: 0 }}\n                    animate={{ opacity: 1 }}\n                  >\n                    <div className=\"flex items-center justify-center space-x-2 text-lime-400\">\n                      <motion.svg\n                        className=\"w-4 h-4\"\n                        fill=\"none\"\n                        viewBox=\"0 0 24 24\"\n                        stroke=\"currentColor\"\n                        animate={{ rotate: 360 }}\n                        transition={{ duration: 1, repeat: Infinity, ease: \"linear\" }}\n                      >\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\" />\n                      </motion.svg>\n                      <span className=\"text-sm font-medium\">Redirecting to order tracking...</span>\n                    </div>\n                  </motion.div>\n                )}\n\n                {/* Track Order Now Button */}\n                <motion.button\n                  onClick={handleSkipCountdown}\n                  disabled={isRedirecting}\n                  className=\"relative overflow-hidden rounded-lg px-8 py-4 group w-full disabled:opacity-50 disabled:cursor-not-allowed\"\n                  whileHover={!isRedirecting ? { scale: 1.03 } : {}}\n                  whileTap={!isRedirecting ? { scale: 0.97 } : {}}\n                  transition={{ type: \"spring\", stiffness: 400, damping: 17 }}\n                >\n                  {/* Button Background */}\n                  <span className=\"absolute inset-0 w-full h-full bg-gradient-to-r from-lime-700/70 to-lime-600/70\"></span>\n\n                  {/* Button Glow Effect */}\n                  <span className=\"absolute inset-0 w-full h-full transition-all duration-300\n                                  bg-gradient-to-r from-lime-600/50 via-lime-500/50 to-lime-600/50\n                                  opacity-0 group-hover:opacity-100 group-hover:blur-md\"></span>\n\n                  {/* Button Border */}\n                  <span className=\"absolute inset-0 w-full h-full border border-lime-500/50 rounded-lg\"></span>\n\n                  {/* Button Text */}\n                  <span className=\"relative z-10 flex items-center justify-center text-white font-medium text-lg\">\n                    <svg className=\"w-5 h-5 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2}\n                          d=\"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\" />\n                    </svg>\n                    {countdown > 0 && !isRedirecting ? 'Track Order Now' : 'Track Your Order'}\n                  </span>\n                </motion.button>\n              </motion.div>\n\n              {/* Return to Home */}\n              <motion.div\n                variants={itemVariants}\n                className=\"mt-6\"\n              >\n                <Link href=\"/\">\n                  <motion.button\n                    className=\"text-gray-400 hover:text-lime-400 transition-colors duration-300\"\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                  >\n                    Return to Home\n                  </motion.button>\n                </Link>\n              </motion.div>\n            </div>\n          </motion.div>\n        </div>\n      </motion.div>\n    </main>\n  );\n};\n\nexport default OrderConfirmation;"}