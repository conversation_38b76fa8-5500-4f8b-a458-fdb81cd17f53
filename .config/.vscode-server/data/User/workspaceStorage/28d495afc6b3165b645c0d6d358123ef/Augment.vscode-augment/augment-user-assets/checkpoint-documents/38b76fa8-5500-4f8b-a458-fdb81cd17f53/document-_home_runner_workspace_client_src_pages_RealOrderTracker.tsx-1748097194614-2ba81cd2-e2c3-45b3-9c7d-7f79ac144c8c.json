{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/RealOrderTracker.tsx"}, "originalCode": "import { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { CheckCircle, Clock, Package, ChefHat, Navigation, User, Sparkles, Star } from 'lucide-react';\nimport { format } from 'date-fns';\nimport confetti from 'canvas-confetti';\nimport {\n  ORDER_STATUSES,\n  ORDER_TYPES,\n  getStatusTimeline,\n  getStatusLabel,\n  getStatusDescription\n} from '@/utils/orderStatusWorkflow';\n\ninterface OrderTrackerProps {\n  params?: {\n    orderId?: string;\n  };\n}\n\ninterface Order {\n  id: number;\n  status: string;\n  orderDetails: {\n    type: 'delivery' | 'takeaway';\n    time: string;\n    scheduledTime?: string;\n  };\n  items: Array<{\n    id: number;\n    name: string;\n    quantity: number;\n    price: number;\n    description?: string;\n    imageUrl?: string;\n  }>;\n  customer: {\n    firstName: string;\n    email: string;\n    phone: string;\n    address?: string;\n    postalCode?: string;\n    city?: string;\n  };\n  subtotal: number;\n  deliveryFee: number;\n  total: number;\n  paymentMethod: string;\n  notes?: string;\n  createdAt: string;\n  estimatedDeliveryTime?: string;\n}\n\nconst RealOrderTracker = (props: OrderTrackerProps) => {\n  const orderId = props.params?.orderId;\n  const [order, setOrder] = useState<Order | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [hasTriggeredConfetti, setHasTriggeredConfetti] = useState(false);\n\n  // Fetch order data\n  useEffect(() => {\n    const fetchOrder = async () => {\n      if (!orderId) {\n        setError('Order ID is required');\n        setLoading(false);\n        return;\n      }\n\n      try {\n        const response = await fetch(`/api/orders/${orderId}`);\n        if (!response.ok) {\n          throw new Error('Order not found');\n        }\n\n        const orderData = await response.json();\n        setOrder(orderData);\n        setError(null);\n      } catch (err) {\n        console.error('Error fetching order:', err);\n        setError('Failed to load order details');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchOrder();\n\n    // Poll for updates every 30 seconds\n    const interval = setInterval(fetchOrder, 30000);\n    return () => clearInterval(interval);\n  }, [orderId]);\n\n  // Trigger confetti for completed orders\n  useEffect(() => {\n    if (order && !hasTriggeredConfetti && isOrderCompleted(order.status)) {\n      triggerConfetti();\n      setHasTriggeredConfetti(true);\n    }\n  }, [order, hasTriggeredConfetti]);\n\n  // Helper function to check if order is completed\n  const isOrderCompleted = (status: string) => {\n    return status === ORDER_STATUSES.COMPLETED || status === ORDER_STATUSES.DELIVERED;\n  };\n\n  // Confetti animation\n  const triggerConfetti = () => {\n    const duration = 3000;\n    const animationEnd = Date.now() + duration;\n    const defaults = { startVelocity: 30, spread: 360, ticks: 60, zIndex: 0 };\n\n    function randomInRange(min: number, max: number) {\n      return Math.random() * (max - min) + min;\n    }\n\n    const interval = setInterval(function() {\n      const timeLeft = animationEnd - Date.now();\n\n      if (timeLeft <= 0) {\n        return clearInterval(interval);\n      }\n\n      const particleCount = 50 * (timeLeft / duration);\n\n      // since particles fall down, start a bit higher than random\n      confetti(Object.assign({}, defaults, {\n        particleCount,\n        origin: { x: randomInRange(0.1, 0.3), y: Math.random() - 0.2 }\n      }));\n      confetti(Object.assign({}, defaults, {\n        particleCount,\n        origin: { x: randomInRange(0.7, 0.9), y: Math.random() - 0.2 }\n      }));\n    }, 250);\n  };\n\n  // Get status icon with special handling for completed orders\n  const getStatusIcon = (status: string, isCompleted = false, isActive = false) => {\n    if (isCompleted && (status === ORDER_STATUSES.DELIVERED || status === ORDER_STATUSES.COMPLETED)) {\n      return (\n        <motion.div\n          initial={{ scale: 0 }}\n          animate={{ scale: 1 }}\n          transition={{ type: \"spring\", stiffness: 300, damping: 20 }}\n        >\n          <Star className=\"w-6 h-6 text-yellow-400 fill-current\" />\n        </motion.div>\n      );\n    }\n\n    switch (status) {\n      case ORDER_STATUSES.CONFIRMED:\n        return <CheckCircle className=\"w-6 h-6\" />;\n      case ORDER_STATUSES.PREPARING:\n        return <ChefHat className=\"w-6 h-6\" />;\n      case ORDER_STATUSES.READY_FOR_PICKUP:\n        return <Package className=\"w-6 h-6\" />;\n      case ORDER_STATUSES.READY_FOR_DELIVERY:\n        return <Package className=\"w-6 h-6\" />;\n      case ORDER_STATUSES.WITH_DRIVER:\n        return <User className=\"w-6 h-6\" />;\n      case ORDER_STATUSES.ON_THE_WAY:\n        return <Navigation className=\"w-6 h-6\" />;\n      case ORDER_STATUSES.DELIVERED:\n      case ORDER_STATUSES.COMPLETED:\n        return (\n          <motion.div\n            animate={{\n              scale: [1, 1.2, 1],\n              rotate: [0, 360, 0]\n            }}\n            transition={{\n              duration: 2,\n              repeat: Infinity,\n              repeatType: \"loop\" as const\n            }}\n          >\n            <CheckCircle className=\"w-6 h-6\" />\n          </motion.div>\n        );\n      default:\n        return <Clock className=\"w-6 h-6\" />;\n    }\n  };\n\n  // Get status color with enhanced completion styling\n  const getStatusColorClass = (status: string, isActive: boolean, isCompleted: boolean) => {\n    // Special styling for completed final step\n    if (isCompleted && (status === ORDER_STATUSES.DELIVERED || status === ORDER_STATUSES.COMPLETED)) {\n      return 'text-yellow-400 bg-gradient-to-r from-yellow-900/30 to-green-900/30 border-yellow-500 shadow-lg shadow-yellow-500/20';\n    }\n\n    if (isCompleted) return 'text-green-400 bg-green-900/20 border-green-700';\n\n    if (isActive) {\n      switch (status) {\n        case ORDER_STATUSES.CONFIRMED:\n          return 'text-blue-400 bg-blue-900/20 border-blue-700';\n        case ORDER_STATUSES.PREPARING:\n          return 'text-orange-400 bg-orange-900/20 border-orange-700';\n        case ORDER_STATUSES.READY_FOR_PICKUP:\n        case ORDER_STATUSES.READY_FOR_DELIVERY:\n          return 'text-green-400 bg-green-900/20 border-green-700';\n        case ORDER_STATUSES.WITH_DRIVER:\n          return 'text-cyan-400 bg-cyan-900/20 border-cyan-700';\n        case ORDER_STATUSES.ON_THE_WAY:\n          return 'text-purple-400 bg-purple-900/20 border-purple-700';\n        case ORDER_STATUSES.DELIVERED:\n        case ORDER_STATUSES.COMPLETED:\n          return 'text-yellow-400 bg-gradient-to-r from-yellow-900/30 to-green-900/30 border-yellow-500 shadow-lg shadow-yellow-500/20';\n        default:\n          return 'text-gray-400 bg-gray-900/20 border-gray-700';\n      }\n    }\n    return 'text-gray-600 bg-gray-900/10 border-gray-800';\n  };\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-black flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-cyan-400 mx-auto mb-4\"></div>\n          <p className=\"text-gray-400\">Loading order details...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (error || !order) {\n    return (\n      <div className=\"min-h-screen bg-black flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"text-red-400 text-6xl mb-4\">⚠️</div>\n          <h1 className=\"text-2xl font-bold text-white mb-2\">Order Not Found</h1>\n          <p className=\"text-gray-400 mb-6\">{error || 'The order you are looking for does not exist.'}</p>\n          <a href=\"/\" className=\"text-cyan-400 hover:text-cyan-300 underline\">\n            Return to Home\n          </a>\n        </div>\n      </div>\n    );\n  }\n\n  const orderType = order.orderDetails.type;\n  const statusTimeline = getStatusTimeline(orderType);\n  const currentStatusIndex = statusTimeline.indexOf(order.status as any);\n  const isCompleted = isOrderCompleted(order.status);\n\n  // Fix for completed orders - if order is completed but not found in timeline,\n  // it means it's at the final step\n  const actualCurrentIndex = isCompleted && currentStatusIndex === -1\n    ? statusTimeline.length - 1\n    : currentStatusIndex;\n\n  return (\n    <div className=\"min-h-screen bg-black text-white py-12\">\n      {/* Background */}\n      <div className=\"absolute inset-0 z-0 opacity-[0.02]\"\n           style={{\n             backgroundImage: \"linear-gradient(to right, #00FFFF 1px, transparent 1px), linear-gradient(to bottom, #00FFFF 1px, transparent 1px)\",\n             backgroundSize: \"40px 40px\"\n           }}>\n      </div>\n\n      <div className=\"container mx-auto px-4 z-10 relative max-w-4xl\">\n        {/* Header */}\n        <div className=\"text-center mb-12\">\n          <h1 className=\"text-3xl md:text-4xl font-bold mb-4\">\n            Track Your Order\n          </h1>\n          <div className=\"space-y-2\">\n            <p className=\"text-xl\">\n              Order <span className=\"text-cyan-400 font-medium\">#{order.id}</span>\n            </p>\n            <p className=\"text-gray-400\">\n              {orderType === ORDER_TYPES.DELIVERY ? 'Delivery' : 'Takeaway'} Order\n            </p>\n            <p className=\"text-gray-500 text-sm\">\n              Placed on {format(new Date(order.createdAt), 'PPp')}\n            </p>\n          </div>\n        </div>\n\n        {/* Current Status */}\n        <div className=\"bg-gray-900/50 rounded-xl p-6 mb-8 border border-gray-800\">\n          <div className=\"flex items-center justify-center space-x-4\">\n            <motion.div\n              className={`p-3 rounded-full ${getStatusColorClass(order.status, true, isCompleted)}`}\n              animate={isCompleted ? {\n                boxShadow: [\n                  \"0 0 0 rgba(255, 255, 0, 0)\",\n                  \"0 0 20px rgba(255, 255, 0, 0.6)\",\n                  \"0 0 0 rgba(255, 255, 0, 0)\"\n                ]\n              } : {}}\n              transition={isCompleted ? {\n                duration: 2,\n                repeat: Infinity,\n                repeatType: \"loop\" as const\n              } : {}}\n            >\n              {getStatusIcon(order.status, isCompleted)}\n            </motion.div>\n            <div className=\"text-center\">\n              <h2 className=\"text-2xl font-bold flex items-center justify-center gap-2\">\n                {getStatusLabel(order.status as any)}\n                {isCompleted && (\n                  <motion.div\n                    initial={{ scale: 0, rotate: -180 }}\n                    animate={{ scale: 1, rotate: 0 }}\n                    transition={{ type: \"spring\", stiffness: 300, damping: 20, delay: 0.5 }}\n                  >\n                    <Sparkles className=\"w-6 h-6 text-yellow-400\" />\n                  </motion.div>\n                )}\n              </h2>\n              <p className=\"text-gray-400\">{getStatusDescription(order.status as any)}</p>\n              {isCompleted && (\n                <motion.p\n                  className=\"text-yellow-400 font-medium mt-2\"\n                  initial={{ opacity: 0, y: 10 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 1 }}\n                >\n                  🎉 Thank you for choosing us!\n                </motion.p>\n              )}\n            </div>\n          </div>\n        </div>\n\n        {/* Progress Timeline */}\n        <div className=\"bg-gray-900/50 rounded-xl p-6 mb-8 border border-gray-800\">\n          <h3 className=\"text-xl font-bold mb-6\">Order Progress</h3>\n\n          {/* Progress Bar */}\n          <div className=\"relative h-2 bg-gray-800 rounded-full mb-8 overflow-hidden\">\n            <motion.div\n              className=\"absolute h-full bg-gradient-to-r from-cyan-500 to-green-500 rounded-full\"\n              initial={{ width: \"0%\" }}\n              animate={{ width: `${currentStatusIndex >= 0 ? (currentStatusIndex / (statusTimeline.length - 1)) * 100 : 0}%` }}\n              transition={{ duration: 1, ease: \"easeInOut\" }}\n            />\n          </div>\n\n          {/* Timeline Steps */}\n          <div className=\"space-y-6\">\n            {statusTimeline.map((status, index) => {\n              const isActive = index === currentStatusIndex;\n              const isCompleted = index < currentStatusIndex;\n\n              return (\n                <motion.div\n                  key={status}\n                  className=\"flex items-center space-x-4\"\n                  initial={{ opacity: 0, x: -20 }}\n                  animate={{ opacity: 1, x: 0 }}\n                  transition={{ delay: index * 0.1 }}\n                >\n                  <div className={`p-3 rounded-full border-2 ${getStatusColorClass(status, isActive, isCompleted)}`}>\n                    {getStatusIcon(status)}\n                  </div>\n                  <div className=\"flex-1\">\n                    <h4 className={`font-medium ${isActive || isCompleted ? 'text-white' : 'text-gray-500'}`}>\n                      {getStatusLabel(status)}\n                    </h4>\n                    <p className={`text-sm ${isActive || isCompleted ? 'text-gray-300' : 'text-gray-600'}`}>\n                      {getStatusDescription(status)}\n                    </p>\n                  </div>\n                  {isCompleted && (\n                    <CheckCircle className=\"w-5 h-5 text-green-400\" />\n                  )}\n                </motion.div>\n              );\n            })}\n          </div>\n        </div>\n\n        {/* Order Details */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          {/* Items */}\n          <div className=\"bg-gray-900/50 rounded-xl p-6 border border-gray-800\">\n            <h3 className=\"text-xl font-bold mb-4\">Order Items</h3>\n            <div className=\"space-y-3\">\n              {order.items.map((item, index) => (\n                <div key={index} className=\"flex justify-between items-center\">\n                  <div>\n                    <span className=\"text-white\">{item.name}</span>\n                    <span className=\"text-gray-400 ml-2\">x{item.quantity}</span>\n                  </div>\n                  <span className=\"text-cyan-400\">${item.price.toFixed(2)}</span>\n                </div>\n              ))}\n              <div className=\"border-t border-gray-700 pt-3 mt-3\">\n                <div className=\"flex justify-between items-center font-bold\">\n                  <span>Total</span>\n                  <span className=\"text-green-400\">${order.total.toFixed(2)}</span>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Customer Details */}\n          <div className=\"bg-gray-900/50 rounded-xl p-6 border border-gray-800\">\n            <h3 className=\"text-xl font-bold mb-4\">\n              {orderType === ORDER_TYPES.DELIVERY ? 'Delivery Details' : 'Customer Details'}\n            </h3>\n            <div className=\"space-y-3\">\n              <div>\n                <span className=\"text-gray-400\">Name:</span>\n                <span className=\"text-white ml-2\">{order.customer.firstName}</span>\n              </div>\n              <div>\n                <span className=\"text-gray-400\">Phone:</span>\n                <span className=\"text-white ml-2\">{order.customer.phone}</span>\n              </div>\n              {orderType === ORDER_TYPES.DELIVERY && order.customer.address && (\n                <div>\n                  <span className=\"text-gray-400\">Address:</span>\n                  <span className=\"text-white ml-2\">\n                    {order.customer.address}\n                    {order.customer.postalCode && `, ${order.customer.postalCode}`}\n                    {order.customer.city && ` ${order.customer.city}`}\n                  </span>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n\n        {/* Actions */}\n        <div className=\"text-center mt-8\">\n          <a\n            href=\"/\"\n            className=\"inline-flex items-center px-6 py-3 bg-cyan-600 hover:bg-cyan-700 text-white rounded-lg transition-colors\"\n          >\n            Return to Home\n          </a>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default RealOrderTracker;\n", "modifiedCode": "import { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { CheckCircle, Clock, Package, ChefHat, Navigation, User, Sparkles, Star } from 'lucide-react';\nimport { format } from 'date-fns';\nimport confetti from 'canvas-confetti';\nimport {\n  ORDER_STATUSES,\n  ORDER_TYPES,\n  getStatusTimeline,\n  getStatusLabel,\n  getStatusDescription\n} from '@/utils/orderStatusWorkflow';\n\ninterface OrderTrackerProps {\n  params?: {\n    orderId?: string;\n  };\n}\n\ninterface Order {\n  id: number;\n  status: string;\n  orderDetails: {\n    type: 'delivery' | 'takeaway';\n    time: string;\n    scheduledTime?: string;\n  };\n  items: Array<{\n    id: number;\n    name: string;\n    quantity: number;\n    price: number;\n    description?: string;\n    imageUrl?: string;\n  }>;\n  customer: {\n    firstName: string;\n    email: string;\n    phone: string;\n    address?: string;\n    postalCode?: string;\n    city?: string;\n  };\n  subtotal: number;\n  deliveryFee: number;\n  total: number;\n  paymentMethod: string;\n  notes?: string;\n  createdAt: string;\n  estimatedDeliveryTime?: string;\n}\n\nconst RealOrderTracker = (props: OrderTrackerProps) => {\n  const orderId = props.params?.orderId;\n  const [order, setOrder] = useState<Order | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [hasTriggeredConfetti, setHasTriggeredConfetti] = useState(false);\n\n  // Fetch order data\n  useEffect(() => {\n    const fetchOrder = async () => {\n      if (!orderId) {\n        setError('Order ID is required');\n        setLoading(false);\n        return;\n      }\n\n      try {\n        const response = await fetch(`/api/orders/${orderId}`);\n        if (!response.ok) {\n          throw new Error('Order not found');\n        }\n\n        const orderData = await response.json();\n        setOrder(orderData);\n        setError(null);\n      } catch (err) {\n        console.error('Error fetching order:', err);\n        setError('Failed to load order details');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchOrder();\n\n    // Poll for updates every 30 seconds\n    const interval = setInterval(fetchOrder, 30000);\n    return () => clearInterval(interval);\n  }, [orderId]);\n\n  // Trigger confetti for completed orders\n  useEffect(() => {\n    if (order && !hasTriggeredConfetti && isOrderCompleted(order.status)) {\n      triggerConfetti();\n      setHasTriggeredConfetti(true);\n    }\n  }, [order, hasTriggeredConfetti]);\n\n  // Helper function to check if order is completed\n  const isOrderCompleted = (status: string) => {\n    return status === ORDER_STATUSES.COMPLETED || status === ORDER_STATUSES.DELIVERED;\n  };\n\n  // Confetti animation\n  const triggerConfetti = () => {\n    const duration = 3000;\n    const animationEnd = Date.now() + duration;\n    const defaults = { startVelocity: 30, spread: 360, ticks: 60, zIndex: 0 };\n\n    function randomInRange(min: number, max: number) {\n      return Math.random() * (max - min) + min;\n    }\n\n    const interval = setInterval(function() {\n      const timeLeft = animationEnd - Date.now();\n\n      if (timeLeft <= 0) {\n        return clearInterval(interval);\n      }\n\n      const particleCount = 50 * (timeLeft / duration);\n\n      // since particles fall down, start a bit higher than random\n      confetti(Object.assign({}, defaults, {\n        particleCount,\n        origin: { x: randomInRange(0.1, 0.3), y: Math.random() - 0.2 }\n      }));\n      confetti(Object.assign({}, defaults, {\n        particleCount,\n        origin: { x: randomInRange(0.7, 0.9), y: Math.random() - 0.2 }\n      }));\n    }, 250);\n  };\n\n  // Get status icon with special handling for completed orders\n  const getStatusIcon = (status: string, isCompleted = false, isActive = false) => {\n    if (isCompleted && (status === ORDER_STATUSES.DELIVERED || status === ORDER_STATUSES.COMPLETED)) {\n      return (\n        <motion.div\n          initial={{ scale: 0 }}\n          animate={{ scale: 1 }}\n          transition={{ type: \"spring\", stiffness: 300, damping: 20 }}\n        >\n          <Star className=\"w-6 h-6 text-yellow-400 fill-current\" />\n        </motion.div>\n      );\n    }\n\n    switch (status) {\n      case ORDER_STATUSES.CONFIRMED:\n        return <CheckCircle className=\"w-6 h-6\" />;\n      case ORDER_STATUSES.PREPARING:\n        return <ChefHat className=\"w-6 h-6\" />;\n      case ORDER_STATUSES.READY_FOR_PICKUP:\n        return <Package className=\"w-6 h-6\" />;\n      case ORDER_STATUSES.READY_FOR_DELIVERY:\n        return <Package className=\"w-6 h-6\" />;\n      case ORDER_STATUSES.WITH_DRIVER:\n        return <User className=\"w-6 h-6\" />;\n      case ORDER_STATUSES.ON_THE_WAY:\n        return <Navigation className=\"w-6 h-6\" />;\n      case ORDER_STATUSES.DELIVERED:\n      case ORDER_STATUSES.COMPLETED:\n        return (\n          <motion.div\n            animate={{\n              scale: [1, 1.2, 1],\n              rotate: [0, 360, 0]\n            }}\n            transition={{\n              duration: 2,\n              repeat: Infinity,\n              repeatType: \"loop\" as const\n            }}\n          >\n            <CheckCircle className=\"w-6 h-6\" />\n          </motion.div>\n        );\n      default:\n        return <Clock className=\"w-6 h-6\" />;\n    }\n  };\n\n  // Get status color with enhanced completion styling\n  const getStatusColorClass = (status: string, isActive: boolean, isCompleted: boolean) => {\n    // Special styling for completed final step\n    if (isCompleted && (status === ORDER_STATUSES.DELIVERED || status === ORDER_STATUSES.COMPLETED)) {\n      return 'text-yellow-400 bg-gradient-to-r from-yellow-900/30 to-green-900/30 border-yellow-500 shadow-lg shadow-yellow-500/20';\n    }\n\n    if (isCompleted) return 'text-green-400 bg-green-900/20 border-green-700';\n\n    if (isActive) {\n      switch (status) {\n        case ORDER_STATUSES.CONFIRMED:\n          return 'text-blue-400 bg-blue-900/20 border-blue-700';\n        case ORDER_STATUSES.PREPARING:\n          return 'text-orange-400 bg-orange-900/20 border-orange-700';\n        case ORDER_STATUSES.READY_FOR_PICKUP:\n        case ORDER_STATUSES.READY_FOR_DELIVERY:\n          return 'text-green-400 bg-green-900/20 border-green-700';\n        case ORDER_STATUSES.WITH_DRIVER:\n          return 'text-cyan-400 bg-cyan-900/20 border-cyan-700';\n        case ORDER_STATUSES.ON_THE_WAY:\n          return 'text-purple-400 bg-purple-900/20 border-purple-700';\n        case ORDER_STATUSES.DELIVERED:\n        case ORDER_STATUSES.COMPLETED:\n          return 'text-yellow-400 bg-gradient-to-r from-yellow-900/30 to-green-900/30 border-yellow-500 shadow-lg shadow-yellow-500/20';\n        default:\n          return 'text-gray-400 bg-gray-900/20 border-gray-700';\n      }\n    }\n    return 'text-gray-600 bg-gray-900/10 border-gray-800';\n  };\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-black flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-cyan-400 mx-auto mb-4\"></div>\n          <p className=\"text-gray-400\">Loading order details...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (error || !order) {\n    return (\n      <div className=\"min-h-screen bg-black flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"text-red-400 text-6xl mb-4\">⚠️</div>\n          <h1 className=\"text-2xl font-bold text-white mb-2\">Order Not Found</h1>\n          <p className=\"text-gray-400 mb-6\">{error || 'The order you are looking for does not exist.'}</p>\n          <a href=\"/\" className=\"text-cyan-400 hover:text-cyan-300 underline\">\n            Return to Home\n          </a>\n        </div>\n      </div>\n    );\n  }\n\n  const orderType = order.orderDetails.type;\n  const statusTimeline = getStatusTimeline(orderType);\n  const currentStatusIndex = statusTimeline.indexOf(order.status as any);\n  const isCompleted = isOrderCompleted(order.status);\n\n  // Fix for completed orders - if order is completed but not found in timeline,\n  // it means it's at the final step\n  const actualCurrentIndex = isCompleted && currentStatusIndex === -1\n    ? statusTimeline.length - 1\n    : currentStatusIndex;\n\n  return (\n    <div className=\"min-h-screen bg-black text-white py-12\">\n      {/* Background */}\n      <div className=\"absolute inset-0 z-0 opacity-[0.02]\"\n           style={{\n             backgroundImage: \"linear-gradient(to right, #00FFFF 1px, transparent 1px), linear-gradient(to bottom, #00FFFF 1px, transparent 1px)\",\n             backgroundSize: \"40px 40px\"\n           }}>\n      </div>\n\n      <div className=\"container mx-auto px-4 z-10 relative max-w-4xl\">\n        {/* Header */}\n        <div className=\"text-center mb-12\">\n          <h1 className=\"text-3xl md:text-4xl font-bold mb-4\">\n            Track Your Order\n          </h1>\n          <div className=\"space-y-2\">\n            <p className=\"text-xl\">\n              Order <span className=\"text-cyan-400 font-medium\">#{order.id}</span>\n            </p>\n            <p className=\"text-gray-400\">\n              {orderType === ORDER_TYPES.DELIVERY ? 'Delivery' : 'Takeaway'} Order\n            </p>\n            <p className=\"text-gray-500 text-sm\">\n              Placed on {format(new Date(order.createdAt), 'PPp')}\n            </p>\n          </div>\n        </div>\n\n        {/* Current Status */}\n        <div className=\"bg-gray-900/50 rounded-xl p-6 mb-8 border border-gray-800\">\n          <div className=\"flex items-center justify-center space-x-4\">\n            <motion.div\n              className={`p-3 rounded-full ${getStatusColorClass(order.status, true, isCompleted)}`}\n              animate={isCompleted ? {\n                boxShadow: [\n                  \"0 0 0 rgba(255, 255, 0, 0)\",\n                  \"0 0 20px rgba(255, 255, 0, 0.6)\",\n                  \"0 0 0 rgba(255, 255, 0, 0)\"\n                ]\n              } : {}}\n              transition={isCompleted ? {\n                duration: 2,\n                repeat: Infinity,\n                repeatType: \"loop\" as const\n              } : {}}\n            >\n              {getStatusIcon(order.status, isCompleted)}\n            </motion.div>\n            <div className=\"text-center\">\n              <h2 className=\"text-2xl font-bold flex items-center justify-center gap-2\">\n                {getStatusLabel(order.status as any)}\n                {isCompleted && (\n                  <motion.div\n                    initial={{ scale: 0, rotate: -180 }}\n                    animate={{ scale: 1, rotate: 0 }}\n                    transition={{ type: \"spring\", stiffness: 300, damping: 20, delay: 0.5 }}\n                  >\n                    <Sparkles className=\"w-6 h-6 text-yellow-400\" />\n                  </motion.div>\n                )}\n              </h2>\n              <p className=\"text-gray-400\">{getStatusDescription(order.status as any)}</p>\n              {isCompleted && (\n                <motion.p\n                  className=\"text-yellow-400 font-medium mt-2\"\n                  initial={{ opacity: 0, y: 10 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 1 }}\n                >\n                  🎉 Thank you for choosing us!\n                </motion.p>\n              )}\n            </div>\n          </div>\n        </div>\n\n        {/* Horizontal Progress Timeline */}\n        <div className=\"bg-gray-900/50 rounded-xl p-6 mb-8 border border-gray-800\">\n          <h3 className=\"text-xl font-bold mb-8 text-center\">Order Progress</h3>\n\n          {/* Horizontal Timeline Container */}\n          <div className=\"relative\">\n            {/* Progress Line */}\n            <div className=\"absolute top-1/2 left-0 right-0 h-1 bg-gray-800 rounded-full transform -translate-y-1/2 z-0\">\n              <motion.div\n                className=\"h-full bg-gradient-to-r from-cyan-500 to-green-500 rounded-full\"\n                initial={{ width: \"0%\" }}\n                animate={{\n                  width: `${actualCurrentIndex >= 0 ?\n                    (actualCurrentIndex / (statusTimeline.length - 1)) * 100 : 0}%`\n                }}\n                transition={{ duration: 1.5, ease: \"easeInOut\" }}\n              />\n            </div>\n\n            {/* Timeline Steps */}\n            <div className=\"relative z-10 flex justify-between items-center\">\n              {statusTimeline.map((status, index) => {\n                const isActive = index === actualCurrentIndex;\n                const isStepCompleted = index < actualCurrentIndex ||\n                  (isCompleted && index === statusTimeline.length - 1);\n                const isFinalStep = index === statusTimeline.length - 1;\n\n                return (\n                  <motion.div\n                    key={status}\n                    className=\"flex flex-col items-center space-y-3 relative\"\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ delay: index * 0.2 }}\n                  >\n                    {/* Step Circle */}\n                    <motion.div\n                      className={`relative p-4 rounded-full border-2 z-10 bg-black\n                        ${getStatusColorClass(status, isActive, isStepCompleted)}\n                        ${isFinalStep && isCompleted ? 'ring-4 ring-yellow-400/30' : ''}\n                      `}\n                      animate={isFinalStep && isCompleted ? {\n                        scale: [1, 1.1, 1],\n                        boxShadow: [\n                          \"0 0 0 rgba(255, 255, 0, 0)\",\n                          \"0 0 30px rgba(255, 255, 0, 0.8)\",\n                          \"0 0 0 rgba(255, 255, 0, 0)\"\n                        ]\n                      } : isActive ? {\n                        scale: [1, 1.05, 1]\n                      } : {}}\n                      transition={isFinalStep && isCompleted ? {\n                        duration: 2,\n                        repeat: Infinity,\n                        repeatType: \"loop\" as const\n                      } : isActive ? {\n                        duration: 1.5,\n                        repeat: Infinity,\n                        repeatType: \"reverse\" as const\n                      } : {}}\n                    >\n                      {getStatusIcon(status, isStepCompleted, isActive)}\n\n                      {/* Completion Sparkles */}\n                      {isFinalStep && isCompleted && (\n                        <AnimatePresence>\n                          <motion.div\n                            className=\"absolute -top-2 -right-2\"\n                            initial={{ scale: 0, rotate: -180 }}\n                            animate={{ scale: 1, rotate: 0 }}\n                            exit={{ scale: 0, rotate: 180 }}\n                            transition={{ type: \"spring\", stiffness: 300, damping: 20 }}\n                          >\n                            <Sparkles className=\"w-4 h-4 text-yellow-400\" />\n                          </motion.div>\n                        </AnimatePresence>\n                      )}\n                    </motion.div>\n\n                    {/* Step Label */}\n                    <div className=\"text-center max-w-24\">\n                      <h4 className={`text-sm font-medium mb-1\n                        ${isActive || isStepCompleted ? 'text-white' : 'text-gray-500'}\n                        ${isFinalStep && isCompleted ? 'text-yellow-400' : ''}\n                      `}>\n                        {getStatusLabel(status)}\n                      </h4>\n                      <p className={`text-xs leading-tight\n                        ${isActive || isStepCompleted ? 'text-gray-300' : 'text-gray-600'}\n                      `}>\n                        {getStatusDescription(status)}\n                      </p>\n                    </div>\n\n                    {/* Step Number */}\n                    <div className={`text-xs px-2 py-1 rounded-full\n                      ${isActive || isStepCompleted ? 'bg-cyan-900/30 text-cyan-400' : 'bg-gray-800 text-gray-500'}\n                      ${isFinalStep && isCompleted ? 'bg-yellow-900/30 text-yellow-400' : ''}\n                    `}>\n                      {index + 1}\n                    </div>\n                  </motion.div>\n                );\n              })}\n            </div>\n          </div>\n\n          {/* Completion Message */}\n          {isCompleted && (\n            <motion.div\n              className=\"mt-8 text-center\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 1.5 }}\n            >\n              <div className=\"bg-gradient-to-r from-yellow-900/20 to-green-900/20 rounded-lg p-4 border border-yellow-500/30\">\n                <h4 className=\"text-lg font-bold text-yellow-400 mb-2\">\n                  🎉 Order Complete!\n                </h4>\n                <p className=\"text-gray-300\">\n                  Your order has been successfully {orderType === ORDER_TYPES.DELIVERY ? 'delivered' : 'completed'}.\n                  We hope you enjoyed your meal!\n                </p>\n              </div>\n            </motion.div>\n          )}\n        </div>\n\n        {/* Order Details */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          {/* Items */}\n          <div className=\"bg-gray-900/50 rounded-xl p-6 border border-gray-800\">\n            <h3 className=\"text-xl font-bold mb-4\">Order Items</h3>\n            <div className=\"space-y-3\">\n              {order.items.map((item, index) => (\n                <div key={index} className=\"flex justify-between items-center\">\n                  <div>\n                    <span className=\"text-white\">{item.name}</span>\n                    <span className=\"text-gray-400 ml-2\">x{item.quantity}</span>\n                  </div>\n                  <span className=\"text-cyan-400\">${item.price.toFixed(2)}</span>\n                </div>\n              ))}\n              <div className=\"border-t border-gray-700 pt-3 mt-3\">\n                <div className=\"flex justify-between items-center font-bold\">\n                  <span>Total</span>\n                  <span className=\"text-green-400\">${order.total.toFixed(2)}</span>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Customer Details */}\n          <div className=\"bg-gray-900/50 rounded-xl p-6 border border-gray-800\">\n            <h3 className=\"text-xl font-bold mb-4\">\n              {orderType === ORDER_TYPES.DELIVERY ? 'Delivery Details' : 'Customer Details'}\n            </h3>\n            <div className=\"space-y-3\">\n              <div>\n                <span className=\"text-gray-400\">Name:</span>\n                <span className=\"text-white ml-2\">{order.customer.firstName}</span>\n              </div>\n              <div>\n                <span className=\"text-gray-400\">Phone:</span>\n                <span className=\"text-white ml-2\">{order.customer.phone}</span>\n              </div>\n              {orderType === ORDER_TYPES.DELIVERY && order.customer.address && (\n                <div>\n                  <span className=\"text-gray-400\">Address:</span>\n                  <span className=\"text-white ml-2\">\n                    {order.customer.address}\n                    {order.customer.postalCode && `, ${order.customer.postalCode}`}\n                    {order.customer.city && ` ${order.customer.city}`}\n                  </span>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n\n        {/* Actions */}\n        <div className=\"text-center mt-8\">\n          <a\n            href=\"/\"\n            className=\"inline-flex items-center px-6 py-3 bg-cyan-600 hover:bg-cyan-700 text-white rounded-lg transition-colors\"\n          >\n            Return to Home\n          </a>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default RealOrderTracker;\n"}