{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/TimelineStep.tsx"}, "originalCode": "import { motion } from \"framer-motion\";\n\ninterface TimelineStepProps {\n  icon: React.ReactNode;\n  title: string;\n  time?: string;\n  isActive: boolean;\n  isCompleted: boolean;\n  index: number;\n}\n\nconst TimelineStep = ({ icon, title, time, isActive, isCompleted, index }: TimelineStepProps) => {\n  return (\n    <div className=\"flex items-start\">\n      {/* Step Icon */}\n      <div className=\"relative\">\n        <motion.div\n          className={`w-12 h-12 rounded-full flex items-center justify-center z-10 relative \n                    ${isCompleted \n                      ? 'bg-gradient-to-br from-lime-500 to-lime-700 text-white' \n                      : isActive \n                        ? 'bg-gradient-to-br from-cyan-500/90 to-cyan-700/90 text-white'\n                        : 'bg-gray-800 text-gray-400'}`}\n          initial={{ scale: 0.8, opacity: 0 }}\n          animate={{ \n            scale: isActive ? [1, 1.05, 1] : 1, \n            opacity: 1,\n            boxShadow: isActive \n              ? ['0 0 0 rgba(0, 255, 255, 0)', '0 0 20px rgba(0, 255, 255, 0.6)', '0 0 10px rgba(0, 255, 255, 0.3)']\n              : isCompleted\n                ? '0 0 10px rgba(57, 255, 20, 0.3)'\n                : 'none'\n          }}\n          transition={isActive \n            ? { duration: 2, repeat: Infinity, repeatType: \"reverse\" }\n            : { duration: 0.5, delay: index * 0.2 }\n          }\n        >\n          {icon}\n        </motion.div>\n        \n        {/* Completed checkmark overlay */}\n        {isCompleted && (\n          <motion.div \n            className=\"absolute inset-0 flex items-center justify-center\"\n            initial={{ opacity: 0, scale: 0 }}\n            animate={{ opacity: 1, scale: 1 }}\n            transition={{ duration: 0.3, delay: 0.2 }}\n          >\n            <span className=\"flex items-center justify-center text-white z-20\">\n              <svg className=\"w-5 h-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={3} d=\"M5 13l4 4L19 7\" />\n              </svg>\n            </span>\n          </motion.div>\n        )}\n      </div>\n      \n      {/* Step Content */}\n      <div className=\"ml-4 pt-1\">\n        <motion.h3 \n          className={`font-medium text-base ${\n            isActive \n              ? 'text-cyan-400'\n              : isCompleted \n                ? 'text-lime-500'\n                : 'text-gray-400'\n          }`}\n          initial={{ opacity: 0, x: -10 }}\n          animate={{ opacity: 1, x: 0 }}\n          transition={{ duration: 0.5, delay: index * 0.2 + 0.1 }}\n        >\n          {title}\n        </motion.h3>\n        \n        {time && (\n          <motion.p \n            className=\"text-gray-500 text-sm\"\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            transition={{ duration: 0.5, delay: index * 0.2 + 0.2 }}\n          >\n            {time}\n          </motion.p>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default TimelineStep;", "modifiedCode": "import { motion } from \"framer-motion\";\n\ninterface TimelineStepProps {\n  icon: React.ReactNode;\n  title: string;\n  time?: string;\n  isActive: boolean;\n  isCompleted: boolean;\n  index: number;\n}\n\nconst TimelineStep = ({ icon, title, time, isActive, isCompleted, index }: TimelineStepProps) => {\n  return (\n    <div className=\"flex items-start\">\n      {/* Step Icon */}\n      <div className=\"relative\">\n        <motion.div\n          className={`w-12 h-12 rounded-full flex items-center justify-center z-10 relative \n                    ${isCompleted \n                      ? 'bg-gradient-to-br from-lime-500 to-lime-700 text-white' \n                      : isActive \n                        ? 'bg-gradient-to-br from-cyan-500/90 to-cyan-700/90 text-white'\n                        : 'bg-gray-800 text-gray-400'}`}\n          initial={{ scale: 0.8, opacity: 0 }}\n          animate={{ \n            scale: isActive ? [1, 1.05, 1] : 1, \n            opacity: 1,\n            boxShadow: isActive \n              ? ['0 0 0 rgba(0, 255, 255, 0)', '0 0 20px rgba(0, 255, 255, 0.6)', '0 0 10px rgba(0, 255, 255, 0.3)']\n              : isCompleted\n                ? '0 0 10px rgba(57, 255, 20, 0.3)'\n                : 'none'\n          }}\n          transition={isActive \n            ? { duration: 2, repeat: Infinity, repeatType: \"reverse\" }\n            : { duration: 0.5, delay: index * 0.2 }\n          }\n        >\n          {icon}\n        </motion.div>\n        \n        {/* Completed checkmark overlay */}\n        {isCompleted && (\n          <motion.div \n            className=\"absolute inset-0 flex items-center justify-center\"\n            initial={{ opacity: 0, scale: 0 }}\n            animate={{ opacity: 1, scale: 1 }}\n            transition={{ duration: 0.3, delay: 0.2 }}\n          >\n            <span className=\"flex items-center justify-center text-white z-20\">\n              <svg className=\"w-5 h-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={3} d=\"M5 13l4 4L19 7\" />\n              </svg>\n            </span>\n          </motion.div>\n        )}\n      </div>\n      \n      {/* Step Content */}\n      <div className=\"ml-4 pt-1\">\n        <motion.h3 \n          className={`font-medium text-base ${\n            isActive \n              ? 'text-cyan-400'\n              : isCompleted \n                ? 'text-lime-500'\n                : 'text-gray-400'\n          }`}\n          initial={{ opacity: 0, x: -10 }}\n          animate={{ opacity: 1, x: 0 }}\n          transition={{ duration: 0.5, delay: index * 0.2 + 0.1 }}\n        >\n          {title}\n        </motion.h3>\n        \n        {time && (\n          <motion.p \n            className=\"text-gray-500 text-sm\"\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            transition={{ duration: 0.5, delay: index * 0.2 + 0.2 }}\n          >\n            {time}\n          </motion.p>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default TimelineStep;"}