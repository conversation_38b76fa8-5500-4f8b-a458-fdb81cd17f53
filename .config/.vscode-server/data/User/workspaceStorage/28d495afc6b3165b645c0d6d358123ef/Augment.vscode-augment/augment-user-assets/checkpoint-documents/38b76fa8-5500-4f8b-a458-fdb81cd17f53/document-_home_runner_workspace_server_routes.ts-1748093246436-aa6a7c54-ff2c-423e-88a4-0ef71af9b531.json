{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "server/routes.ts"}, "originalCode": "import { Express, Request, Response, NextFunction, Router } from 'express';\nimport http from 'http';\nimport { log } from './vite';\nimport { storage } from './storage';\nimport adminApiRouter from './admin-api';\nimport { setupAuth, isAdmin, isStaff, isManagerOrAdmin } from './auth';\n\nexport async function registerRoutes(app: Express): Promise<http.Server> {\n  const apiRouter = Router();\n\n  // Logging middleware\n  app.use((req: Request, _res: Response, next: NextFunction) => {\n    if (req.url.startsWith('/api')) {\n      log(`${req.method} ${req.url}`, 'api');\n    }\n    next();\n  });\n\n  // Reusable error handler\n  const handleError = (error: any, res: Response) => {\n    console.error(error);\n    res.status(500).json({ error: 'An unexpected error occurred' });\n  };\n\n  // Menu Items / Dishes Routes\n  apiRouter.get('/dishes', async (req: Request, res: Response) => {\n    try {\n      const dishes = await storage.getAllDishes();\n      res.json(dishes);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  apiRouter.get('/dishes/:id', async (req: Request, res: Response) => {\n    try {\n      const id = parseInt(req.params.id);\n      const dish = await storage.getDishById(id);\n\n      if (!dish) {\n        return res.status(404).json({ error: 'Dish not found' });\n      }\n\n      res.json(dish);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  // Categories Routes\n  apiRouter.get('/categories', async (req: Request, res: Response) => {\n    try {\n      const categories = await storage.getAllMenuCategories();\n      res.json(categories);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  apiRouter.post('/categories', async (req: Request, res: Response) => {\n    try {\n      if (!req.body.name) {\n        return res.status(400).json({ error: 'Category name is required' });\n      }\n\n      const newCategory = await storage.createMenuCategory(req.body);\n      res.status(201).json(newCategory);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  apiRouter.put('/categories/:id', async (req: Request, res: Response) => {\n    try {\n      const id = parseInt(req.params.id);\n      const category = await storage.getMenuCategoryById(id);\n\n      if (!category) {\n        return res.status(404).json({ error: 'Category not found' });\n      }\n\n      const updatedCategory = await storage.updateMenuCategory(id, req.body);\n      res.json(updatedCategory);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  apiRouter.delete('/categories/:id', async (req: Request, res: Response) => {\n    try {\n      const id = parseInt(req.params.id);\n      const category = await storage.getMenuCategoryById(id);\n\n      if (!category) {\n        return res.status(404).json({ error: 'Category not found' });\n      }\n\n      const deletedCategory = await storage.deleteMenuCategory(id);\n      res.json({ message: 'Category deleted successfully', category: deletedCategory });\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  // Menu Items Routes\n  apiRouter.get('/items', async (req: Request, res: Response) => {\n    try {\n      const items = await storage.getAllMenuItems();\n      res.json(items);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  apiRouter.post('/items', async (req: Request, res: Response) => {\n    try {\n      const newItem = await storage.createMenuItem(req.body);\n      res.status(201).json(newItem);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  apiRouter.put('/items/:id', async (req: Request, res: Response) => {\n    try {\n      const id = parseInt(req.params.id);\n      const item = await storage.getMenuItemById(id);\n\n      if (!item) {\n        return res.status(404).json({ error: 'Menu item not found' });\n      }\n\n      // Update logic would go here\n      res.json({ ...item, ...req.body });\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  // Order Routes\n  apiRouter.post('/orders', async (req: Request, res: Response) => {\n    try {\n      // Get restaurant settings to check if it's open\n      const adminSettings = await fetch('http://localhost:5000/api/admin/settings').then(res => res.json());\n\n      // Check if restaurant is open\n      if (!adminSettings.restaurant_open) {\n        return res.status(403).json({\n          error: 'Restaurant is currently closed. Orders cannot be placed at this time.'\n        });\n      }\n\n      // If restaurant is open, proceed with order creation\n      const newOrder = await storage.createOrder(req.body);\n      res.status(201).json(newOrder);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  apiRouter.get('/orders/:id', async (req: Request, res: Response) => {\n    try {\n      const id = parseInt(req.params.id);\n      const order = await storage.getOrderById(id);\n\n      if (!order) {\n        return res.status(404).json({ error: 'Order not found' });\n      }\n\n      res.json(order);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  // Contact Form Route\n  apiRouter.post('/contact', async (req: Request, res: Response) => {\n    try {\n      const message = await storage.createContactMessage(req.body);\n      res.status(201).json({ success: true, message });\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  // Cart Routes\n  apiRouter.post('/cart', async (req: Request, res: Response) => {\n    try {\n      const result = await storage.createCart(req.body);\n      res.status(201).json(result);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  // Public Settings Route (for checkout page to get delivery fee)\n  apiRouter.get('/settings', async (req: Request, res: Response) => {\n    try {\n      const settings = await storage.getRestaurantSettings();\n\n      if (!settings) {\n        // Return default settings if none exist\n        return res.json({\n          delivery_fee: 49,\n          estimated_time: \"25-35 min\",\n          restaurant_open: true\n        });\n      }\n\n      // Return only public settings (no business hours)\n      res.json({\n        delivery_fee: settings.deliveryFee,\n        estimated_time: settings.estimatedTime,\n        restaurant_open: settings.restaurantOpen\n      });\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  // Public Customizations Route (for menu page)\n  apiRouter.get('/items/:itemId/customizations', async (req: Request, res: Response) => {\n    try {\n      const itemId = parseInt(req.params.itemId);\n      const customizations = await storage.getCustomizationOptionsForMenuItem(itemId);\n      res.json(customizations);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  // Register the API router with app\n  app.use('/api', apiRouter);\n\n  // Setup authentication\n  setupAuth(app);\n\n  // Create staff-accessible order routes (for managers and drivers)\n  // These routes need to be registered before the general admin routes\n  app.get('/api/admin/orders', isStaff, async (req: Request, res: Response) => {\n    try {\n      const statusFilter = req.query.status as string;\n      // Get orders from database\n      let orders = await storage.getAllOrders();\n\n      // Apply status filtering if applicable\n      if (statusFilter && statusFilter !== 'all') {\n        if (statusFilter === 'active') {\n          // Active orders are those that are not completed or cancelled\n          orders = orders.filter(order =>\n            !['completed', 'cancelled'].includes(order.status)\n          );\n        } else {\n          orders = orders.filter(order => order.status === statusFilter);\n        }\n      }\n\n      res.json(orders);\n    } catch (error) {\n      console.error('Error fetching orders:', error);\n      res.status(500).json({ error: 'Failed to fetch orders' });\n    }\n  });\n\n  app.get('/api/admin/orders/:id', isStaff, async (req: Request, res: Response) => {\n    try {\n      const orderId = parseInt(req.params.id);\n      const order = await storage.getOrderById(orderId);\n\n      if (!order) {\n        return res.status(404).json({ error: 'Order not found' });\n      }\n\n      res.json(order);\n    } catch (error) {\n      console.error(`Error fetching order ${req.params.id}:`, error);\n      res.status(500).json({ error: 'Failed to fetch order' });\n    }\n  });\n\n  app.put('/api/admin/orders/:id/status', isStaff, async (req: Request, res: Response) => {\n    try {\n      const orderId = parseInt(req.params.id);\n      const { newStatus } = req.body;\n\n      if (!newStatus) {\n        return res.status(400).json({ error: 'New status is required' });\n      }\n\n      // Get current order to validate status transition\n      const currentOrder = await storage.getOrderById(orderId);\n      if (!currentOrder) {\n        return res.status(404).json({ error: 'Order not found' });\n      }\n\n      // Validate status transition\n      const validStatuses = getValidNextStatuses(currentOrder.status, currentOrder.orderDetails?.type || 'takeaway');\n      if (!validStatuses.includes(newStatus)) {\n        return res.status(400).json({\n          error: `Invalid status transition from ${currentOrder.status} to ${newStatus}`,\n          validStatuses\n        });\n      }\n\n      // Update the order status in the database\n      const updatedOrder = await storage.updateOrder(orderId, { status: newStatus });\n\n      if (!updatedOrder) {\n        return res.status(500).json({ error: 'Failed to update order status' });\n      }\n\n      res.json({\n        success: true,\n        message: `Order ${orderId} status updated to ${newStatus}`,\n        order: updatedOrder\n      });\n    } catch (error) {\n      console.error(`Error updating order status for ${req.params.id}:`, error);\n      res.status(500).json({ error: 'Failed to update order status' });\n    }\n  });\n\n  app.post('/api/admin/dispatch/to-driver', isStaff, async (req: Request, res: Response) => {\n    try {\n      const { orderId } = req.body;\n\n      if (!orderId) {\n        return res.status(400).json({ error: 'Order ID is required' });\n      }\n\n      // In a real app, this would dispatch to a driver system\n      // For now, we just acknowledge receipt\n      res.json({\n        success: true,\n        message: `Order #${orderId} has been dispatched to a driver`,\n        timestamp: new Date().toISOString()\n      });\n    } catch (error) {\n      console.error('Error dispatching order to driver:', error);\n      res.status(500).json({ error: 'Failed to dispatch order to driver' });\n    }\n  });\n\n  // Register admin API routes with admin protection (for settings, menu management, etc.)\n  app.use('/api/admin', isAdmin, adminApiRouter);\n\n  // Create and return the server\n  const server = http.createServer(app);\n  return server;\n}", "modifiedCode": "import { Express, Request, Response, NextFunction, Router } from 'express';\nimport http from 'http';\nimport { log } from './vite';\nimport { storage } from './storage';\nimport adminApiRouter from './admin-api';\nimport { setupAuth, isAdmin, isStaff, isManagerOrAdmin } from './auth';\n\n// Status workflow validation functions\nfunction getValidNextStatuses(currentStatus: string, orderType: string): string[] {\n  const workflows = {\n    delivery: {\n      confirmed: ['preparing'],\n      preparing: ['ready_for_delivery'],\n      ready_for_delivery: ['with_driver'],\n      with_driver: ['on_the_way'],\n      on_the_way: ['delivered'],\n      delivered: ['completed']\n    },\n    takeaway: {\n      confirmed: ['preparing'],\n      preparing: ['ready_for_pickup'],\n      ready_for_pickup: ['completed']\n    }\n  };\n\n  const workflow = workflows[orderType as keyof typeof workflows] || workflows.takeaway;\n  return workflow[currentStatus as keyof typeof workflow] || [];\n}\n\nexport async function registerRoutes(app: Express): Promise<http.Server> {\n  const apiRouter = Router();\n\n  // Logging middleware\n  app.use((req: Request, _res: Response, next: NextFunction) => {\n    if (req.url.startsWith('/api')) {\n      log(`${req.method} ${req.url}`, 'api');\n    }\n    next();\n  });\n\n  // Reusable error handler\n  const handleError = (error: any, res: Response) => {\n    console.error(error);\n    res.status(500).json({ error: 'An unexpected error occurred' });\n  };\n\n  // Menu Items / Dishes Routes\n  apiRouter.get('/dishes', async (req: Request, res: Response) => {\n    try {\n      const dishes = await storage.getAllDishes();\n      res.json(dishes);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  apiRouter.get('/dishes/:id', async (req: Request, res: Response) => {\n    try {\n      const id = parseInt(req.params.id);\n      const dish = await storage.getDishById(id);\n\n      if (!dish) {\n        return res.status(404).json({ error: 'Dish not found' });\n      }\n\n      res.json(dish);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  // Categories Routes\n  apiRouter.get('/categories', async (req: Request, res: Response) => {\n    try {\n      const categories = await storage.getAllMenuCategories();\n      res.json(categories);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  apiRouter.post('/categories', async (req: Request, res: Response) => {\n    try {\n      if (!req.body.name) {\n        return res.status(400).json({ error: 'Category name is required' });\n      }\n\n      const newCategory = await storage.createMenuCategory(req.body);\n      res.status(201).json(newCategory);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  apiRouter.put('/categories/:id', async (req: Request, res: Response) => {\n    try {\n      const id = parseInt(req.params.id);\n      const category = await storage.getMenuCategoryById(id);\n\n      if (!category) {\n        return res.status(404).json({ error: 'Category not found' });\n      }\n\n      const updatedCategory = await storage.updateMenuCategory(id, req.body);\n      res.json(updatedCategory);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  apiRouter.delete('/categories/:id', async (req: Request, res: Response) => {\n    try {\n      const id = parseInt(req.params.id);\n      const category = await storage.getMenuCategoryById(id);\n\n      if (!category) {\n        return res.status(404).json({ error: 'Category not found' });\n      }\n\n      const deletedCategory = await storage.deleteMenuCategory(id);\n      res.json({ message: 'Category deleted successfully', category: deletedCategory });\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  // Menu Items Routes\n  apiRouter.get('/items', async (req: Request, res: Response) => {\n    try {\n      const items = await storage.getAllMenuItems();\n      res.json(items);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  apiRouter.post('/items', async (req: Request, res: Response) => {\n    try {\n      const newItem = await storage.createMenuItem(req.body);\n      res.status(201).json(newItem);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  apiRouter.put('/items/:id', async (req: Request, res: Response) => {\n    try {\n      const id = parseInt(req.params.id);\n      const item = await storage.getMenuItemById(id);\n\n      if (!item) {\n        return res.status(404).json({ error: 'Menu item not found' });\n      }\n\n      // Update logic would go here\n      res.json({ ...item, ...req.body });\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  // Order Routes\n  apiRouter.post('/orders', async (req: Request, res: Response) => {\n    try {\n      // Get restaurant settings to check if it's open\n      const adminSettings = await fetch('http://localhost:5000/api/admin/settings').then(res => res.json());\n\n      // Check if restaurant is open\n      if (!adminSettings.restaurant_open) {\n        return res.status(403).json({\n          error: 'Restaurant is currently closed. Orders cannot be placed at this time.'\n        });\n      }\n\n      // If restaurant is open, proceed with order creation\n      const newOrder = await storage.createOrder(req.body);\n      res.status(201).json(newOrder);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  apiRouter.get('/orders/:id', async (req: Request, res: Response) => {\n    try {\n      const id = parseInt(req.params.id);\n      const order = await storage.getOrderById(id);\n\n      if (!order) {\n        return res.status(404).json({ error: 'Order not found' });\n      }\n\n      res.json(order);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  // Contact Form Route\n  apiRouter.post('/contact', async (req: Request, res: Response) => {\n    try {\n      const message = await storage.createContactMessage(req.body);\n      res.status(201).json({ success: true, message });\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  // Cart Routes\n  apiRouter.post('/cart', async (req: Request, res: Response) => {\n    try {\n      const result = await storage.createCart(req.body);\n      res.status(201).json(result);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  // Public Settings Route (for checkout page to get delivery fee)\n  apiRouter.get('/settings', async (req: Request, res: Response) => {\n    try {\n      const settings = await storage.getRestaurantSettings();\n\n      if (!settings) {\n        // Return default settings if none exist\n        return res.json({\n          delivery_fee: 49,\n          estimated_time: \"25-35 min\",\n          restaurant_open: true\n        });\n      }\n\n      // Return only public settings (no business hours)\n      res.json({\n        delivery_fee: settings.deliveryFee,\n        estimated_time: settings.estimatedTime,\n        restaurant_open: settings.restaurantOpen\n      });\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  // Public Customizations Route (for menu page)\n  apiRouter.get('/items/:itemId/customizations', async (req: Request, res: Response) => {\n    try {\n      const itemId = parseInt(req.params.itemId);\n      const customizations = await storage.getCustomizationOptionsForMenuItem(itemId);\n      res.json(customizations);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  // Register the API router with app\n  app.use('/api', apiRouter);\n\n  // Setup authentication\n  setupAuth(app);\n\n  // Create staff-accessible order routes (for managers and drivers)\n  // These routes need to be registered before the general admin routes\n  app.get('/api/admin/orders', isStaff, async (req: Request, res: Response) => {\n    try {\n      const statusFilter = req.query.status as string;\n      // Get orders from database\n      let orders = await storage.getAllOrders();\n\n      // Apply status filtering if applicable\n      if (statusFilter && statusFilter !== 'all') {\n        if (statusFilter === 'active') {\n          // Active orders are those that are not completed or cancelled\n          orders = orders.filter(order =>\n            !['completed', 'cancelled'].includes(order.status)\n          );\n        } else {\n          orders = orders.filter(order => order.status === statusFilter);\n        }\n      }\n\n      res.json(orders);\n    } catch (error) {\n      console.error('Error fetching orders:', error);\n      res.status(500).json({ error: 'Failed to fetch orders' });\n    }\n  });\n\n  app.get('/api/admin/orders/:id', isStaff, async (req: Request, res: Response) => {\n    try {\n      const orderId = parseInt(req.params.id);\n      const order = await storage.getOrderById(orderId);\n\n      if (!order) {\n        return res.status(404).json({ error: 'Order not found' });\n      }\n\n      res.json(order);\n    } catch (error) {\n      console.error(`Error fetching order ${req.params.id}:`, error);\n      res.status(500).json({ error: 'Failed to fetch order' });\n    }\n  });\n\n  app.put('/api/admin/orders/:id/status', isStaff, async (req: Request, res: Response) => {\n    try {\n      const orderId = parseInt(req.params.id);\n      const { newStatus } = req.body;\n\n      if (!newStatus) {\n        return res.status(400).json({ error: 'New status is required' });\n      }\n\n      // Get current order to validate status transition\n      const currentOrder = await storage.getOrderById(orderId);\n      if (!currentOrder) {\n        return res.status(404).json({ error: 'Order not found' });\n      }\n\n      // Validate status transition\n      const validStatuses = getValidNextStatuses(currentOrder.status, currentOrder.orderDetails?.type || 'takeaway');\n      if (!validStatuses.includes(newStatus)) {\n        return res.status(400).json({\n          error: `Invalid status transition from ${currentOrder.status} to ${newStatus}`,\n          validStatuses\n        });\n      }\n\n      // Update the order status in the database\n      const updatedOrder = await storage.updateOrder(orderId, { status: newStatus });\n\n      if (!updatedOrder) {\n        return res.status(500).json({ error: 'Failed to update order status' });\n      }\n\n      res.json({\n        success: true,\n        message: `Order ${orderId} status updated to ${newStatus}`,\n        order: updatedOrder\n      });\n    } catch (error) {\n      console.error(`Error updating order status for ${req.params.id}:`, error);\n      res.status(500).json({ error: 'Failed to update order status' });\n    }\n  });\n\n  app.post('/api/admin/dispatch/to-driver', isStaff, async (req: Request, res: Response) => {\n    try {\n      const { orderId } = req.body;\n\n      if (!orderId) {\n        return res.status(400).json({ error: 'Order ID is required' });\n      }\n\n      // In a real app, this would dispatch to a driver system\n      // For now, we just acknowledge receipt\n      res.json({\n        success: true,\n        message: `Order #${orderId} has been dispatched to a driver`,\n        timestamp: new Date().toISOString()\n      });\n    } catch (error) {\n      console.error('Error dispatching order to driver:', error);\n      res.status(500).json({ error: 'Failed to dispatch order to driver' });\n    }\n  });\n\n  // Register admin API routes with admin protection (for settings, menu management, etc.)\n  app.use('/api/admin', isAdmin, adminApiRouter);\n\n  // Create and return the server\n  const server = http.createServer(app);\n  return server;\n}"}