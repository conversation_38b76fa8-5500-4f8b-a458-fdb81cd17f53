{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/manager/ManagerLayout.tsx"}, "originalCode": "import { ReactNode } from \"react\";\nimport { useLocation, Link } from \"wouter\";\nimport { motion } from \"framer-motion\";\nimport { ChefHat, Home, LogOut, Bell } from \"lucide-react\";\n\ninterface ManagerLayoutProps {\n  children: ReactNode;\n}\n\nconst ManagerLayout = ({ children }: ManagerLayoutProps) => {\n  const [location] = useLocation();\n\n  return (\n    <div className=\"min-h-screen bg-black text-white flex flex-col\">\n      {/* Header */}\n      <motion.header\n        className=\"bg-gray-900/80 border-b border-gray-800 py-4 px-6 flex justify-between items-center backdrop-blur-md\"\n        initial={{ y: -20, opacity: 0 }}\n        animate={{ y: 0, opacity: 1 }}\n        transition={{ duration: 0.3 }}\n      >\n        <div className=\"flex items-center\">\n          <ChefHat className=\"h-6 w-6 text-orange-500 mr-3\" />\n          <h1 className=\"text-xl font-bold bg-gradient-to-r from-orange-400 via-yellow-400 to-orange-400 text-transparent bg-clip-text\">\n            Barbecuez Kitchen Manager\n          </h1>\n        </div>\n\n        <div className=\"flex items-center space-x-4\">\n          <motion.div\n            className=\"relative\"\n            whileHover={{ scale: 1.05 }}\n          >\n            <button className=\"p-2 rounded-full bg-gray-800 text-yellow-500 hover:bg-yellow-900/30 border border-yellow-800/50\">\n              <Bell className=\"w-5 h-5\" />\n            </button>\n            <span className=\"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center\">\n              3\n            </span>\n          </motion.div>\n\n          <Link href=\"/\">\n            <div className=\"flex items-center text-gray-400 hover:text-white hover:bg-gray-800/50 rounded-lg px-3 py-2 cursor-pointer\">\n              <Home className=\"w-5 h-5 mr-2\" />\n              <span>Home</span>\n            </div>\n          </Link>\n\n          <Link href=\"/logout\">\n            <div className=\"flex items-center text-red-400 hover:text-white hover:bg-red-900/50 rounded-lg px-3 py-2 cursor-pointer\">\n              <LogOut className=\"w-5 h-5 mr-2\" />\n              <span>Logout</span>\n            </div>\n          </Link>\n        </div>\n      </motion.header>\n\n      {/* Main Content */}\n      <main className=\"flex-grow p-4 md:p-6\">\n        <div className=\"max-w-6xl mx-auto\">\n          {children}\n        </div>\n      </main>\n\n      {/* Footer */}\n      <footer className=\"py-4 px-6 text-center text-gray-500 text-sm\">\n        &copy; {new Date().getFullYear()} Barbecuez Restaurant Kitchen Manager. All rights reserved.\n      </footer>\n    </div>\n  );\n};\n\nexport default ManagerLayout;", "modifiedCode": "import { ReactNode, useState } from \"react\";\nimport { useLocation, Link } from \"wouter\";\nimport { motion } from \"framer-motion\";\nimport { ChefHat, Home, LogOut, Bell } from \"lucide-react\";\nimport { useNotifications } from \"@/context/NotificationContext\";\nimport NotificationDropdown from \"@/components/NotificationDropdown\";\n\ninterface ManagerLayoutProps {\n  children: ReactNode;\n}\n\nconst ManagerLayout = ({ children }: ManagerLayoutProps) => {\n  const [location] = useLocation();\n\n  return (\n    <div className=\"min-h-screen bg-black text-white flex flex-col\">\n      {/* Header */}\n      <motion.header\n        className=\"bg-gray-900/80 border-b border-gray-800 py-4 px-6 flex justify-between items-center backdrop-blur-md\"\n        initial={{ y: -20, opacity: 0 }}\n        animate={{ y: 0, opacity: 1 }}\n        transition={{ duration: 0.3 }}\n      >\n        <div className=\"flex items-center\">\n          <ChefHat className=\"h-6 w-6 text-orange-500 mr-3\" />\n          <h1 className=\"text-xl font-bold bg-gradient-to-r from-orange-400 via-yellow-400 to-orange-400 text-transparent bg-clip-text\">\n            Barbecuez Kitchen Manager\n          </h1>\n        </div>\n\n        <div className=\"flex items-center space-x-4\">\n          <motion.div\n            className=\"relative\"\n            whileHover={{ scale: 1.05 }}\n          >\n            <button className=\"p-2 rounded-full bg-gray-800 text-yellow-500 hover:bg-yellow-900/30 border border-yellow-800/50\">\n              <Bell className=\"w-5 h-5\" />\n            </button>\n            <span className=\"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center\">\n              3\n            </span>\n          </motion.div>\n\n          <Link href=\"/\">\n            <div className=\"flex items-center text-gray-400 hover:text-white hover:bg-gray-800/50 rounded-lg px-3 py-2 cursor-pointer\">\n              <Home className=\"w-5 h-5 mr-2\" />\n              <span>Home</span>\n            </div>\n          </Link>\n\n          <Link href=\"/logout\">\n            <div className=\"flex items-center text-red-400 hover:text-white hover:bg-red-900/50 rounded-lg px-3 py-2 cursor-pointer\">\n              <LogOut className=\"w-5 h-5 mr-2\" />\n              <span>Logout</span>\n            </div>\n          </Link>\n        </div>\n      </motion.header>\n\n      {/* Main Content */}\n      <main className=\"flex-grow p-4 md:p-6\">\n        <div className=\"max-w-6xl mx-auto\">\n          {children}\n        </div>\n      </main>\n\n      {/* Footer */}\n      <footer className=\"py-4 px-6 text-center text-gray-500 text-sm\">\n        &copy; {new Date().getFullYear()} Barbecuez Restaurant Kitchen Manager. All rights reserved.\n      </footer>\n    </div>\n  );\n};\n\nexport default ManagerLayout;"}