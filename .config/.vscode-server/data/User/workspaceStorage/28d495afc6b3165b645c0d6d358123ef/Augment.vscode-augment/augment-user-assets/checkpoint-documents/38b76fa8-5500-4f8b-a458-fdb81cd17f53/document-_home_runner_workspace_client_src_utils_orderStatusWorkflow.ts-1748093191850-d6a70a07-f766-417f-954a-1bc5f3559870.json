{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/utils/orderStatusWorkflow.ts"}, "modifiedCode": "// Order Status Workflow System\n// Defines valid status transitions and workflow logic for different order types\n\nexport const ORDER_STATUSES = {\n  // Initial status\n  CONFIRMED: 'confirmed',\n  \n  // Kitchen workflow\n  PREPARING: 'preparing',\n  \n  // Ready states (different for delivery vs takeaway)\n  READY_FOR_PICKUP: 'ready_for_pickup',\n  READY_FOR_DELIVERY: 'ready_for_delivery',\n  \n  // Driver workflow (delivery only)\n  WITH_DRIVER: 'with_driver',\n  ON_THE_WAY: 'on_the_way',\n  \n  // Final states\n  DELIVERED: 'delivered',\n  COMPLETED: 'completed',\n  CANCELLED: 'cancelled'\n} as const;\n\nexport const ORDER_TYPES = {\n  DELIVERY: 'delivery',\n  TAKEAWAY: 'takeaway'\n} as const;\n\nexport type OrderStatus = typeof ORDER_STATUSES[keyof typeof ORDER_STATUSES];\nexport type OrderType = typeof ORDER_TYPES[keyof typeof ORDER_TYPES];\n\n// Define valid status transitions for each order type\nexport const STATUS_WORKFLOWS = {\n  [ORDER_TYPES.DELIVERY]: {\n    [ORDER_STATUSES.CONFIRMED]: [ORDER_STATUSES.PREPARING],\n    [ORDER_STATUSES.PREPARING]: [ORDER_STATUSES.READY_FOR_DELIVERY],\n    [ORDER_STATUSES.READY_FOR_DELIVERY]: [ORDER_STATUSES.WITH_DRIVER],\n    [ORDER_STATUSES.WITH_DRIVER]: [ORDER_STATUSES.ON_THE_WAY],\n    [ORDER_STATUSES.ON_THE_WAY]: [ORDER_STATUSES.DELIVERED],\n    [ORDER_STATUSES.DELIVERED]: [ORDER_STATUSES.COMPLETED],\n  },\n  [ORDER_TYPES.TAKEAWAY]: {\n    [ORDER_STATUSES.CONFIRMED]: [ORDER_STATUSES.PREPARING],\n    [ORDER_STATUSES.PREPARING]: [ORDER_STATUSES.READY_FOR_PICKUP],\n    [ORDER_STATUSES.READY_FOR_PICKUP]: [ORDER_STATUSES.COMPLETED],\n  }\n} as const;\n\n// Status display information\nexport const STATUS_INFO = {\n  [ORDER_STATUSES.CONFIRMED]: {\n    label: 'Order Confirmed',\n    description: 'Your order has been received and confirmed',\n    color: 'blue',\n    icon: 'bell'\n  },\n  [ORDER_STATUSES.PREPARING]: {\n    label: 'Preparing',\n    description: 'Your order is being prepared in the kitchen',\n    color: 'orange',\n    icon: 'flame'\n  },\n  [ORDER_STATUSES.READY_FOR_PICKUP]: {\n    label: 'Ready for Pickup',\n    description: 'Your order is ready for collection',\n    color: 'green',\n    icon: 'package'\n  },\n  [ORDER_STATUSES.READY_FOR_DELIVERY]: {\n    label: 'Ready for Delivery',\n    description: 'Your order is ready and waiting for a driver',\n    color: 'green',\n    icon: 'truck'\n  },\n  [ORDER_STATUSES.WITH_DRIVER]: {\n    label: 'With Driver',\n    description: 'A driver has collected your order',\n    color: 'cyan',\n    icon: 'user'\n  },\n  [ORDER_STATUSES.ON_THE_WAY]: {\n    label: 'On the Way',\n    description: 'Your order is on its way to you',\n    color: 'purple',\n    icon: 'truck'\n  },\n  [ORDER_STATUSES.DELIVERED]: {\n    label: 'Delivered',\n    description: 'Your order has been delivered',\n    color: 'green',\n    icon: 'check'\n  },\n  [ORDER_STATUSES.COMPLETED]: {\n    label: 'Completed',\n    description: 'Order completed successfully',\n    color: 'teal',\n    icon: 'check-circle'\n  },\n  [ORDER_STATUSES.CANCELLED]: {\n    label: 'Cancelled',\n    description: 'Order has been cancelled',\n    color: 'red',\n    icon: 'x'\n  }\n} as const;\n\n// Workflow validation functions\nexport function isValidStatusTransition(\n  currentStatus: OrderStatus,\n  newStatus: OrderStatus,\n  orderType: OrderType\n): boolean {\n  const workflow = STATUS_WORKFLOWS[orderType];\n  const validNextStatuses = workflow[currentStatus as keyof typeof workflow];\n  return validNextStatuses?.includes(newStatus as any) || false;\n}\n\nexport function getNextValidStatuses(\n  currentStatus: OrderStatus,\n  orderType: OrderType\n): OrderStatus[] {\n  const workflow = STATUS_WORKFLOWS[orderType];\n  return (workflow[currentStatus as keyof typeof workflow] as OrderStatus[]) || [];\n}\n\nexport function getNextStatus(\n  currentStatus: OrderStatus,\n  orderType: OrderType\n): OrderStatus | null {\n  const validNextStatuses = getNextValidStatuses(currentStatus, orderType);\n  return validNextStatuses.length > 0 ? validNextStatuses[0] : null;\n}\n\n// Check if status requires driver assignment\nexport function requiresDriverAssignment(status: OrderStatus): boolean {\n  return status === ORDER_STATUSES.READY_FOR_DELIVERY;\n}\n\n// Check if order should be visible to drivers\nexport function isDriverOrder(status: OrderStatus, orderType: OrderType): boolean {\n  return orderType === ORDER_TYPES.DELIVERY && [\n    ORDER_STATUSES.READY_FOR_DELIVERY,\n    ORDER_STATUSES.WITH_DRIVER,\n    ORDER_STATUSES.ON_THE_WAY\n  ].includes(status);\n}\n\n// Check if order should be visible to managers\nexport function isManagerOrder(status: OrderStatus): boolean {\n  return [\n    ORDER_STATUSES.CONFIRMED,\n    ORDER_STATUSES.PREPARING,\n    ORDER_STATUSES.READY_FOR_PICKUP,\n    ORDER_STATUSES.READY_FOR_DELIVERY\n  ].includes(status);\n}\n\n// Get status timeline for order tracker\nexport function getStatusTimeline(orderType: OrderType): OrderStatus[] {\n  if (orderType === ORDER_TYPES.DELIVERY) {\n    return [\n      ORDER_STATUSES.CONFIRMED,\n      ORDER_STATUSES.PREPARING,\n      ORDER_STATUSES.READY_FOR_DELIVERY,\n      ORDER_STATUSES.WITH_DRIVER,\n      ORDER_STATUSES.ON_THE_WAY,\n      ORDER_STATUSES.DELIVERED\n    ];\n  } else {\n    return [\n      ORDER_STATUSES.CONFIRMED,\n      ORDER_STATUSES.PREPARING,\n      ORDER_STATUSES.READY_FOR_PICKUP\n    ];\n  }\n}\n\n// Get user-friendly status label\nexport function getStatusLabel(status: OrderStatus): string {\n  return STATUS_INFO[status]?.label || status.replace(/_/g, ' ');\n}\n\n// Get status description\nexport function getStatusDescription(status: OrderStatus): string {\n  return STATUS_INFO[status]?.description || '';\n}\n\n// Get status color theme\nexport function getStatusColor(status: OrderStatus): string {\n  return STATUS_INFO[status]?.color || 'gray';\n}\n"}