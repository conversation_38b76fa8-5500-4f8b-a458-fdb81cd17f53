{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/hooks/useAudioNotification.ts"}, "originalCode": "import { useCallback, useRef } from 'react';\nimport { useNotifications } from '@/context/NotificationContext';\n\ninterface AudioNotificationOptions {\n  volume?: number;\n  fallbackToBeep?: boolean;\n}\n\nexport const useAudioNotification = (options: AudioNotificationOptions = {}) => {\n  const { soundEnabled } = useNotifications();\n  const audioRef = useRef<HTMLAudioElement | null>(null);\n  const { volume = 0.5, fallbackToBeep = true } = options;\n\n  const playNotificationSound = useCallback(async (soundType: 'new_order' | 'status_update' | 'error' = 'new_order') => {\n    if (!soundEnabled) return;\n\n    try {\n      // Define sound files for different notification types\n      const soundFiles = {\n        new_order: '/sounds/new-order.mp3',\n        status_update: '/sounds/status-update.mp3',\n        error: '/sounds/error.mp3'\n      };\n\n      const soundFile = soundFiles[soundType];\n\n      // Create or reuse audio element\n      if (!audioRef.current) {\n        audioRef.current = new Audio();\n      }\n\n      const audio = audioRef.current;\n      audio.src = soundFile;\n      audio.volume = volume;\n\n      // Attempt to play the sound\n      await audio.play();\n    } catch (error) {\n      console.warn('Could not play notification sound:', error);\n\n      // Fallback to system beep if enabled and available\n      if (fallbackToBeep && 'AudioContext' in window) {\n        try {\n          await playSystemBeep();\n        } catch (beepError) {\n          console.warn('Could not play system beep:', beepError);\n        }\n      }\n    }\n  }, [soundEnabled, volume, fallbackToBeep]);\n\n  const playSystemBeep = useCallback(async () => {\n    const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();\n    const oscillator = audioContext.createOscillator();\n    const gainNode = audioContext.createGain();\n\n    oscillator.connect(gainNode);\n    gainNode.connect(audioContext.destination);\n\n    oscillator.frequency.setValueAtTime(800, audioContext.currentTime);\n    oscillator.type = 'sine';\n\n    gainNode.gain.setValueAtTime(0, audioContext.currentTime);\n    gainNode.gain.linearRampToValueAtTime(0.3, audioContext.currentTime + 0.01);\n    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);\n\n    oscillator.start(audioContext.currentTime);\n    oscillator.stop(audioContext.currentTime + 0.3);\n\n    // Clean up\n    setTimeout(() => {\n      audioContext.close();\n    }, 500);\n  }, []);\n\n  const preloadSounds = useCallback(() => {\n    const soundFiles = [\n      '/sounds/new-order.mp3',\n      '/sounds/status-update.mp3',\n      '/sounds/error.mp3'\n    ];\n\n    soundFiles.forEach(soundFile => {\n      const audio = new Audio();\n      audio.preload = 'auto';\n      audio.src = soundFile;\n      // Don't store these, just preload them\n    });\n  }, []);\n\n  return {\n    playNotificationSound,\n    playSystemBeep,\n    preloadSounds,\n    soundEnabled\n  };\n};\n", "modifiedCode": "import { useCallback, useRef } from 'react';\nimport { useNotifications } from '@/context/NotificationContext';\n\ninterface AudioNotificationOptions {\n  volume?: number;\n  fallbackToBeep?: boolean;\n}\n\nexport const useAudioNotification = (options: AudioNotificationOptions = {}) => {\n  const { soundEnabled } = useNotifications();\n  const audioRef = useRef<HTMLAudioElement | null>(null);\n  const { volume = 0.5, fallbackToBeep = true } = options;\n\n  const playNotificationSound = useCallback(async (soundType: 'new_order' | 'status_update' | 'error' = 'new_order') => {\n    if (!soundEnabled) return;\n\n    try {\n      // For now, use programmatically generated sounds since we don't have audio files yet\n      // In production, replace this with actual sound files\n      await playSystemBeep(soundType);\n    } catch (error) {\n      console.warn('Could not play notification sound:', error);\n    }\n  }, [soundEnabled]);\n\n  const playSystemBeep = useCallback(async () => {\n    const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();\n    const oscillator = audioContext.createOscillator();\n    const gainNode = audioContext.createGain();\n\n    oscillator.connect(gainNode);\n    gainNode.connect(audioContext.destination);\n\n    oscillator.frequency.setValueAtTime(800, audioContext.currentTime);\n    oscillator.type = 'sine';\n\n    gainNode.gain.setValueAtTime(0, audioContext.currentTime);\n    gainNode.gain.linearRampToValueAtTime(0.3, audioContext.currentTime + 0.01);\n    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);\n\n    oscillator.start(audioContext.currentTime);\n    oscillator.stop(audioContext.currentTime + 0.3);\n\n    // Clean up\n    setTimeout(() => {\n      audioContext.close();\n    }, 500);\n  }, []);\n\n  const preloadSounds = useCallback(() => {\n    const soundFiles = [\n      '/sounds/new-order.mp3',\n      '/sounds/status-update.mp3',\n      '/sounds/error.mp3'\n    ];\n\n    soundFiles.forEach(soundFile => {\n      const audio = new Audio();\n      audio.preload = 'auto';\n      audio.src = soundFile;\n      // Don't store these, just preload them\n    });\n  }, []);\n\n  return {\n    playNotificationSound,\n    playSystemBeep,\n    preloadSounds,\n    soundEnabled\n  };\n};\n"}