{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "server/auth.ts"}, "originalCode": "import passport from \"passport\";\nimport { Strategy as LocalStrategy } from \"passport-local\";\nimport { Express } from \"express\";\nimport session from \"express-session\";\nimport { promisify } from \"util\";\nimport * as crypto from \"crypto\";\nimport { db } from \"./db\";\nimport { users, loginUserSchema } from \"@shared/schema\";\nimport { eq } from \"drizzle-orm\";\nimport connectPg from \"connect-pg-simple\";\nimport { pool } from \"./db\";\n\ndeclare global {\n  namespace Express {\n    interface User {\n      id: number;\n      username: string;\n      email: string;\n      first_name: string | null;\n      last_name: string | null;\n      role: string;\n      is_active: boolean;\n      created_at: Date | null;\n      updated_at: Date | null;\n    }\n  }\n}\n\n// Convert scrypt to promise-based\nconst scryptAsync = promisify(crypto.scrypt);\n\n// Hash password function\nasync function hashPassword(password: string): Promise<string> {\n  const salt = crypto.randomBytes(16).toString(\"hex\");\n  const buf = (await scryptAsync(password, salt, 64)) as Buffer;\n  return `${buf.toString(\"hex\")}.${salt}`;\n}\n\n// Compare password function\nasync function comparePasswords(supplied: string, stored: string): Promise<boolean> {\n  const [hashed, salt] = stored.split(\".\");\n  const hashedBuf = Buffer.from(hashed, \"hex\");\n  const suppliedBuf = (await scryptAsync(supplied, salt, 64)) as Buffer;\n  return crypto.timingSafeEqual(hashedBuf, suppliedBuf);\n}\n\nexport function setupAuth(app: Express) {\n  const PostgresSessionStore = connectPg(session);\n\n  const sessionSettings: session.SessionOptions = {\n    secret: process.env.SESSION_SECRET || \"barbecuez-restaurant-secret\",\n    resave: false,\n    saveUninitialized: false,\n    cookie: {\n      secure: process.env.NODE_ENV === \"production\",\n      maxAge: 1000 * 60 * 60 * 24, // 1 day\n    },\n    store: new PostgresSessionStore({\n      pool,\n      tableName: 'session',\n      createTableIfMissing: true\n    })\n  };\n\n  app.set(\"trust proxy\", 1);\n  app.use(session(sessionSettings));\n  app.use(passport.initialize());\n  app.use(passport.session());\n\n  // Setup Local Strategy\n  passport.use(\n    new LocalStrategy(async (username, password, done) => {\n      try {\n        // Find user in database with direct database query instead of Drizzle\n        const userResult = await pool.query(\n          'SELECT * FROM users WHERE username = $1',\n          [username]\n        );\n\n        if (userResult.rows.length === 0) {\n          return done(null, false, { message: \"Incorrect username.\" });\n        }\n\n        const user = userResult.rows[0];\n\n        // Check password\n        const isValidPassword = await comparePasswords(password, user.password);\n\n        if (!isValidPassword) {\n          return done(null, false, { message: \"Incorrect password.\" });\n        }\n\n        // Success - return user without password\n        const { password: _, ...userWithoutPassword } = user;\n        return done(null, userWithoutPassword as Express.User);\n      } catch (error) {\n        return done(error);\n      }\n    })\n  );\n\n  // Serialize and deserialize user\n  passport.serializeUser((user, done) => {\n    done(null, user.id);\n  });\n\n  passport.deserializeUser(async (id: number, done) => {\n    try {\n      // Direct database query instead of Drizzle\n      const userResult = await pool.query(\n        'SELECT * FROM users WHERE id = $1',\n        [id]\n      );\n\n      if (userResult.rows.length === 0) {\n        return done(null, false);\n      }\n\n      const user = userResult.rows[0];\n      const { password: _, ...userWithoutPassword } = user;\n      done(null, userWithoutPassword as Express.User);\n    } catch (error) {\n      done(error);\n    }\n  });\n\n  // Registration endpoint\n  app.post(\"/api/register\", async (req, res) => {\n    try {\n      // Validate request body\n      const validation = loginUserSchema.safeParse(req.body);\n      if (!validation.success) {\n        return res.status(400).json({ error: validation.error.message });\n      }\n\n      // Check if username already exists using direct query\n      const existingCheck = await pool.query(\n        'SELECT EXISTS(SELECT 1 FROM users WHERE username = $1)',\n        [req.body.username]\n      );\n\n      if (existingCheck.rows[0].exists) {\n        return res.status(400).json({ error: \"Username already exists\" });\n      }\n\n      // Hash password\n      const hashedPassword = await hashPassword(req.body.password);\n\n      // Insert new user with direct query\n      const result = await pool.query(\n        `INSERT INTO users (username, email, password, role)\n         VALUES ($1, $2, $3, $4)\n         RETURNING id, username, email, first_name, last_name, role, is_active, created_at, updated_at`,\n        [\n          req.body.username,\n          req.body.email || `${req.body.username}@example.com`, // Fallback for required email\n          hashedPassword,\n          req.body.role || \"customer\"\n        ]\n      );\n\n      const user = result.rows[0];\n\n      // Log user in\n      req.login(user as Express.User, (err) => {\n        if (err) {\n          return res.status(500).json({ error: err.message });\n        }\n\n        return res.status(201).json(user);\n      });\n    } catch (error) {\n      console.error(\"Registration error:\", error);\n      return res.status(500).json({ error: \"Server error during registration\" });\n    }\n  });\n\n  // Login endpoint\n  app.post(\"/api/login\", (req, res, next) => {\n    passport.authenticate(\"local\", (err: Error | null, user: Express.User | false, info: { message: string } | undefined) => {\n      if (err) {\n        return next(err);\n      }\n\n      if (!user) {\n        return res.status(401).json({ error: info?.message || \"Invalid credentials\" });\n      }\n\n      req.login(user, (loginErr: Error | null) => {\n        if (loginErr) {\n          return next(loginErr);\n        }\n        console.log(\"User logged in successfully:\", user.username);\n        return res.status(200).json(user);\n      });\n    })(req, res, next);\n  });\n\n  // Logout endpoint\n  app.post(\"/api/logout\", (req, res, next) => {\n    req.logout((err: Error | null) => {\n      if (err) {\n        return next(err);\n      }\n      req.session.destroy((sessionErr) => {\n        if (sessionErr) {\n          console.error(\"Error destroying session:\", sessionErr);\n        }\n        res.clearCookie('connect.sid');\n        res.status(200).json({ message: \"Logged out successfully\" });\n      });\n    });\n  });\n\n  // Get current user endpoint\n  app.get(\"/api/user\", (req, res) => {\n    if (!req.isAuthenticated()) {\n      return res.status(401).json({ error: \"Not authenticated\" });\n    }\n\n    res.status(200).json(req.user);\n  });\n}\n\n// Middleware to check if user is authenticated\nexport function isAuthenticated(req: any, res: any, next: any) {\n  if (req.isAuthenticated()) {\n    return next();\n  }\n  return res.status(401).json({ error: \"Authentication required\" });\n}\n\n// Middleware to check if user is admin\nexport function isAdmin(req: any, res: any, next: any) {\n  if (req.isAuthenticated() && req.user?.role === 'admin') {\n    return next();\n  }\n  return res.status(403).json({ error: \"Admin access required\" });\n}", "modifiedCode": "import passport from \"passport\";\nimport { Strategy as LocalStrategy } from \"passport-local\";\nimport { Express } from \"express\";\nimport session from \"express-session\";\nimport { promisify } from \"util\";\nimport * as crypto from \"crypto\";\nimport { db } from \"./db\";\nimport { users, loginUserSchema } from \"@shared/schema\";\nimport { eq } from \"drizzle-orm\";\nimport connectPg from \"connect-pg-simple\";\nimport { pool } from \"./db\";\n\ndeclare global {\n  namespace Express {\n    interface User {\n      id: number;\n      username: string;\n      email: string;\n      first_name: string | null;\n      last_name: string | null;\n      role: string;\n      is_active: boolean;\n      created_at: Date | null;\n      updated_at: Date | null;\n    }\n  }\n}\n\n// Convert scrypt to promise-based\nconst scryptAsync = promisify(crypto.scrypt);\n\n// Hash password function\nasync function hashPassword(password: string): Promise<string> {\n  const salt = crypto.randomBytes(16).toString(\"hex\");\n  const buf = (await scryptAsync(password, salt, 64)) as Buffer;\n  return `${buf.toString(\"hex\")}.${salt}`;\n}\n\n// Compare password function\nasync function comparePasswords(supplied: string, stored: string): Promise<boolean> {\n  const [hashed, salt] = stored.split(\".\");\n  const hashedBuf = Buffer.from(hashed, \"hex\");\n  const suppliedBuf = (await scryptAsync(supplied, salt, 64)) as Buffer;\n  return crypto.timingSafeEqual(hashedBuf, suppliedBuf);\n}\n\nexport function setupAuth(app: Express) {\n  const PostgresSessionStore = connectPg(session);\n\n  const sessionSettings: session.SessionOptions = {\n    secret: process.env.SESSION_SECRET || \"barbecuez-restaurant-secret\",\n    resave: false,\n    saveUninitialized: false,\n    cookie: {\n      secure: process.env.NODE_ENV === \"production\",\n      maxAge: 1000 * 60 * 60 * 24, // 1 day\n    },\n    store: new PostgresSessionStore({\n      pool,\n      tableName: 'session',\n      createTableIfMissing: true\n    })\n  };\n\n  app.set(\"trust proxy\", 1);\n  app.use(session(sessionSettings));\n  app.use(passport.initialize());\n  app.use(passport.session());\n\n  // Setup Local Strategy\n  passport.use(\n    new LocalStrategy(async (username, password, done) => {\n      try {\n        // Find user in database with direct database query instead of Drizzle\n        const userResult = await pool.query(\n          'SELECT * FROM users WHERE username = $1',\n          [username]\n        );\n\n        if (userResult.rows.length === 0) {\n          return done(null, false, { message: \"Incorrect username.\" });\n        }\n\n        const user = userResult.rows[0];\n\n        // Check password\n        const isValidPassword = await comparePasswords(password, user.password);\n\n        if (!isValidPassword) {\n          return done(null, false, { message: \"Incorrect password.\" });\n        }\n\n        // Success - return user without password\n        const { password: _, ...userWithoutPassword } = user;\n        return done(null, userWithoutPassword as Express.User);\n      } catch (error) {\n        return done(error);\n      }\n    })\n  );\n\n  // Serialize and deserialize user\n  passport.serializeUser((user, done) => {\n    done(null, user.id);\n  });\n\n  passport.deserializeUser(async (id: number, done) => {\n    try {\n      // Direct database query instead of Drizzle\n      const userResult = await pool.query(\n        'SELECT * FROM users WHERE id = $1',\n        [id]\n      );\n\n      if (userResult.rows.length === 0) {\n        return done(null, false);\n      }\n\n      const user = userResult.rows[0];\n      const { password: _, ...userWithoutPassword } = user;\n      done(null, userWithoutPassword as Express.User);\n    } catch (error) {\n      done(error);\n    }\n  });\n\n  // Registration endpoint\n  app.post(\"/api/register\", async (req, res) => {\n    try {\n      // Validate request body\n      const validation = loginUserSchema.safeParse(req.body);\n      if (!validation.success) {\n        return res.status(400).json({ error: validation.error.message });\n      }\n\n      // Check if username already exists using direct query\n      const existingCheck = await pool.query(\n        'SELECT EXISTS(SELECT 1 FROM users WHERE username = $1)',\n        [req.body.username]\n      );\n\n      if (existingCheck.rows[0].exists) {\n        return res.status(400).json({ error: \"Username already exists\" });\n      }\n\n      // Hash password\n      const hashedPassword = await hashPassword(req.body.password);\n\n      // Insert new user with direct query\n      const result = await pool.query(\n        `INSERT INTO users (username, email, password, role)\n         VALUES ($1, $2, $3, $4)\n         RETURNING id, username, email, first_name, last_name, role, is_active, created_at, updated_at`,\n        [\n          req.body.username,\n          req.body.email || `${req.body.username}@example.com`, // Fallback for required email\n          hashedPassword,\n          req.body.role || \"customer\"\n        ]\n      );\n\n      const user = result.rows[0];\n\n      // Log user in\n      req.login(user as Express.User, (err) => {\n        if (err) {\n          return res.status(500).json({ error: err.message });\n        }\n\n        return res.status(201).json(user);\n      });\n    } catch (error) {\n      console.error(\"Registration error:\", error);\n      return res.status(500).json({ error: \"Server error during registration\" });\n    }\n  });\n\n  // Login endpoint\n  app.post(\"/api/login\", (req, res, next) => {\n    passport.authenticate(\"local\", (err: Error | null, user: Express.User | false, info: { message: string } | undefined) => {\n      if (err) {\n        return next(err);\n      }\n\n      if (!user) {\n        return res.status(401).json({ error: info?.message || \"Invalid credentials\" });\n      }\n\n      req.login(user, (loginErr: Error | null) => {\n        if (loginErr) {\n          return next(loginErr);\n        }\n        console.log(\"User logged in successfully:\", user.username);\n        return res.status(200).json(user);\n      });\n    })(req, res, next);\n  });\n\n  // Logout endpoint\n  app.post(\"/api/logout\", (req, res, next) => {\n    req.logout((err: Error | null) => {\n      if (err) {\n        return next(err);\n      }\n      req.session.destroy((sessionErr) => {\n        if (sessionErr) {\n          console.error(\"Error destroying session:\", sessionErr);\n        }\n        res.clearCookie('connect.sid');\n        res.status(200).json({ message: \"Logged out successfully\" });\n      });\n    });\n  });\n\n  // Get current user endpoint\n  app.get(\"/api/user\", (req, res) => {\n    if (!req.isAuthenticated()) {\n      return res.status(401).json({ error: \"Not authenticated\" });\n    }\n\n    res.status(200).json(req.user);\n  });\n}\n\n// Middleware to check if user is authenticated\nexport function isAuthenticated(req: any, res: any, next: any) {\n  if (req.isAuthenticated()) {\n    return next();\n  }\n  return res.status(401).json({ error: \"Authentication required\" });\n}\n\n// Middleware to check if user is admin\nexport function isAdmin(req: any, res: any, next: any) {\n  if (req.isAuthenticated() && req.user?.role === 'admin') {\n    return next();\n  }\n  return res.status(403).json({ error: \"Admin access required\" });\n}\n\n// Middleware to check if user has staff access (admin, manager, or driver)\nexport function isStaff(req: any, res: any, next: any) {\n  if (req.isAuthenticated() && ['admin', 'manager', 'driver'].includes(req.user?.role)) {\n    return next();\n  }\n  return res.status(403).json({ error: \"Staff access required\" });\n}\n\n// Middleware to check if user is manager or admin\nexport function isManagerOrAdmin(req: any, res: any, next: any) {\n  if (req.isAuthenticated() && ['admin', 'manager'].includes(req.user?.role)) {\n    return next();\n  }\n  return res.status(403).json({ error: \"Manager or admin access required\" });\n}"}