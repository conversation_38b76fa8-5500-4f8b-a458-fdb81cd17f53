{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/NotificationDropdown.tsx"}, "modifiedCode": "import { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { \n  <PERSON>, \n  <PERSON>, \n  <PERSON>, \n  <PERSON><PERSON>he<PERSON>, \n  Trash2, \n  Volume2, \n  VolumeX,\n  Clock,\n  User,\n  Package\n} from 'lucide-react';\nimport { useNotifications, Notification } from '@/context/NotificationContext';\nimport { formatDistanceToNow } from 'date-fns';\n\ninterface NotificationDropdownProps {\n  isOpen: boolean;\n  onClose: () => void;\n}\n\nconst NotificationDropdown = ({ isOpen, onClose }: NotificationDropdownProps) => {\n  const {\n    notifications,\n    unreadCount,\n    soundEnabled,\n    markAsRead,\n    markAllAsRead,\n    removeNotification,\n    clearAllNotifications,\n    toggleSound\n  } = useNotifications();\n\n  const [filter, setFilter] = useState<'all' | 'unread'>('all');\n\n  const filteredNotifications = filter === 'unread' \n    ? notifications.filter(n => !n.read)\n    : notifications;\n\n  const getNotificationIcon = (type: Notification['type']) => {\n    switch (type) {\n      case 'new_order':\n        return <Bell className=\"w-4 h-4 text-blue-400\" />;\n      case 'status_update':\n        return <Package className=\"w-4 h-4 text-green-400\" />;\n      case 'system':\n        return <Clock className=\"w-4 h-4 text-yellow-400\" />;\n      default:\n        return <Bell className=\"w-4 h-4 text-gray-400\" />;\n    }\n  };\n\n  const getPriorityColor = (priority: Notification['priority']) => {\n    switch (priority) {\n      case 'high':\n        return 'border-l-red-500 bg-red-900/10';\n      case 'medium':\n        return 'border-l-yellow-500 bg-yellow-900/10';\n      case 'low':\n        return 'border-l-blue-500 bg-blue-900/10';\n      default:\n        return 'border-l-gray-500 bg-gray-900/10';\n    }\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <>\n      {/* Backdrop */}\n      <div \n        className=\"fixed inset-0 z-40 bg-black/20\" \n        onClick={onClose}\n      />\n      \n      {/* Dropdown */}\n      <motion.div\n        initial={{ opacity: 0, y: -10, scale: 0.95 }}\n        animate={{ opacity: 1, y: 0, scale: 1 }}\n        exit={{ opacity: 0, y: -10, scale: 0.95 }}\n        transition={{ duration: 0.2 }}\n        className=\"absolute top-full right-0 mt-2 w-96 bg-gray-900 border border-gray-700 rounded-lg shadow-xl z-50 max-h-96 overflow-hidden\"\n      >\n        {/* Header */}\n        <div className=\"p-4 border-b border-gray-700\">\n          <div className=\"flex items-center justify-between mb-3\">\n            <h3 className=\"text-lg font-medium text-white\">Notifications</h3>\n            <button\n              onClick={onClose}\n              className=\"text-gray-400 hover:text-white transition-colors\"\n            >\n              <X className=\"w-5 h-5\" />\n            </button>\n          </div>\n          \n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex space-x-2\">\n              <button\n                onClick={() => setFilter('all')}\n                className={`px-3 py-1 text-sm rounded-full transition-colors ${\n                  filter === 'all' \n                    ? 'bg-blue-600 text-white' \n                    : 'bg-gray-800 text-gray-400 hover:text-white'\n                }`}\n              >\n                All ({notifications.length})\n              </button>\n              <button\n                onClick={() => setFilter('unread')}\n                className={`px-3 py-1 text-sm rounded-full transition-colors ${\n                  filter === 'unread' \n                    ? 'bg-blue-600 text-white' \n                    : 'bg-gray-800 text-gray-400 hover:text-white'\n                }`}\n              >\n                Unread ({unreadCount})\n              </button>\n            </div>\n            \n            <div className=\"flex items-center space-x-2\">\n              <button\n                onClick={toggleSound}\n                className={`p-1 rounded transition-colors ${\n                  soundEnabled \n                    ? 'text-green-400 hover:text-green-300' \n                    : 'text-gray-400 hover:text-gray-300'\n                }`}\n                title={soundEnabled ? 'Disable sound' : 'Enable sound'}\n              >\n                {soundEnabled ? <Volume2 className=\"w-4 h-4\" /> : <VolumeX className=\"w-4 h-4\" />}\n              </button>\n              \n              {unreadCount > 0 && (\n                <button\n                  onClick={markAllAsRead}\n                  className=\"text-blue-400 hover:text-blue-300 transition-colors\"\n                  title=\"Mark all as read\"\n                >\n                  <CheckCheck className=\"w-4 h-4\" />\n                </button>\n              )}\n              \n              {notifications.length > 0 && (\n                <button\n                  onClick={clearAllNotifications}\n                  className=\"text-red-400 hover:text-red-300 transition-colors\"\n                  title=\"Clear all notifications\"\n                >\n                  <Trash2 className=\"w-4 h-4\" />\n                </button>\n              )}\n            </div>\n          </div>\n        </div>\n\n        {/* Notifications List */}\n        <div className=\"max-h-80 overflow-y-auto\">\n          <AnimatePresence>\n            {filteredNotifications.length === 0 ? (\n              <div className=\"p-8 text-center text-gray-500\">\n                <Bell className=\"w-12 h-12 text-gray-700 mx-auto mb-3\" />\n                <p>No {filter === 'unread' ? 'unread ' : ''}notifications</p>\n              </div>\n            ) : (\n              filteredNotifications.map((notification) => (\n                <motion.div\n                  key={notification.id}\n                  initial={{ opacity: 0, x: -20 }}\n                  animate={{ opacity: 1, x: 0 }}\n                  exit={{ opacity: 0, x: 20 }}\n                  className={`p-4 border-l-4 border-b border-gray-800 hover:bg-gray-800/50 transition-colors cursor-pointer ${\n                    getPriorityColor(notification.priority)\n                  } ${!notification.read ? 'bg-gray-800/30' : ''}`}\n                  onClick={() => !notification.read && markAsRead(notification.id)}\n                >\n                  <div className=\"flex items-start justify-between\">\n                    <div className=\"flex items-start space-x-3 flex-1\">\n                      <div className=\"mt-1\">\n                        {getNotificationIcon(notification.type)}\n                      </div>\n                      \n                      <div className=\"flex-1 min-w-0\">\n                        <div className=\"flex items-center space-x-2\">\n                          <h4 className={`text-sm font-medium ${\n                            notification.read ? 'text-gray-300' : 'text-white'\n                          }`}>\n                            {notification.title}\n                          </h4>\n                          {!notification.read && (\n                            <div className=\"w-2 h-2 bg-blue-500 rounded-full\"></div>\n                          )}\n                        </div>\n                        \n                        <p className={`text-sm mt-1 ${\n                          notification.read ? 'text-gray-500' : 'text-gray-400'\n                        }`}>\n                          {notification.message}\n                        </p>\n                        \n                        {notification.customerName && (\n                          <div className=\"flex items-center mt-2 text-xs text-gray-500\">\n                            <User className=\"w-3 h-3 mr-1\" />\n                            {notification.customerName}\n                          </div>\n                        )}\n                        \n                        <div className=\"flex items-center justify-between mt-2\">\n                          <span className=\"text-xs text-gray-500\">\n                            {formatDistanceToNow(notification.timestamp, { addSuffix: true })}\n                          </span>\n                          \n                          {notification.orderId && (\n                            <span className=\"text-xs text-blue-400\">\n                              Order #{notification.orderId}\n                            </span>\n                          )}\n                        </div>\n                      </div>\n                    </div>\n                    \n                    <div className=\"flex items-center space-x-1 ml-2\">\n                      {!notification.read && (\n                        <button\n                          onClick={(e) => {\n                            e.stopPropagation();\n                            markAsRead(notification.id);\n                          }}\n                          className=\"text-gray-400 hover:text-green-400 transition-colors\"\n                          title=\"Mark as read\"\n                        >\n                          <Check className=\"w-4 h-4\" />\n                        </button>\n                      )}\n                      \n                      <button\n                        onClick={(e) => {\n                          e.stopPropagation();\n                          removeNotification(notification.id);\n                        }}\n                        className=\"text-gray-400 hover:text-red-400 transition-colors\"\n                        title=\"Remove notification\"\n                      >\n                        <X className=\"w-4 h-4\" />\n                      </button>\n                    </div>\n                  </div>\n                </motion.div>\n              ))\n            )}\n          </AnimatePresence>\n        </div>\n      </motion.div>\n    </>\n  );\n};\n\nexport default NotificationDropdown;\n"}