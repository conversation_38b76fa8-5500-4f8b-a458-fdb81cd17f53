{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/manager/ManagerPage.tsx"}, "originalCode": "import { useState, useEffect, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  ChefHat,\n  Truck,\n  CheckCircle,\n  Clock,\n  Flame,\n  ShoppingBag,\n  Package,\n  Bell,\n  AlertTriangle\n} from 'lucide-react';\nimport ManagerLayout from './ManagerLayout';\nimport { format, parseISO } from 'date-fns';\nimport { useNotifications } from '@/context/NotificationContext';\nimport { useAudioNotification } from '@/hooks/useAudioNotification';\nimport {\n  ORDER_STATUSES,\n  ORDER_TYPES,\n  getNextStatus,\n  getStatusLabel,\n  getStatusColor,\n  isManagerOrder,\n  requiresDriverAssignment\n} from '@/utils/orderStatusWorkflow';\n\n// Use centralized status and type constants\nconst STATUS = ORDER_STATUSES;\nconst ORDER_TYPE = ORDER_TYPES;\n\n// Format currency\nconst formatCurrency = (amount: number) => {\n  return `$${(amount / 100).toFixed(2)}`;\n};\n\n// Format date for display\nconst formatDate = (dateString: string) => {\n  try {\n    const date = parseISO(dateString);\n    return format(date, 'MMM d, h:mm a');\n  } catch (e) {\n    return dateString;\n  }\n};\n\n// Format time elapsed since order was placed\nconst formatTimeElapsed = (dateString: string) => {\n  try {\n    const orderDate = parseISO(dateString);\n    const now = new Date();\n    const diffInMinutes = Math.floor((now.getTime() - orderDate.getTime()) / (1000 * 60));\n\n    if (diffInMinutes < 1) return 'Just now';\n    if (diffInMinutes === 1) return '1 minute ago';\n    if (diffInMinutes < 60) return `${diffInMinutes} minutes ago`;\n\n    const hours = Math.floor(diffInMinutes / 60);\n    if (hours === 1) return '1 hour ago';\n    return `${hours} hours ago`;\n  } catch (e) {\n    return 'Unknown time';\n  }\n};\n\ninterface OrderItem {\n  id: number;\n  name: string;\n  price: number;\n  quantity: number;\n}\n\ninterface OrderCustomer {\n  firstName: string;\n  lastName: string;\n  phone: string;\n  email: string;\n  address?: string;\n  postalCode?: string;\n  city?: string;\n}\n\ninterface OrderDetails {\n  type?: 'delivery' | 'takeaway';\n  time?: 'asap' | 'scheduled';\n  scheduledTime?: string | null;\n}\n\ninterface Order {\n  id: number;\n  createdAt: string;\n  status: string;\n  items: OrderItem[];\n  customer: OrderCustomer;\n  orderDetails?: OrderDetails;\n  subtotal: number;\n  deliveryFee: number;\n  total: number;\n  paymentMethod: string;\n  notes: string | null;\n}\n\n// Status Badge Component\nconst StatusBadge = ({ status }: { status: string }) => {\n  let color = '';\n  let icon = <AlertTriangle className=\"w-4 h-4\" />;\n  let label = status.replace(/_/g, ' ');\n\n  switch (status) {\n    case STATUS.CONFIRMED:\n      color = 'bg-blue-900/40 border-blue-700/30 text-blue-400';\n      icon = <Bell className=\"w-4 h-4\" />;\n      label = 'Confirmed';\n      break;\n    case STATUS.PROCESSING:\n    case STATUS.PREPARING:\n      color = 'bg-orange-900/40 border-orange-700/30 text-orange-400';\n      icon = <Flame className=\"w-4 h-4\" />;\n      label = 'Preparing';\n      break;\n    case STATUS.READY_FOR_PICKUP:\n      color = 'bg-green-900/40 border-green-700/30 text-green-400';\n      icon = <Package className=\"w-4 h-4\" />;\n      label = 'Ready for Pickup';\n      break;\n    case STATUS.READY_FOR_DELIVERY:\n      color = 'bg-green-900/40 border-green-700/30 text-green-400';\n      icon = <Truck className=\"w-4 h-4\" />;\n      label = 'Ready for Delivery';\n      break;\n    case STATUS.COMPLETED:\n      color = 'bg-teal-900/40 border-teal-700/30 text-teal-400';\n      icon = <CheckCircle className=\"w-4 h-4\" />;\n      label = 'Completed';\n      break;\n  }\n\n  return (\n    <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${color}`}>\n      <span className=\"mr-1.5\">{icon}</span>\n      {label}\n    </div>\n  );\n};\n\n// Action Button Component\nconst ActionButton = ({\n  order,\n  onStatusUpdate,\n  onDispatchToDriver,\n  isLoading,\n  isDispatched = false\n}: {\n  order: Order;\n  onStatusUpdate: (orderId: number, newStatus: string) => void;\n  onDispatchToDriver: (orderId: number) => void;\n  isLoading: boolean;\n  isDispatched?: boolean;\n}) => {\n  // Get next action based on current status and order type using workflow system\n  const getNextAction = () => {\n    const { status, orderDetails } = order;\n    const orderType = orderDetails?.type || ORDER_TYPE.TAKEAWAY;\n    const nextStatus = getNextStatus(status, orderType);\n\n    if (!nextStatus) return null;\n\n    // Special handling for dispatch to driver\n    if (requiresDriverAssignment(status)) {\n      if (isDispatched) {\n        // Order has been dispatched but driver hasn't accepted yet\n        return {\n          nextStatus: \"waiting\",\n          label: 'Awaiting Driver',\n          color: 'bg-yellow-900/50 border-yellow-700 text-yellow-400 cursor-not-allowed',\n          icon: <Clock className=\"w-5 h-5 mr-2\" />,\n          disabled: true\n        };\n      } else {\n        return {\n          nextStatus: \"dispatch\",\n          label: 'Dispatch to Driver',\n          color: 'bg-cyan-900/50 hover:bg-cyan-800 border-cyan-700 text-cyan-400 hover:text-white shadow-[0_0_15px_rgba(6,182,212,0.3)]',\n          icon: <Truck className=\"w-5 h-5 mr-2\" />\n        };\n      }\n    }\n\n    // Get action details based on next status\n    const getActionDetails = (nextStatus: string) => {\n      switch (nextStatus) {\n        case STATUS.PREPARING:\n          return {\n            label: 'Start Preparing',\n            color: 'bg-orange-900/50 hover:bg-orange-800 border-orange-700 text-orange-400 hover:text-white shadow-[0_0_15px_rgba(249,115,22,0.3)]',\n            icon: <Flame className=\"w-5 h-5 mr-2\" />\n          };\n        case STATUS.READY_FOR_PICKUP:\n          return {\n            label: 'Ready for Pickup',\n            color: 'bg-green-900/50 hover:bg-green-800 border-green-700 text-green-400 hover:text-white shadow-[0_0_15px_rgba(34,197,94,0.3)]',\n            icon: <Package className=\"w-5 h-5 mr-2\" />\n          };\n        case STATUS.READY_FOR_DELIVERY:\n          return {\n            label: 'Ready for Delivery',\n            color: 'bg-green-900/50 hover:bg-green-800 border-green-700 text-green-400 hover:text-white shadow-[0_0_15px_rgba(34,197,94,0.3)]',\n            icon: <Truck className=\"w-5 h-5 mr-2\" />\n          };\n        case STATUS.COMPLETED:\n          return {\n            label: 'Mark Completed',\n            color: 'bg-teal-900/50 hover:bg-teal-800 border-teal-700 text-teal-400 hover:text-white shadow-[0_0_15px_rgba(20,184,166,0.3)]',\n            icon: <CheckCircle className=\"w-5 h-5 mr-2\" />\n          };\n        default:\n          return {\n            label: getStatusLabel(nextStatus),\n            color: 'bg-gray-900/50 hover:bg-gray-800 border-gray-700 text-gray-400 hover:text-white',\n            icon: <Clock className=\"w-5 h-5 mr-2\" />\n          };\n      }\n    };\n\n    return {\n      nextStatus,\n      ...getActionDetails(nextStatus)\n    };\n  };\n\n  const action = getNextAction();\n\n  if (!action) return null;\n\n  // Handle action click\n  const handleActionClick = () => {\n    const { nextStatus } = action;\n\n    if (nextStatus === \"dispatch\") {\n      onDispatchToDriver(order.id);\n    } else if (nextStatus !== \"waiting\") {\n      onStatusUpdate(order.id, nextStatus);\n    }\n  };\n\n  const isDisabled = isLoading || action.disabled;\n\n  return (\n    <motion.button\n      className={`flex items-center justify-center px-4 py-2.5 rounded-lg font-medium\n                  border transition-all ${action.color} disabled:opacity-50 disabled:cursor-not-allowed\n                  w-full md:w-auto`}\n      onClick={handleActionClick}\n      disabled={isDisabled}\n      whileHover={!isDisabled ? { scale: 1.05 } : {}}\n      whileTap={!isDisabled ? { scale: 0.95 } : {}}\n    >\n      {isLoading ? (\n        <Clock className=\"w-5 h-5 animate-spin mr-2\" />\n      ) : (\n        action.icon\n      )}\n      {action.label}\n    </motion.button>\n  );\n};\n\n// Order Card Component\nconst OrderCard = ({\n  order,\n  onStatusUpdate,\n  onDispatchToDriver,\n  isUpdating\n}: {\n  order: Order;\n  onStatusUpdate: (orderId: number, newStatus: string) => void;\n  onDispatchToDriver: (orderId: number) => void;\n  isUpdating: boolean;\n}) => {\n  return (\n    <motion.div\n      className=\"bg-gray-900/70 rounded-xl border border-gray-800 overflow-hidden backdrop-blur-md shadow-lg\"\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      exit={{ opacity: 0, scale: 0.95 }}\n      transition={{ duration: 0.3 }}\n      layout\n    >\n      {/* Header */}\n      <div className=\"bg-gray-900 p-4 border-b border-gray-800 flex justify-between items-center\">\n        <div className=\"flex items-center\">\n          <span className=\"text-lg font-medium text-white mr-3\">Order #{order.id}</span>\n          <StatusBadge status={order.status} />\n        </div>\n\n        <div className=\"flex items-center space-x-3\">\n          <span\n            className={`px-3 py-1 rounded-full text-xs font-medium\n                      ${order.orderDetails?.type === ORDER_TYPE.DELIVERY\n                        ? 'bg-cyan-900/30 text-cyan-400 border border-cyan-800/30'\n                        : 'bg-amber-900/30 text-amber-400 border border-amber-800/30'\n                      }`}\n          >\n            {order.orderDetails?.type === ORDER_TYPE.DELIVERY ? 'Delivery' : 'Takeaway'}\n          </span>\n\n          <span className=\"text-sm text-gray-400\">\n            {formatTimeElapsed(order.createdAt)}\n          </span>\n        </div>\n      </div>\n\n      {/* Body */}\n      <div className=\"p-4 space-y-4\">\n        {/* Customer Info */}\n        <div className=\"flex justify-between items-start\">\n          <div>\n            <h3 className=\"text-white font-medium mb-1\">\n              {order.customer.firstName} {order.customer.lastName}\n            </h3>\n            <p className=\"text-sm text-gray-400\">{order.customer.phone}</p>\n          </div>\n\n          <div className=\"text-right\">\n            <p className=\"text-sm text-gray-400\">\n              {order.orderDetails?.time === 'asap'\n                ? 'ASAP'\n                : (order.orderDetails?.scheduledTime\n                    ? `For: ${formatDate(order.orderDetails.scheduledTime)}`\n                    : ''\n                  )\n              }\n            </p>\n            <p className=\"text-sm font-medium text-white\">{formatCurrency(order.total)}</p>\n          </div>\n        </div>\n\n        {/* Order Items */}\n        <div className=\"bg-gray-800/50 rounded-lg p-3\">\n          <h3 className=\"text-white text-sm font-medium mb-2\">Order Items:</h3>\n          <ul className=\"space-y-1.5\">\n            {order.items.map(item => (\n              <li key={item.id} className=\"text-sm flex justify-between\">\n                <div>\n                  <span className=\"text-orange-400 font-medium\">{item.quantity}x</span>\n                  <span className=\"text-white ml-2\">{item.name}</span>\n                </div>\n                <span className=\"text-gray-400\">{formatCurrency(item.price * item.quantity)}</span>\n              </li>\n            ))}\n          </ul>\n        </div>\n\n        {/* Notes */}\n        {order.notes && (\n          <div className=\"bg-gray-800/50 rounded-lg p-3\">\n            <h3 className=\"text-white text-sm font-medium mb-1\">Special Instructions:</h3>\n            <p className=\"text-sm text-gray-300\">{order.notes}</p>\n          </div>\n        )}\n\n        {/* Actions */}\n        <div className=\"flex justify-end\">\n          <ActionButton\n            order={order}\n            onStatusUpdate={onStatusUpdate}\n            onDispatchToDriver={onDispatchToDriver}\n            isLoading={isUpdating}\n          />\n        </div>\n      </div>\n    </motion.div>\n  );\n};\n\n// Main Manager Page Component\nconst ManagerPage = () => {\n  const [orders, setOrders] = useState<Order[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [isUpdating, setIsUpdating] = useState(false);\n  const [statusFilter, setStatusFilter] = useState('active');\n  const [dispatchedOrders, setDispatchedOrders] = useState<Set<number>>(new Set());\n  const previousOrdersRef = useRef<Order[]>([]);\n\n  const { addNotification } = useNotifications();\n  const { playNotificationSound, preloadSounds } = useAudioNotification();\n\n  // Preload notification sounds on component mount\n  useEffect(() => {\n    preloadSounds();\n  }, [preloadSounds]);\n\n  // Detect new orders and play notification sound\n  useEffect(() => {\n    if (previousOrdersRef.current.length === 0) {\n      // First load, just store the orders\n      previousOrdersRef.current = orders;\n      return;\n    }\n\n    // Check for new orders\n    const newOrders = orders.filter(order =>\n      !previousOrdersRef.current.some(prevOrder => prevOrder.id === order.id)\n    );\n\n    // Check for status updates\n    const updatedOrders = orders.filter(order => {\n      const prevOrder = previousOrdersRef.current.find(prev => prev.id === order.id);\n      return prevOrder && prevOrder.status !== order.status;\n    });\n\n    // Handle new orders\n    newOrders.forEach(order => {\n      playNotificationSound('new_order');\n      addNotification({\n        type: 'new_order',\n        title: 'New Order Received',\n        message: `Order #${order.id} from ${order.customerDetails?.name || 'Customer'}`,\n        orderId: order.id,\n        customerName: order.customerDetails?.name,\n        priority: 'high'\n      });\n    });\n\n    // Handle status updates\n    updatedOrders.forEach(order => {\n      const prevOrder = previousOrdersRef.current.find(prev => prev.id === order.id);\n      if (prevOrder) {\n        playNotificationSound('status_update');\n        addNotification({\n          type: 'status_update',\n          title: 'Order Status Updated',\n          message: `Order #${order.id} is now ${order.status.replace('_', ' ')}`,\n          orderId: order.id,\n          customerName: order.customerDetails?.name,\n          priority: 'medium'\n        });\n      }\n    });\n\n    // Update the reference\n    previousOrdersRef.current = orders;\n  }, [orders, playNotificationSound, addNotification]);\n\n  // Fetch orders\n  useEffect(() => {\n    const fetchOrders = async () => {\n      setLoading(true);\n      try {\n        const response = await fetch('/api/admin/orders');\n        if (!response.ok) {\n          throw new Error(`Failed to fetch orders: ${response.status}`);\n        }\n\n        const allOrders = await response.json();\n\n        // Filter orders based on selected status\n        let filteredOrders = allOrders;\n\n        if (statusFilter === 'active') {\n          // Active orders are those managed by the manager (not yet dispatched or completed)\n          filteredOrders = allOrders.filter((order: Order) =>\n            isManagerOrder(order.status)\n          );\n        } else if (statusFilter !== 'all') {\n          filteredOrders = allOrders.filter((order: Order) =>\n            order.status === statusFilter\n          );\n        }\n\n        setOrders(filteredOrders);\n        setError(null);\n      } catch (err) {\n        console.error('Error fetching orders:', err);\n        setError('Failed to load orders. Please try again.');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchOrders();\n\n    // Periodically refresh orders (every 30 seconds)\n    const interval = setInterval(fetchOrders, 30000);\n    return () => clearInterval(interval);\n  }, [statusFilter]);\n\n  // Update order status\n  const handleStatusUpdate = async (orderId: number, newStatus: string) => {\n    setIsUpdating(true);\n    try {\n      const response = await fetch(`/api/admin/orders/${orderId}/status`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({ newStatus })\n      });\n\n      if (!response.ok) {\n        throw new Error(`Failed to update order status: ${response.status}`);\n      }\n\n      // Update state\n      setOrders(prevOrders =>\n        prevOrders.map(order =>\n          order.id === orderId\n            ? { ...order, status: newStatus }\n            : order\n        )\n      );\n\n    } catch (err) {\n      console.error('Error updating order status:', err);\n      setError('Failed to update order status. Please try again.');\n    } finally {\n      setIsUpdating(false);\n    }\n  };\n\n  // Dispatch order to driver\n  const handleDispatchToDriver = async (orderId: number) => {\n    setIsUpdating(true);\n    try {\n      const order = orders.find(o => o.id === orderId);\n      const response = await fetch('/api/admin/dispatch/to-driver', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({ orderId })\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.error || `Failed to dispatch order: ${response.status}`);\n      }\n\n      const result = await response.json();\n      console.log('Order dispatched successfully:', result);\n\n      // Mark order as dispatched\n      setDispatchedOrders(prev => new Set(prev).add(orderId));\n\n      // Keep the order in manager view since driver hasn't accepted it yet\n      // The order will be removed when driver changes status to 'with_driver'\n\n      // Add notification for successful dispatch\n      addNotification({\n        type: 'status_update',\n        title: 'Order Dispatched',\n        message: `Order #${orderId} has been dispatched to a driver and is awaiting acceptance`,\n        orderId: orderId,\n        customerName: order?.customerDetails?.name,\n        priority: 'medium'\n      });\n\n    } catch (err) {\n      console.error('Error dispatching order to driver:', err);\n      const order = orders.find(o => o.id === orderId);\n      addNotification({\n        type: 'system',\n        title: 'Dispatch Failed',\n        message: `Failed to dispatch order #${orderId}: ${err.message}`,\n        orderId: orderId,\n        priority: 'high'\n      });\n      setError('Failed to dispatch order to driver. Please try again.');\n    } finally {\n      setIsUpdating(false);\n    }\n  };\n\n  // Group orders by status for Kanban-style display\n  const groupOrdersByStatus = () => {\n    const groups = {\n      [STATUS.CONFIRMED]: [] as Order[],\n      [STATUS.PREPARING]: [] as Order[],\n      [STATUS.READY_FOR_PICKUP]: [] as Order[],\n      [STATUS.READY_FOR_DELIVERY]: [] as Order[]\n    };\n\n    orders.forEach(order => {\n      if (groups[order.status as keyof typeof groups]) {\n        groups[order.status as keyof typeof groups].push(order);\n      }\n    });\n\n    return groups;\n  };\n\n  const ordersGrouped = groupOrdersByStatus();\n\n  // Count orders by type\n  const countByType = (type: string) => {\n    return orders.filter(order => order.orderDetails?.type === type).length;\n  };\n\n  // Count orders by status\n  const countByStatus = (status: string) => {\n    return orders.filter(order => order.status === status).length;\n  };\n\n  return (\n    <ManagerLayout>\n      <div className=\"space-y-6\">\n        <div className=\"flex flex-col md:flex-row md:justify-between md:items-center gap-4\">\n          <div>\n            <h1 className=\"text-2xl font-bold bg-gradient-to-r from-orange-400 via-yellow-400 to-orange-400 text-transparent bg-clip-text mb-2\">\n              Kitchen Order Management\n            </h1>\n            <p className=\"text-gray-400\">Manage all incoming orders and update their status</p>\n          </div>\n\n          <div className=\"flex flex-wrap gap-2\">\n            <div className=\"bg-orange-900/30 border border-orange-700/30 rounded-lg p-3 text-center min-w-[100px]\">\n              <div className=\"text-orange-400 font-medium\">Preparing</div>\n              <div className=\"text-2xl font-bold text-white\">{countByStatus(STATUS.PREPARING)}</div>\n            </div>\n\n            <div className=\"bg-amber-900/30 border border-amber-700/30 rounded-lg p-3 text-center min-w-[100px]\">\n              <div className=\"text-amber-400 font-medium\">Takeaway</div>\n              <div className=\"text-2xl font-bold text-white\">{countByType(ORDER_TYPE.TAKEAWAY)}</div>\n            </div>\n\n            <div className=\"bg-cyan-900/30 border border-cyan-700/30 rounded-lg p-3 text-center min-w-[100px]\">\n              <div className=\"text-cyan-400 font-medium\">Delivery</div>\n              <div className=\"text-2xl font-bold text-white\">{countByType(ORDER_TYPE.DELIVERY)}</div>\n            </div>\n          </div>\n        </div>\n\n        {/* Filter Controls */}\n        <div className=\"flex justify-between items-center\">\n          <div className=\"flex space-x-2\">\n            <select\n              value={statusFilter}\n              onChange={(e) => setStatusFilter(e.target.value)}\n              className=\"bg-gray-900 border border-gray-700 rounded-lg px-3 py-2 text-sm text-white focus:ring-orange-500 focus:border-orange-500\"\n            >\n              <option value=\"active\">Active Orders</option>\n              <option value={STATUS.CONFIRMED}>Confirmed Only</option>\n              <option value={STATUS.PREPARING}>Preparing Only</option>\n              <option value={STATUS.READY_FOR_PICKUP}>Ready for Pickup Only</option>\n              <option value={STATUS.READY_FOR_DELIVERY}>Ready for Delivery Only</option>\n              <option value=\"all\">All Orders</option>\n            </select>\n          </div>\n\n          <div className=\"text-sm text-gray-400\">\n            Total: {orders.length} order{orders.length !== 1 ? 's' : ''}\n          </div>\n        </div>\n\n        {error && (\n          <div className=\"bg-red-900/40 border border-red-700 text-red-400 p-4 rounded-lg\">\n            {error}\n          </div>\n        )}\n\n        {loading ? (\n          <div className=\"flex justify-center items-center py-12\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500\"></div>\n          </div>\n        ) : orders.length === 0 ? (\n          <div className=\"bg-gray-900/50 border border-gray-800 rounded-xl p-8 text-center\">\n            <ChefHat className=\"w-16 h-16 text-gray-700 mx-auto mb-4\" />\n            <h2 className=\"text-xl font-medium text-white mb-2\">No Orders Found</h2>\n            <p className=\"text-gray-400\">\n              There are currently no orders matching your filter.\n              New orders will appear here automatically.\n            </p>\n          </div>\n        ) : (\n          <div className=\"grid grid-cols-1 xl:grid-cols-4 gap-6\">\n            {/* Column 1: Confirmed Orders */}\n            <div className=\"bg-gray-900/50 rounded-xl border border-blue-900/30 p-4\">\n              <div className=\"flex items-center mb-4 border-b border-gray-800 pb-3\">\n                <Bell className=\"w-5 h-5 text-blue-400 mr-2\" />\n                <h3 className=\"text-lg font-medium text-blue-400\">New Orders</h3>\n                <span className=\"ml-auto bg-blue-900/50 text-blue-400 text-sm px-2 py-0.5 rounded-full\">\n                  {ordersGrouped[STATUS.CONFIRMED]?.length || 0}\n                </span>\n              </div>\n\n              <div className=\"space-y-4\">\n                <AnimatePresence>\n                  {ordersGrouped[STATUS.CONFIRMED]?.map(order => (\n                    <OrderCard\n                      key={order.id}\n                      order={order}\n                      onStatusUpdate={handleStatusUpdate}\n                      onDispatchToDriver={handleDispatchToDriver}\n                      isUpdating={isUpdating}\n                    />\n                  ))}\n                </AnimatePresence>\n\n                {ordersGrouped[STATUS.CONFIRMED]?.length === 0 && (\n                  <div className=\"text-center py-8 text-gray-500\">\n                    <Bell className=\"w-8 h-8 text-gray-700 mx-auto mb-2\" />\n                    <p>No new orders</p>\n                  </div>\n                )}\n              </div>\n            </div>\n\n            {/* Column 2: Preparing Orders */}\n            <div className=\"bg-gray-900/50 rounded-xl border border-orange-900/30 p-4\">\n              <div className=\"flex items-center mb-4 border-b border-gray-800 pb-3\">\n                <Flame className=\"w-5 h-5 text-orange-400 mr-2\" />\n                <h3 className=\"text-lg font-medium text-orange-400\">Preparing</h3>\n                <span className=\"ml-auto bg-orange-900/50 text-orange-400 text-sm px-2 py-0.5 rounded-full\">\n                  {ordersGrouped[STATUS.PREPARING]?.length || 0}\n                </span>\n              </div>\n\n              <div className=\"space-y-4\">\n                <AnimatePresence>\n                  {ordersGrouped[STATUS.PREPARING]?.map(order => (\n                    <OrderCard\n                      key={order.id}\n                      order={order}\n                      onStatusUpdate={handleStatusUpdate}\n                      onDispatchToDriver={handleDispatchToDriver}\n                      isUpdating={isUpdating}\n                    />\n                  ))}\n                </AnimatePresence>\n\n                {ordersGrouped[STATUS.PREPARING]?.length === 0 && (\n                  <div className=\"text-center py-8 text-gray-500\">\n                    <Flame className=\"w-8 h-8 text-gray-700 mx-auto mb-2\" />\n                    <p>No orders in preparation</p>\n                  </div>\n                )}\n              </div>\n            </div>\n\n            {/* Column 3: Ready Orders (Pickup or Delivery) */}\n            <div className=\"bg-gray-900/50 rounded-xl border border-green-900/30 p-4\">\n              <div className=\"flex items-center mb-4 border-b border-gray-800 pb-3\">\n                <Package className=\"w-5 h-5 text-green-400 mr-2\" />\n                <h3 className=\"text-lg font-medium text-green-400\">Ready</h3>\n                <span className=\"ml-auto bg-green-900/50 text-green-400 text-sm px-2 py-0.5 rounded-full\">\n                  {(ordersGrouped[STATUS.READY_FOR_PICKUP]?.length || 0) +\n                   (ordersGrouped[STATUS.READY_FOR_DELIVERY]?.length || 0)}\n                </span>\n              </div>\n\n              <div className=\"space-y-4\">\n                <AnimatePresence>\n                  {/* Combine both types of ready orders */}\n                  {[\n                    ...(ordersGrouped[STATUS.READY_FOR_PICKUP] || []),\n                    ...(ordersGrouped[STATUS.READY_FOR_DELIVERY] || [])\n                  ].map(order => (\n                    <OrderCard\n                      key={order.id}\n                      order={order}\n                      onStatusUpdate={handleStatusUpdate}\n                      onDispatchToDriver={handleDispatchToDriver}\n                      isUpdating={isUpdating}\n                    />\n                  ))}\n                </AnimatePresence>\n\n                {(ordersGrouped[STATUS.READY_FOR_PICKUP]?.length || 0) +\n                 (ordersGrouped[STATUS.READY_FOR_DELIVERY]?.length || 0) === 0 && (\n                  <div className=\"text-center py-8 text-gray-500\">\n                    <Package className=\"w-8 h-8 text-gray-700 mx-auto mb-2\" />\n                    <p>No orders ready yet</p>\n                  </div>\n                )}\n              </div>\n            </div>\n\n            {/* Column 4: Completed/Archived Orders */}\n            <div className=\"bg-gray-900/50 rounded-xl border border-teal-900/30 p-4\">\n              <div className=\"flex items-center mb-4 border-b border-gray-800 pb-3\">\n                <CheckCircle className=\"w-5 h-5 text-teal-400 mr-2\" />\n                <h3 className=\"text-lg font-medium text-teal-400\">Completed</h3>\n                <span className=\"ml-auto bg-teal-900/50 text-teal-400 text-sm px-2 py-0.5 rounded-full\">\n                  {ordersGrouped[STATUS.COMPLETED]?.length || 0}\n                </span>\n              </div>\n\n              <div className=\"space-y-4\">\n                <AnimatePresence>\n                  {ordersGrouped[STATUS.COMPLETED]?.map(order => (\n                    <OrderCard\n                      key={order.id}\n                      order={order}\n                      onStatusUpdate={handleStatusUpdate}\n                      onDispatchToDriver={handleDispatchToDriver}\n                      isUpdating={isUpdating}\n                    />\n                  ))}\n                </AnimatePresence>\n\n                {ordersGrouped[STATUS.COMPLETED]?.length === 0 && (\n                  <div className=\"text-center py-8 text-gray-500\">\n                    <CheckCircle className=\"w-8 h-8 text-gray-700 mx-auto mb-2\" />\n                    <p>No completed orders</p>\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </ManagerLayout>\n  );\n};\n\nexport default ManagerPage;", "modifiedCode": "import { useState, useEffect, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  ChefHat,\n  Truck,\n  CheckCircle,\n  Clock,\n  Flame,\n  ShoppingBag,\n  Package,\n  Bell,\n  AlertTriangle\n} from 'lucide-react';\nimport ManagerLayout from './ManagerLayout';\nimport { format, parseISO } from 'date-fns';\nimport { useNotifications } from '@/context/NotificationContext';\nimport { useAudioNotification } from '@/hooks/useAudioNotification';\nimport {\n  ORDER_STATUSES,\n  ORDER_TYPES,\n  getNextStatus,\n  getStatusLabel,\n  getStatusColor,\n  isManagerOrder,\n  requiresDriverAssignment\n} from '@/utils/orderStatusWorkflow';\n\n// Use centralized status and type constants\nconst STATUS = ORDER_STATUSES;\nconst ORDER_TYPE = ORDER_TYPES;\n\n// Format currency\nconst formatCurrency = (amount: number) => {\n  return `$${(amount / 100).toFixed(2)}`;\n};\n\n// Format date for display\nconst formatDate = (dateString: string) => {\n  try {\n    const date = parseISO(dateString);\n    return format(date, 'MMM d, h:mm a');\n  } catch (e) {\n    return dateString;\n  }\n};\n\n// Format time elapsed since order was placed\nconst formatTimeElapsed = (dateString: string) => {\n  try {\n    const orderDate = parseISO(dateString);\n    const now = new Date();\n    const diffInMinutes = Math.floor((now.getTime() - orderDate.getTime()) / (1000 * 60));\n\n    if (diffInMinutes < 1) return 'Just now';\n    if (diffInMinutes === 1) return '1 minute ago';\n    if (diffInMinutes < 60) return `${diffInMinutes} minutes ago`;\n\n    const hours = Math.floor(diffInMinutes / 60);\n    if (hours === 1) return '1 hour ago';\n    return `${hours} hours ago`;\n  } catch (e) {\n    return 'Unknown time';\n  }\n};\n\ninterface OrderItem {\n  id: number;\n  name: string;\n  price: number;\n  quantity: number;\n}\n\ninterface OrderCustomer {\n  firstName: string;\n  lastName: string;\n  phone: string;\n  email: string;\n  address?: string;\n  postalCode?: string;\n  city?: string;\n}\n\ninterface OrderDetails {\n  type?: 'delivery' | 'takeaway';\n  time?: 'asap' | 'scheduled';\n  scheduledTime?: string | null;\n}\n\ninterface Order {\n  id: number;\n  createdAt: string;\n  status: string;\n  items: OrderItem[];\n  customer: OrderCustomer;\n  orderDetails?: OrderDetails;\n  subtotal: number;\n  deliveryFee: number;\n  total: number;\n  paymentMethod: string;\n  notes: string | null;\n}\n\n// Status Badge Component\nconst StatusBadge = ({ status }: { status: string }) => {\n  let color = '';\n  let icon = <AlertTriangle className=\"w-4 h-4\" />;\n  let label = status.replace(/_/g, ' ');\n\n  switch (status) {\n    case STATUS.CONFIRMED:\n      color = 'bg-blue-900/40 border-blue-700/30 text-blue-400';\n      icon = <Bell className=\"w-4 h-4\" />;\n      label = 'Confirmed';\n      break;\n    case STATUS.PROCESSING:\n    case STATUS.PREPARING:\n      color = 'bg-orange-900/40 border-orange-700/30 text-orange-400';\n      icon = <Flame className=\"w-4 h-4\" />;\n      label = 'Preparing';\n      break;\n    case STATUS.READY_FOR_PICKUP:\n      color = 'bg-green-900/40 border-green-700/30 text-green-400';\n      icon = <Package className=\"w-4 h-4\" />;\n      label = 'Ready for Pickup';\n      break;\n    case STATUS.READY_FOR_DELIVERY:\n      color = 'bg-green-900/40 border-green-700/30 text-green-400';\n      icon = <Truck className=\"w-4 h-4\" />;\n      label = 'Ready for Delivery';\n      break;\n    case STATUS.COMPLETED:\n      color = 'bg-teal-900/40 border-teal-700/30 text-teal-400';\n      icon = <CheckCircle className=\"w-4 h-4\" />;\n      label = 'Completed';\n      break;\n  }\n\n  return (\n    <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${color}`}>\n      <span className=\"mr-1.5\">{icon}</span>\n      {label}\n    </div>\n  );\n};\n\n// Action Button Component\nconst ActionButton = ({\n  order,\n  onStatusUpdate,\n  onDispatchToDriver,\n  isLoading,\n  isDispatched = false\n}: {\n  order: Order;\n  onStatusUpdate: (orderId: number, newStatus: string) => void;\n  onDispatchToDriver: (orderId: number) => void;\n  isLoading: boolean;\n  isDispatched?: boolean;\n}) => {\n  // Get next action based on current status and order type using workflow system\n  const getNextAction = () => {\n    const { status, orderDetails } = order;\n    const orderType = orderDetails?.type || ORDER_TYPE.TAKEAWAY;\n    const nextStatus = getNextStatus(status, orderType);\n\n    if (!nextStatus) return null;\n\n    // Special handling for dispatch to driver\n    if (requiresDriverAssignment(status)) {\n      if (isDispatched) {\n        // Order has been dispatched but driver hasn't accepted yet\n        return {\n          nextStatus: \"waiting\",\n          label: 'Awaiting Driver',\n          color: 'bg-yellow-900/50 border-yellow-700 text-yellow-400 cursor-not-allowed',\n          icon: <Clock className=\"w-5 h-5 mr-2\" />,\n          disabled: true\n        };\n      } else {\n        return {\n          nextStatus: \"dispatch\",\n          label: 'Dispatch to Driver',\n          color: 'bg-cyan-900/50 hover:bg-cyan-800 border-cyan-700 text-cyan-400 hover:text-white shadow-[0_0_15px_rgba(6,182,212,0.3)]',\n          icon: <Truck className=\"w-5 h-5 mr-2\" />\n        };\n      }\n    }\n\n    // Get action details based on next status\n    const getActionDetails = (nextStatus: string) => {\n      switch (nextStatus) {\n        case STATUS.PREPARING:\n          return {\n            label: 'Start Preparing',\n            color: 'bg-orange-900/50 hover:bg-orange-800 border-orange-700 text-orange-400 hover:text-white shadow-[0_0_15px_rgba(249,115,22,0.3)]',\n            icon: <Flame className=\"w-5 h-5 mr-2\" />\n          };\n        case STATUS.READY_FOR_PICKUP:\n          return {\n            label: 'Ready for Pickup',\n            color: 'bg-green-900/50 hover:bg-green-800 border-green-700 text-green-400 hover:text-white shadow-[0_0_15px_rgba(34,197,94,0.3)]',\n            icon: <Package className=\"w-5 h-5 mr-2\" />\n          };\n        case STATUS.READY_FOR_DELIVERY:\n          return {\n            label: 'Ready for Delivery',\n            color: 'bg-green-900/50 hover:bg-green-800 border-green-700 text-green-400 hover:text-white shadow-[0_0_15px_rgba(34,197,94,0.3)]',\n            icon: <Truck className=\"w-5 h-5 mr-2\" />\n          };\n        case STATUS.COMPLETED:\n          return {\n            label: 'Mark Completed',\n            color: 'bg-teal-900/50 hover:bg-teal-800 border-teal-700 text-teal-400 hover:text-white shadow-[0_0_15px_rgba(20,184,166,0.3)]',\n            icon: <CheckCircle className=\"w-5 h-5 mr-2\" />\n          };\n        default:\n          return {\n            label: getStatusLabel(nextStatus),\n            color: 'bg-gray-900/50 hover:bg-gray-800 border-gray-700 text-gray-400 hover:text-white',\n            icon: <Clock className=\"w-5 h-5 mr-2\" />\n          };\n      }\n    };\n\n    return {\n      nextStatus,\n      ...getActionDetails(nextStatus)\n    };\n  };\n\n  const action = getNextAction();\n\n  if (!action) return null;\n\n  // Handle action click\n  const handleActionClick = () => {\n    const { nextStatus } = action;\n\n    if (nextStatus === \"dispatch\") {\n      onDispatchToDriver(order.id);\n    } else if (nextStatus !== \"waiting\") {\n      onStatusUpdate(order.id, nextStatus);\n    }\n  };\n\n  const isDisabled = isLoading || action.disabled;\n\n  return (\n    <motion.button\n      className={`flex items-center justify-center px-4 py-2.5 rounded-lg font-medium\n                  border transition-all ${action.color} disabled:opacity-50 disabled:cursor-not-allowed\n                  w-full md:w-auto`}\n      onClick={handleActionClick}\n      disabled={isDisabled}\n      whileHover={!isDisabled ? { scale: 1.05 } : {}}\n      whileTap={!isDisabled ? { scale: 0.95 } : {}}\n    >\n      {isLoading ? (\n        <Clock className=\"w-5 h-5 animate-spin mr-2\" />\n      ) : (\n        action.icon\n      )}\n      {action.label}\n    </motion.button>\n  );\n};\n\n// Order Card Component\nconst OrderCard = ({\n  order,\n  onStatusUpdate,\n  onDispatchToDriver,\n  isUpdating\n}: {\n  order: Order;\n  onStatusUpdate: (orderId: number, newStatus: string) => void;\n  onDispatchToDriver: (orderId: number) => void;\n  isUpdating: boolean;\n}) => {\n  return (\n    <motion.div\n      className=\"bg-gray-900/70 rounded-xl border border-gray-800 overflow-hidden backdrop-blur-md shadow-lg\"\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      exit={{ opacity: 0, scale: 0.95 }}\n      transition={{ duration: 0.3 }}\n      layout\n    >\n      {/* Header */}\n      <div className=\"bg-gray-900 p-4 border-b border-gray-800 flex justify-between items-center\">\n        <div className=\"flex items-center\">\n          <span className=\"text-lg font-medium text-white mr-3\">Order #{order.id}</span>\n          <StatusBadge status={order.status} />\n        </div>\n\n        <div className=\"flex items-center space-x-3\">\n          <span\n            className={`px-3 py-1 rounded-full text-xs font-medium\n                      ${order.orderDetails?.type === ORDER_TYPE.DELIVERY\n                        ? 'bg-cyan-900/30 text-cyan-400 border border-cyan-800/30'\n                        : 'bg-amber-900/30 text-amber-400 border border-amber-800/30'\n                      }`}\n          >\n            {order.orderDetails?.type === ORDER_TYPE.DELIVERY ? 'Delivery' : 'Takeaway'}\n          </span>\n\n          <span className=\"text-sm text-gray-400\">\n            {formatTimeElapsed(order.createdAt)}\n          </span>\n        </div>\n      </div>\n\n      {/* Body */}\n      <div className=\"p-4 space-y-4\">\n        {/* Customer Info */}\n        <div className=\"flex justify-between items-start\">\n          <div>\n            <h3 className=\"text-white font-medium mb-1\">\n              {order.customer.firstName} {order.customer.lastName}\n            </h3>\n            <p className=\"text-sm text-gray-400\">{order.customer.phone}</p>\n          </div>\n\n          <div className=\"text-right\">\n            <p className=\"text-sm text-gray-400\">\n              {order.orderDetails?.time === 'asap'\n                ? 'ASAP'\n                : (order.orderDetails?.scheduledTime\n                    ? `For: ${formatDate(order.orderDetails.scheduledTime)}`\n                    : ''\n                  )\n              }\n            </p>\n            <p className=\"text-sm font-medium text-white\">{formatCurrency(order.total)}</p>\n          </div>\n        </div>\n\n        {/* Order Items */}\n        <div className=\"bg-gray-800/50 rounded-lg p-3\">\n          <h3 className=\"text-white text-sm font-medium mb-2\">Order Items:</h3>\n          <ul className=\"space-y-1.5\">\n            {order.items.map(item => (\n              <li key={item.id} className=\"text-sm flex justify-between\">\n                <div>\n                  <span className=\"text-orange-400 font-medium\">{item.quantity}x</span>\n                  <span className=\"text-white ml-2\">{item.name}</span>\n                </div>\n                <span className=\"text-gray-400\">{formatCurrency(item.price * item.quantity)}</span>\n              </li>\n            ))}\n          </ul>\n        </div>\n\n        {/* Notes */}\n        {order.notes && (\n          <div className=\"bg-gray-800/50 rounded-lg p-3\">\n            <h3 className=\"text-white text-sm font-medium mb-1\">Special Instructions:</h3>\n            <p className=\"text-sm text-gray-300\">{order.notes}</p>\n          </div>\n        )}\n\n        {/* Actions */}\n        <div className=\"flex justify-end\">\n          <ActionButton\n            order={order}\n            onStatusUpdate={onStatusUpdate}\n            onDispatchToDriver={onDispatchToDriver}\n            isLoading={isUpdating}\n          />\n        </div>\n      </div>\n    </motion.div>\n  );\n};\n\n// Main Manager Page Component\nconst ManagerPage = () => {\n  const [orders, setOrders] = useState<Order[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [isUpdating, setIsUpdating] = useState(false);\n  const [statusFilter, setStatusFilter] = useState('active');\n  const [dispatchedOrders, setDispatchedOrders] = useState<Set<number>>(new Set());\n  const previousOrdersRef = useRef<Order[]>([]);\n\n  const { addNotification } = useNotifications();\n  const { playNotificationSound, preloadSounds } = useAudioNotification();\n\n  // Preload notification sounds on component mount\n  useEffect(() => {\n    preloadSounds();\n  }, [preloadSounds]);\n\n  // Detect new orders and play notification sound\n  useEffect(() => {\n    if (previousOrdersRef.current.length === 0) {\n      // First load, just store the orders\n      previousOrdersRef.current = orders;\n      return;\n    }\n\n    // Check for new orders\n    const newOrders = orders.filter(order =>\n      !previousOrdersRef.current.some(prevOrder => prevOrder.id === order.id)\n    );\n\n    // Check for status updates\n    const updatedOrders = orders.filter(order => {\n      const prevOrder = previousOrdersRef.current.find(prev => prev.id === order.id);\n      return prevOrder && prevOrder.status !== order.status;\n    });\n\n    // Handle new orders\n    newOrders.forEach(order => {\n      playNotificationSound('new_order');\n      addNotification({\n        type: 'new_order',\n        title: 'New Order Received',\n        message: `Order #${order.id} from ${order.customerDetails?.name || 'Customer'}`,\n        orderId: order.id,\n        customerName: order.customerDetails?.name,\n        priority: 'high'\n      });\n    });\n\n    // Handle status updates\n    updatedOrders.forEach(order => {\n      const prevOrder = previousOrdersRef.current.find(prev => prev.id === order.id);\n      if (prevOrder) {\n        playNotificationSound('status_update');\n        addNotification({\n          type: 'status_update',\n          title: 'Order Status Updated',\n          message: `Order #${order.id} is now ${order.status.replace('_', ' ')}`,\n          orderId: order.id,\n          customerName: order.customerDetails?.name,\n          priority: 'medium'\n        });\n      }\n    });\n\n    // Update the reference\n    previousOrdersRef.current = orders;\n  }, [orders, playNotificationSound, addNotification]);\n\n  // Fetch orders\n  useEffect(() => {\n    const fetchOrders = async () => {\n      setLoading(true);\n      try {\n        const response = await fetch('/api/admin/orders');\n        if (!response.ok) {\n          throw new Error(`Failed to fetch orders: ${response.status}`);\n        }\n\n        const allOrders = await response.json();\n\n        // Filter orders based on selected status\n        let filteredOrders = allOrders;\n\n        if (statusFilter === 'active') {\n          // Active orders are those managed by the manager (not yet dispatched or completed)\n          filteredOrders = allOrders.filter((order: Order) =>\n            isManagerOrder(order.status)\n          );\n        } else if (statusFilter !== 'all') {\n          filteredOrders = allOrders.filter((order: Order) =>\n            order.status === statusFilter\n          );\n        }\n\n        setOrders(filteredOrders);\n        setError(null);\n      } catch (err) {\n        console.error('Error fetching orders:', err);\n        setError('Failed to load orders. Please try again.');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchOrders();\n\n    // Periodically refresh orders (every 30 seconds)\n    const interval = setInterval(fetchOrders, 30000);\n    return () => clearInterval(interval);\n  }, [statusFilter]);\n\n  // Update order status\n  const handleStatusUpdate = async (orderId: number, newStatus: string) => {\n    setIsUpdating(true);\n    try {\n      const response = await fetch(`/api/admin/orders/${orderId}/status`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({ newStatus })\n      });\n\n      if (!response.ok) {\n        throw new Error(`Failed to update order status: ${response.status}`);\n      }\n\n      // Update state\n      setOrders(prevOrders =>\n        prevOrders.map(order =>\n          order.id === orderId\n            ? { ...order, status: newStatus }\n            : order\n        )\n      );\n\n    } catch (err) {\n      console.error('Error updating order status:', err);\n      setError('Failed to update order status. Please try again.');\n    } finally {\n      setIsUpdating(false);\n    }\n  };\n\n  // Dispatch order to driver\n  const handleDispatchToDriver = async (orderId: number) => {\n    setIsUpdating(true);\n    try {\n      const order = orders.find(o => o.id === orderId);\n      const response = await fetch('/api/admin/dispatch/to-driver', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({ orderId })\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.error || `Failed to dispatch order: ${response.status}`);\n      }\n\n      const result = await response.json();\n      console.log('Order dispatched successfully:', result);\n\n      // Mark order as dispatched\n      setDispatchedOrders(prev => new Set(prev).add(orderId));\n\n      // Keep the order in manager view since driver hasn't accepted it yet\n      // The order will be removed when driver changes status to 'with_driver'\n\n      // Add notification for successful dispatch\n      addNotification({\n        type: 'status_update',\n        title: 'Order Dispatched',\n        message: `Order #${orderId} has been dispatched to a driver and is awaiting acceptance`,\n        orderId: orderId,\n        customerName: order?.customerDetails?.name,\n        priority: 'medium'\n      });\n\n    } catch (err) {\n      console.error('Error dispatching order to driver:', err);\n      const order = orders.find(o => o.id === orderId);\n      addNotification({\n        type: 'system',\n        title: 'Dispatch Failed',\n        message: `Failed to dispatch order #${orderId}: ${err.message}`,\n        orderId: orderId,\n        priority: 'high'\n      });\n      setError('Failed to dispatch order to driver. Please try again.');\n    } finally {\n      setIsUpdating(false);\n    }\n  };\n\n  // Group orders by status for Kanban-style display\n  const groupOrdersByStatus = () => {\n    const groups = {\n      [STATUS.CONFIRMED]: [] as Order[],\n      [STATUS.PREPARING]: [] as Order[],\n      [STATUS.READY_FOR_PICKUP]: [] as Order[],\n      [STATUS.READY_FOR_DELIVERY]: [] as Order[]\n    };\n\n    orders.forEach(order => {\n      if (groups[order.status as keyof typeof groups]) {\n        groups[order.status as keyof typeof groups].push(order);\n      }\n    });\n\n    return groups;\n  };\n\n  const ordersGrouped = groupOrdersByStatus();\n\n  // Count orders by type\n  const countByType = (type: string) => {\n    return orders.filter(order => order.orderDetails?.type === type).length;\n  };\n\n  // Count orders by status\n  const countByStatus = (status: string) => {\n    return orders.filter(order => order.status === status).length;\n  };\n\n  return (\n    <ManagerLayout>\n      <div className=\"space-y-6\">\n        <div className=\"flex flex-col md:flex-row md:justify-between md:items-center gap-4\">\n          <div>\n            <h1 className=\"text-2xl font-bold bg-gradient-to-r from-orange-400 via-yellow-400 to-orange-400 text-transparent bg-clip-text mb-2\">\n              Kitchen Order Management\n            </h1>\n            <p className=\"text-gray-400\">Manage all incoming orders and update their status</p>\n          </div>\n\n          <div className=\"flex flex-wrap gap-2\">\n            <div className=\"bg-orange-900/30 border border-orange-700/30 rounded-lg p-3 text-center min-w-[100px]\">\n              <div className=\"text-orange-400 font-medium\">Preparing</div>\n              <div className=\"text-2xl font-bold text-white\">{countByStatus(STATUS.PREPARING)}</div>\n            </div>\n\n            <div className=\"bg-amber-900/30 border border-amber-700/30 rounded-lg p-3 text-center min-w-[100px]\">\n              <div className=\"text-amber-400 font-medium\">Takeaway</div>\n              <div className=\"text-2xl font-bold text-white\">{countByType(ORDER_TYPE.TAKEAWAY)}</div>\n            </div>\n\n            <div className=\"bg-cyan-900/30 border border-cyan-700/30 rounded-lg p-3 text-center min-w-[100px]\">\n              <div className=\"text-cyan-400 font-medium\">Delivery</div>\n              <div className=\"text-2xl font-bold text-white\">{countByType(ORDER_TYPE.DELIVERY)}</div>\n            </div>\n          </div>\n        </div>\n\n        {/* Filter Controls */}\n        <div className=\"flex justify-between items-center\">\n          <div className=\"flex space-x-2\">\n            <select\n              value={statusFilter}\n              onChange={(e) => setStatusFilter(e.target.value)}\n              className=\"bg-gray-900 border border-gray-700 rounded-lg px-3 py-2 text-sm text-white focus:ring-orange-500 focus:border-orange-500\"\n            >\n              <option value=\"active\">Active Orders</option>\n              <option value={STATUS.CONFIRMED}>Confirmed Only</option>\n              <option value={STATUS.PREPARING}>Preparing Only</option>\n              <option value={STATUS.READY_FOR_PICKUP}>Ready for Pickup Only</option>\n              <option value={STATUS.READY_FOR_DELIVERY}>Ready for Delivery Only</option>\n              <option value=\"all\">All Orders</option>\n            </select>\n          </div>\n\n          <div className=\"text-sm text-gray-400\">\n            Total: {orders.length} order{orders.length !== 1 ? 's' : ''}\n          </div>\n        </div>\n\n        {error && (\n          <div className=\"bg-red-900/40 border border-red-700 text-red-400 p-4 rounded-lg\">\n            {error}\n          </div>\n        )}\n\n        {loading ? (\n          <div className=\"flex justify-center items-center py-12\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500\"></div>\n          </div>\n        ) : orders.length === 0 ? (\n          <div className=\"bg-gray-900/50 border border-gray-800 rounded-xl p-8 text-center\">\n            <ChefHat className=\"w-16 h-16 text-gray-700 mx-auto mb-4\" />\n            <h2 className=\"text-xl font-medium text-white mb-2\">No Orders Found</h2>\n            <p className=\"text-gray-400\">\n              There are currently no orders matching your filter.\n              New orders will appear here automatically.\n            </p>\n          </div>\n        ) : (\n          <div className=\"grid grid-cols-1 xl:grid-cols-4 gap-6\">\n            {/* Column 1: Confirmed Orders */}\n            <div className=\"bg-gray-900/50 rounded-xl border border-blue-900/30 p-4\">\n              <div className=\"flex items-center mb-4 border-b border-gray-800 pb-3\">\n                <Bell className=\"w-5 h-5 text-blue-400 mr-2\" />\n                <h3 className=\"text-lg font-medium text-blue-400\">New Orders</h3>\n                <span className=\"ml-auto bg-blue-900/50 text-blue-400 text-sm px-2 py-0.5 rounded-full\">\n                  {ordersGrouped[STATUS.CONFIRMED]?.length || 0}\n                </span>\n              </div>\n\n              <div className=\"space-y-4\">\n                <AnimatePresence>\n                  {ordersGrouped[STATUS.CONFIRMED]?.map(order => (\n                    <OrderCard\n                      key={order.id}\n                      order={order}\n                      onStatusUpdate={handleStatusUpdate}\n                      onDispatchToDriver={handleDispatchToDriver}\n                      isUpdating={isUpdating}\n                      isDispatched={dispatchedOrders.has(order.id)}\n                    />\n                  ))}\n                </AnimatePresence>\n\n                {ordersGrouped[STATUS.CONFIRMED]?.length === 0 && (\n                  <div className=\"text-center py-8 text-gray-500\">\n                    <Bell className=\"w-8 h-8 text-gray-700 mx-auto mb-2\" />\n                    <p>No new orders</p>\n                  </div>\n                )}\n              </div>\n            </div>\n\n            {/* Column 2: Preparing Orders */}\n            <div className=\"bg-gray-900/50 rounded-xl border border-orange-900/30 p-4\">\n              <div className=\"flex items-center mb-4 border-b border-gray-800 pb-3\">\n                <Flame className=\"w-5 h-5 text-orange-400 mr-2\" />\n                <h3 className=\"text-lg font-medium text-orange-400\">Preparing</h3>\n                <span className=\"ml-auto bg-orange-900/50 text-orange-400 text-sm px-2 py-0.5 rounded-full\">\n                  {ordersGrouped[STATUS.PREPARING]?.length || 0}\n                </span>\n              </div>\n\n              <div className=\"space-y-4\">\n                <AnimatePresence>\n                  {ordersGrouped[STATUS.PREPARING]?.map(order => (\n                    <OrderCard\n                      key={order.id}\n                      order={order}\n                      onStatusUpdate={handleStatusUpdate}\n                      onDispatchToDriver={handleDispatchToDriver}\n                      isUpdating={isUpdating}\n                    />\n                  ))}\n                </AnimatePresence>\n\n                {ordersGrouped[STATUS.PREPARING]?.length === 0 && (\n                  <div className=\"text-center py-8 text-gray-500\">\n                    <Flame className=\"w-8 h-8 text-gray-700 mx-auto mb-2\" />\n                    <p>No orders in preparation</p>\n                  </div>\n                )}\n              </div>\n            </div>\n\n            {/* Column 3: Ready Orders (Pickup or Delivery) */}\n            <div className=\"bg-gray-900/50 rounded-xl border border-green-900/30 p-4\">\n              <div className=\"flex items-center mb-4 border-b border-gray-800 pb-3\">\n                <Package className=\"w-5 h-5 text-green-400 mr-2\" />\n                <h3 className=\"text-lg font-medium text-green-400\">Ready</h3>\n                <span className=\"ml-auto bg-green-900/50 text-green-400 text-sm px-2 py-0.5 rounded-full\">\n                  {(ordersGrouped[STATUS.READY_FOR_PICKUP]?.length || 0) +\n                   (ordersGrouped[STATUS.READY_FOR_DELIVERY]?.length || 0)}\n                </span>\n              </div>\n\n              <div className=\"space-y-4\">\n                <AnimatePresence>\n                  {/* Combine both types of ready orders */}\n                  {[\n                    ...(ordersGrouped[STATUS.READY_FOR_PICKUP] || []),\n                    ...(ordersGrouped[STATUS.READY_FOR_DELIVERY] || [])\n                  ].map(order => (\n                    <OrderCard\n                      key={order.id}\n                      order={order}\n                      onStatusUpdate={handleStatusUpdate}\n                      onDispatchToDriver={handleDispatchToDriver}\n                      isUpdating={isUpdating}\n                    />\n                  ))}\n                </AnimatePresence>\n\n                {(ordersGrouped[STATUS.READY_FOR_PICKUP]?.length || 0) +\n                 (ordersGrouped[STATUS.READY_FOR_DELIVERY]?.length || 0) === 0 && (\n                  <div className=\"text-center py-8 text-gray-500\">\n                    <Package className=\"w-8 h-8 text-gray-700 mx-auto mb-2\" />\n                    <p>No orders ready yet</p>\n                  </div>\n                )}\n              </div>\n            </div>\n\n            {/* Column 4: Completed/Archived Orders */}\n            <div className=\"bg-gray-900/50 rounded-xl border border-teal-900/30 p-4\">\n              <div className=\"flex items-center mb-4 border-b border-gray-800 pb-3\">\n                <CheckCircle className=\"w-5 h-5 text-teal-400 mr-2\" />\n                <h3 className=\"text-lg font-medium text-teal-400\">Completed</h3>\n                <span className=\"ml-auto bg-teal-900/50 text-teal-400 text-sm px-2 py-0.5 rounded-full\">\n                  {ordersGrouped[STATUS.COMPLETED]?.length || 0}\n                </span>\n              </div>\n\n              <div className=\"space-y-4\">\n                <AnimatePresence>\n                  {ordersGrouped[STATUS.COMPLETED]?.map(order => (\n                    <OrderCard\n                      key={order.id}\n                      order={order}\n                      onStatusUpdate={handleStatusUpdate}\n                      onDispatchToDriver={handleDispatchToDriver}\n                      isUpdating={isUpdating}\n                    />\n                  ))}\n                </AnimatePresence>\n\n                {ordersGrouped[STATUS.COMPLETED]?.length === 0 && (\n                  <div className=\"text-center py-8 text-gray-500\">\n                    <CheckCircle className=\"w-8 h-8 text-gray-700 mx-auto mb-2\" />\n                    <p>No completed orders</p>\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </ManagerLayout>\n  );\n};\n\nexport default ManagerPage;"}