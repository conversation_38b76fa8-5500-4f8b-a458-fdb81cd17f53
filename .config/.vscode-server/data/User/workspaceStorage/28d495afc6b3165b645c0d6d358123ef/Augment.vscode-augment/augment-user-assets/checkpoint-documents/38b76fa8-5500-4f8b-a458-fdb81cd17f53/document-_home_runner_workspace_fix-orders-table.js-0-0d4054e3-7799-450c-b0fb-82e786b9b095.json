{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "fix-orders-table.js"}, "originalCode": "// Simple script to fix the orders table by adding the missing order_details column\nimport { Pool, neonConfig } from '@neondatabase/serverless';\nimport ws from 'ws';\nimport dotenv from 'dotenv';\n\n// Configure WebSocket for Neon\nneonConfig.webSocketConstructor = ws;\n\ndotenv.config();\n\nconst connectionString = process.env.DATABASE_URL;\n\nif (!connectionString) {\n  console.error(\"DATABASE_URL must be set\");\n  process.exit(1);\n}\n\nasync function fixOrdersTable() {\n  const client = new Pool({ connectionString });\n\n  try {\n    console.log(\"Connecting to database...\");\n\n    // Add the orderDetails column\n    console.log(\"Adding order_details column...\");\n    await client.query(`ALTER TABLE orders ADD COLUMN IF NOT EXISTS order_details JSONB;`);\n\n    // Update existing orders to have default orderDetails\n    console.log(\"Setting default values for existing orders...\");\n    await client.query(`\n      UPDATE orders\n      SET order_details = '{\"type\": \"delivery\", \"time\": \"asap\", \"scheduledTime\": null}'::jsonb\n      WHERE order_details IS NULL;\n    `);\n\n    // Make the column NOT NULL after setting default values\n    console.log(\"Making order_details column NOT NULL...\");\n    await client.query(`ALTER TABLE orders ALTER COLUMN order_details SET NOT NULL;`);\n\n    console.log(\"✅ Orders table fixed successfully!\");\n\n    // Verify the fix\n    const result = await client.query(`\n      SELECT column_name, data_type, is_nullable\n      FROM information_schema.columns\n      WHERE table_name = 'orders' AND column_name = 'order_details';\n    `);\n\n    if (result.rows.length > 0) {\n      console.log(\"✅ Verification: order_details column exists:\", result.rows[0]);\n    } else {\n      console.log(\"❌ Verification failed: order_details column not found\");\n    }\n\n  } catch (error) {\n    console.error(\"❌ Error fixing orders table:\", error);\n  } finally {\n    await client.end();\n  }\n}\n\nfixOrdersTable();\n", "modifiedCode": "// Simple script to fix the orders table by adding the missing order_details column\nimport { Pool, neonConfig } from '@neondatabase/serverless';\nimport ws from 'ws';\nimport dotenv from 'dotenv';\n\n// Configure WebSocket for Neon\nneonConfig.webSocketConstructor = ws;\n\ndotenv.config();\n\nconst connectionString = process.env.DATABASE_URL;\n\nif (!connectionString) {\n  console.error(\"DATABASE_URL must be set\");\n  process.exit(1);\n}\n\nasync function fixOrdersTable() {\n  const client = new Pool({ connectionString });\n\n  try {\n    console.log(\"Connecting to database...\");\n\n    // Add the orderDetails column\n    console.log(\"Adding order_details column...\");\n    await client.query(`ALTER TABLE orders ADD COLUMN IF NOT EXISTS order_details JSONB;`);\n\n    // Update existing orders to have default orderDetails\n    console.log(\"Setting default values for existing orders...\");\n    await client.query(`\n      UPDATE orders\n      SET order_details = '{\"type\": \"delivery\", \"time\": \"asap\", \"scheduledTime\": null}'::jsonb\n      WHERE order_details IS NULL;\n    `);\n\n    // Make the column NOT NULL after setting default values\n    console.log(\"Making order_details column NOT NULL...\");\n    await client.query(`ALTER TABLE orders ALTER COLUMN order_details SET NOT NULL;`);\n\n    console.log(\"✅ Orders table fixed successfully!\");\n\n    // Verify the fix\n    const result = await client.query(`\n      SELECT column_name, data_type, is_nullable\n      FROM information_schema.columns\n      WHERE table_name = 'orders' AND column_name = 'order_details';\n    `);\n\n    if (result.rows.length > 0) {\n      console.log(\"✅ Verification: order_details column exists:\", result.rows[0]);\n    } else {\n      console.log(\"❌ Verification failed: order_details column not found\");\n    }\n\n  } catch (error) {\n    console.error(\"❌ Error fixing orders table:\", error);\n  } finally {\n    await client.end();\n  }\n}\n\nfixOrdersTable();\n"}