{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "server/db.js"}, "originalCode": "require('dotenv').config();\nconst { Pool } = require('pg');\n\n// Create a PostgreSQL connection pool\nconst pool = new Pool({\n  connectionString: process.env.DATABASE_URL,\n  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false\n});\n\n// Test database connection\npool.on('connect', () => {\n  console.log('Connected to the database');\n});\n\npool.on('error', (err) => {\n  console.error('Unexpected error on idle client', err);\n  process.exit(-1);\n});\n\nmodule.exports = {\n  query: (text, params) => pool.query(text, params)\n};", "modifiedCode": "require('dotenv').config();\nconst { Pool } = require('pg');\n\n// Create a PostgreSQL connection pool\nconst pool = new Pool({\n  connectionString: process.env.DATABASE_URL,\n  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false\n});\n\n// Test database connection\npool.on('connect', () => {\n  console.log('Connected to the database');\n});\n\npool.on('error', (err) => {\n  console.error('Unexpected error on idle client', err);\n  process.exit(-1);\n});\n\nmodule.exports = {\n  query: (text, params) => pool.query(text, params)\n};"}