{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/driver/DriverLayout.tsx"}, "originalCode": "import { ReactNode, useState } from \"react\";\nimport { useLocation, Link } from \"wouter\";\nimport { motion } from \"framer-motion\";\nimport { ShoppingBag, Home, LogOut } from \"lucide-react\";\nimport { useNotifications } from \"@/context/NotificationContext\";\nimport NotificationDropdown from \"@/components/NotificationDropdown\";\n\ninterface DriverLayoutProps {\n  children: ReactNode;\n}\n\nconst DriverLayout = ({ children }: DriverLayoutProps) => {\n  const [location] = useLocation();\n  const { unreadCount } = useNotifications();\n  const [isNotificationOpen, setIsNotificationOpen] = useState(false);\n\n  return (\n    <div className=\"min-h-screen bg-black text-white flex flex-col\">\n      {/* Header */}\n      <motion.header\n        className=\"bg-gray-900/80 border-b border-gray-800 py-4 px-6 flex justify-between items-center backdrop-blur-md\"\n        initial={{ y: -20, opacity: 0 }}\n        animate={{ y: 0, opacity: 1 }}\n        transition={{ duration: 0.3 }}\n      >\n        <div className=\"flex items-center\">\n          <ShoppingBag className=\"h-6 w-6 text-pink-500 mr-3\" />\n          <h1 className=\"text-xl font-bold bg-gradient-to-r from-pink-400 via-cyan-400 to-pink-400 text-transparent bg-clip-text\">\n            Barbecuez Driver Portal\n          </h1>\n        </div>\n\n        <div className=\"flex items-center space-x-4\">\n          <Link href=\"/\">\n            <div className=\"flex items-center text-gray-400 hover:text-white hover:bg-gray-800/50 rounded-lg px-3 py-2 cursor-pointer\">\n              <Home className=\"w-5 h-5 mr-2\" />\n              <span>Home</span>\n            </div>\n          </Link>\n\n          <Link href=\"/logout\">\n            <div className=\"flex items-center text-red-400 hover:text-white hover:bg-red-900/50 rounded-lg px-3 py-2 cursor-pointer\">\n              <LogOut className=\"w-5 h-5 mr-2\" />\n              <span>Logout</span>\n            </div>\n          </Link>\n        </div>\n      </motion.header>\n\n      {/* Main Content */}\n      <main className=\"flex-grow p-4 md:p-6\">\n        <div className=\"max-w-6xl mx-auto\">\n          {children}\n        </div>\n      </main>\n\n      {/* Footer */}\n      <footer className=\"py-4 px-6 text-center text-gray-500 text-sm\">\n        &copy; {new Date().getFullYear()} Barbecuez Restaurant Driver Portal. All rights reserved.\n      </footer>\n    </div>\n  );\n};\n\nexport default DriverLayout;", "modifiedCode": "import { ReactNode, useState } from \"react\";\nimport { useLocation, Link } from \"wouter\";\nimport { motion } from \"framer-motion\";\nimport { ShoppingBag, Home, LogOut } from \"lucide-react\";\nimport { useNotifications } from \"@/context/NotificationContext\";\nimport NotificationDropdown from \"@/components/NotificationDropdown\";\n\ninterface DriverLayoutProps {\n  children: ReactNode;\n}\n\nconst DriverLayout = ({ children }: DriverLayoutProps) => {\n  const [location] = useLocation();\n  const { unreadCount } = useNotifications();\n  const [isNotificationOpen, setIsNotificationOpen] = useState(false);\n\n  return (\n    <div className=\"min-h-screen bg-black text-white flex flex-col\">\n      {/* Header */}\n      <motion.header\n        className=\"bg-gray-900/80 border-b border-gray-800 py-4 px-6 flex justify-between items-center backdrop-blur-md\"\n        initial={{ y: -20, opacity: 0 }}\n        animate={{ y: 0, opacity: 1 }}\n        transition={{ duration: 0.3 }}\n      >\n        <div className=\"flex items-center\">\n          <ShoppingBag className=\"h-6 w-6 text-pink-500 mr-3\" />\n          <h1 className=\"text-xl font-bold bg-gradient-to-r from-pink-400 via-cyan-400 to-pink-400 text-transparent bg-clip-text\">\n            Barbecuez Driver Portal\n          </h1>\n        </div>\n\n        <div className=\"flex items-center space-x-4\">\n          <motion.div\n            className=\"relative\"\n            whileHover={{ scale: 1.05 }}\n          >\n            <button\n              onClick={() => setIsNotificationOpen(!isNotificationOpen)}\n              className=\"p-2 rounded-full bg-gray-800 text-pink-500 hover:bg-pink-900/30 border border-pink-800/50 transition-colors\"\n            >\n              <ShoppingBag className=\"w-5 h-5\" />\n            </button>\n            {unreadCount > 0 && (\n              <span className=\"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center animate-pulse\">\n                {unreadCount > 99 ? '99+' : unreadCount}\n              </span>\n            )}\n\n            <NotificationDropdown\n              isOpen={isNotificationOpen}\n              onClose={() => setIsNotificationOpen(false)}\n            />\n          </motion.div>\n\n          <Link href=\"/\">\n            <div className=\"flex items-center text-gray-400 hover:text-white hover:bg-gray-800/50 rounded-lg px-3 py-2 cursor-pointer\">\n              <Home className=\"w-5 h-5 mr-2\" />\n              <span>Home</span>\n            </div>\n          </Link>\n\n          <Link href=\"/logout\">\n            <div className=\"flex items-center text-red-400 hover:text-white hover:bg-red-900/50 rounded-lg px-3 py-2 cursor-pointer\">\n              <LogOut className=\"w-5 h-5 mr-2\" />\n              <span>Logout</span>\n            </div>\n          </Link>\n        </div>\n      </motion.header>\n\n      {/* Main Content */}\n      <main className=\"flex-grow p-4 md:p-6\">\n        <div className=\"max-w-6xl mx-auto\">\n          {children}\n        </div>\n      </main>\n\n      {/* Footer */}\n      <footer className=\"py-4 px-6 text-center text-gray-500 text-sm\">\n        &copy; {new Date().getFullYear()} Barbecuez Restaurant Driver Portal. All rights reserved.\n      </footer>\n    </div>\n  );\n};\n\nexport default DriverLayout;"}